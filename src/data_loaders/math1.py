import os
import json
import pandas as pd
from collections import Counter, defaultdict


def _prepare_math1_dataframe(file_path):
    """将math1数据转换为项目所需的DataFrame格式"""
    # 读取JSON文件
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    # 转换为DataFrame
    texts = []
    labels = []
    
    for item in data:
        texts.append(item["doc_token"])
        labels.append(item["doc_label"])
    
    df = pd.DataFrame({"text": texts, "label": labels})
    return df


def _load_math1_ontology(math1_dir):
    """加载math1数据的本体（标签集合）"""
    # 从训练数据中收集所有标签
    train_file = os.path.join(math1_dir, "train.json")
    with open(train_file, 'r') as f:
        train_data = json.load(f)
    
    # 收集所有唯一标签
    all_labels = set()
    for item in train_data:
        all_labels.update(item["doc_label"])
    
    # 转换为列表
    ontology_items = list(all_labels)
    
    # 创建先验概率（基于训练数据中的频率）
    label_counter = Counter()
    total_samples = len(train_data)
    
    for item in train_data:
        for label in item["doc_label"]:
            label_counter[label] += 1
    
    # 计算先验概率
    ontology_priors = defaultdict(
        lambda: 0.0,
        {k: v / total_samples for k, v in label_counter.items()}
    )
    
    return ontology_items, None, ontology_priors


def _load_math1():
    """加载math1数据集"""
    base_dir = "./data"
    math1_dir = os.path.join(base_dir, "math1_data")
    
    # 获取本体信息
    ontology_items, ontology_descriptions, ontology_priors = _load_math1_ontology(math1_dir)
    
    # 获取训练、验证和测试集
    train_file = os.path.join(math1_dir, "train.json")
    val_file = os.path.join(math1_dir, "val.json")
    test_file = os.path.join(math1_dir, "test.json")
    
    train_df = _prepare_math1_dataframe(train_file)
    validation_df = _prepare_math1_dataframe(val_file)
    test_df = _prepare_math1_dataframe(test_file)
    
    return validation_df, test_df, ontology_items, ontology_descriptions, ontology_priors