# ESCO_tech
python compile_irera.py --lm_config_path ./lm_config.json --retriever_model_name /home/<USER>/ZhouSQ/DCX/xmc.dspy/all-mpnet-base-v2 --dataset_name esco_tech --infer_signature_name infer_esco --rank_signature_name rank_esco --infer_student_model_name llama-2-13b-chat --infer_teacher_model_name gpt-3.5-turbo-instruct --rank_student_model_name gpt-4-1106-preview --rank_teacher_model_name gpt-4-1106-preview --infer_compile_metric_name rp10 --rank_compile_metric_name rp10 --prior_A 0 --rank_topk 50 --do_validation --do_test --prior_path ./data/esco/esco_priors.json --ontology_path ./data/esco/skills_en_label.txt --ontology_name esco --optimizer_name left-to-right

# ESCO_techwolf
python compile_irera.py --lm_config_path ./lm_config.json --retriever_model_name /home/<USER>/ZhouSQ/DCX/xmc.dspy/all-mpnet-base-v2 --dataset_name esco_techwolf --infer_signature_name infer_esco --rank_signature_name rank_esco --infer_student_model_name llama-2-13b-chat --infer_teacher_model_name gpt-3.5-turbo-instruct --rank_student_model_name gpt-4-1106-preview --rank_teacher_model_name gpt-4-1106-preview --infer_compile_metric_name rp10 --rank_compile_metric_name rp10 --prior_A 0 --rank_topk 50 --do_validation --do_test --prior_path ./data/esco/esco_priors.json --ontology_path ./data/esco/skills_en_label.txt --ontology_name esco --optimizer_name left-to-right

# ESCO_house
python compile_irera.py --lm_config_path ./lm_config.json --retriever_model_name /home/<USER>/ZhouSQ/DCX/xmc.dspy/all-mpnet-base-v2 --dataset_name esco_house --infer_signature_name infer_esco --rank_signature_name rank_esco --infer_student_model_name llama-2-13b-chat --infer_teacher_model_name gpt-3.5-turbo-instruct --rank_student_model_name gpt-4-1106-preview --rank_teacher_model_name gpt-4-1106-preview --infer_compile_metric_name rp10 --rank_compile_metric_name rp10 --prior_A 0 --rank_topk 50 --do_validation --do_test --prior_path ./data/esco/esco_priors.json --ontology_path ./data/esco/skills_en_label.txt --ontology_name esco --optimizer_name left-to-right

# BioDEX_reactions
python compile_irera.py --lm_config_path ./lm_config.json --retriever_model_name FremyCompany/BioLORD-STAMB2-v1 --dataset_name biodex_reactions --infer_signature_name infer_biodex --rank_signature_name rank_biodex --infer_student_model_name llama-2-13b-chat --infer_teacher_model_name gpt-3.5-turbo-instruct --rank_student_model_name gpt-4-1106-preview --rank_teacher_model_name gpt-4-1106-preview --infer_compile_metric_name rp10 --rank_compile_metric_name rp10 --prior_A 1000 --rank_topk 50 --do_validation --do_test --prior_path ./data/biodex/biodex_priors.json --ontology_path ./data/biodex/reaction_terms.txt --ontology_name biodex --optimizer_name left-to-right
