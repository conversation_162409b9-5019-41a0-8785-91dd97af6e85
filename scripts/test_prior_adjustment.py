#!/usr/bin/env python3
"""
测试先验概率调整功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scripts.prepare_math1_data import apply_prior_adjustment_to_scores, apply_prior_adjustment

def test_basic_functionality():
    """测试基本功能"""
    print("测试基本先验概率调整功能...")
    
    # 测试单个分数调整
    test_cases = [
        (0.5, 0.1, 1.0),    # 中等分数，高先验概率
        (0.8, 0.001, 1.0),  # 高分数，低先验概率
        (0.3, 0.05, 1.0),   # 低分数，中等先验概率
    ]
    
    print("\n单个分数调整测试:")
    print(f"{'原始分数':<10} {'先验概率':<12} {'调整后分数':<12} {'变化率':<10}")
    print("-" * 50)
    
    for original, prior, A in test_cases:
        adjusted = apply_prior_adjustment(original, prior, A)
        change_rate = ((adjusted - original) / original) * 100
        print(f"{original:<10.3f} {prior:<12.6f} {adjusted:<12.6f} {change_rate:<10.4f}%")

def test_batch_adjustment():
    """测试批量调整"""
    print("\n\n测试批量分数调整功能...")
    
    # 测试分数字典
    test_scores = {
        "小数的认识": 0.9,
        "分数的意义": 0.8,
        "计算": 0.7,
        "初识1-10": 0.6,
        "画图游戏": 0.5,
        "感知数与量": 0.4,
        "不存在的标签": 0.3  # 测试不存在的标签
    }
    
    print("批量调整测试:")
    print(f"{'标签':<20} {'原始分数':<10} {'调整后分数':<12} {'变化率':<10}")
    print("-" * 60)
    
    adjusted_scores = apply_prior_adjustment_to_scores(test_scores)
    
    for label in test_scores.keys():
        original = test_scores[label]
        adjusted = adjusted_scores[label]
        change_rate = ((adjusted - original) / original) * 100
        print(f"{label[:18]:<20} {original:<10.3f} {adjusted:<12.6f} {change_rate:<10.4f}%")

def test_different_A_values():
    """测试不同的A参数值"""
    print("\n\n测试不同A参数值的影响...")
    
    test_score = 0.5
    test_prior = 0.01
    A_values = [0.1, 0.5, 1.0, 2.0, 5.0]
    
    print(f"原始分数: {test_score}, 先验概率: {test_prior}")
    print(f"{'A参数':<8} {'调整后分数':<12} {'变化率':<10}")
    print("-" * 35)
    
    for A in A_values:
        adjusted = apply_prior_adjustment(test_score, test_prior, A)
        change_rate = ((adjusted - test_score) / test_score) * 100
        print(f"{A:<8.1f} {adjusted:<12.6f} {change_rate:<10.4f}%")

def analyze_adjustment_distribution():
    """分析调整因子的分布"""
    print("\n\n分析调整因子分布...")
    
    import json
    data_dir = "/home/<USER>/ZhouSQ/DCX/xmc.dspy/data/math1_data"
    
    # 读取调整因子
    factors_file = os.path.join(data_dir, "math1_points_adjustment_factors.json")
    with open(factors_file, 'r', encoding='utf-8') as f:
        factors = json.load(f)
    
    # 读取先验概率
    priors_file = os.path.join(data_dir, "math1_points_priors.json")
    with open(priors_file, 'r', encoding='utf-8') as f:
        priors = json.load(f)
    
    factor_values = list(factors.values())
    prior_values = list(priors.values())
    
    print(f"调整因子统计:")
    print(f"- 最小值: {min(factor_values):.8f}")
    print(f"- 最大值: {max(factor_values):.8f}")
    print(f"- 平均值: {sum(factor_values)/len(factor_values):.8f}")
    print(f"- 标准差: {(sum([(x - sum(factor_values)/len(factor_values))**2 for x in factor_values])/len(factor_values))**0.5:.8f}")
    
    print(f"\n先验概率统计:")
    print(f"- 最小值: {min(prior_values):.8f}")
    print(f"- 最大值: {max(prior_values):.8f}")
    print(f"- 平均值: {sum(prior_values)/len(prior_values):.8f}")
    
    # 找出调整效果最明显的标签
    sorted_factors = sorted(factors.items(), key=lambda x: x[1], reverse=True)
    
    print(f"\n调整效果最明显的前10个标签:")
    print(f"{'标签':<25} {'先验概率':<12} {'调整因子':<12}")
    print("-" * 55)
    
    for label, factor in sorted_factors[:10]:
        prior = priors[label]
        print(f"{label[:23]:<25} {prior:<12.6f} {factor:<12.8f}")

if __name__ == "__main__":
    print("=" * 70)
    print("先验概率调整功能测试")
    print("=" * 70)
    
    test_basic_functionality()
    test_batch_adjustment()
    test_different_A_values()
    analyze_adjustment_distribution()
    
    print("\n" + "=" * 70)
    print("测试完成！")
    print("=" * 70)
