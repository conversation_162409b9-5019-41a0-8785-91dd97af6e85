#!/bin/bash

# 使用Qwen模型训练math1数据集
python compile_irera.py \
    --lm_config_path ./lm_config.json \
    --retriever_model_name /home/<USER>/ZhouSQ/DCX/TACL_math1_qwen_lora/Qwen3-Embedding-4B \
    --dataset_name math1 \
    --infer_signature_name infer_math1 \
    --rank_signature_name rank_math1 \
    --infer_student_model_name qwen3-4b \
    --infer_teacher_model_name qwen3-4b \
    --rank_student_model_name qwen3-8b \
    --rank_teacher_model_name qwen3-8b \
    --infer_compile_metric_name rp10 \
    --rank_compile_metric_name rp10 \
    --prior_A 0 \
    --rank_topk 50 \
    --do_validation \
    --do_test \
    --prior_path ./data/math1_data/math1_priors.json \
    --ontology_path ./data/math1_data/math1_labels.txt \
    --ontology_name math1 \
    --optimizer_name left-to-right