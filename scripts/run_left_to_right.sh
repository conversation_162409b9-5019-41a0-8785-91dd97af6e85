# ESCO_tech
python run_irera.py --dataset_name esco_tech --state_path ./results_precompiled/esco_tech_infer-retrieve-rank_00/program_state.json --do_validation --do_test --lm_config_path ./lm_config.json  

# ESCO_techwolf
python run_irera.py --dataset_name esco_techwolf --state_path ./results_precompiled/esco_techwolf_infer-retrieve-rank_00/program_state.json --do_validation --do_test --lm_config_path ./lm_config.json  

# ESCO_house
python run_irera.py --dataset_name esco_house --state_path ./results_precompiled/esco_house_infer-retrieve-rank_00/program_state.json --do_validation --do_test --lm_config_path ./lm_config.json  

# BioDEX_reactions
python run_irera.py --dataset_name biodex_reactions --state_path ./results_precompiled/biodex_reactions_infer-retrieve-rank_00/program_state.json --do_validation --do_test --lm_config_path ./lm_config.json  
