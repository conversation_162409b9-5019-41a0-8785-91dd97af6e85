#!/usr/bin/env python3
"""
为math1数据集准备先验概率文件和标签文件
"""

import json
import os
from collections import Counter, defaultdict

def prepare_math1_files():
    """准备math1数据集所需的额外文件"""
    data_dir = "/home/<USER>/ZhouSQ/DCX/xmc.dspy/data/math1_data"
    
    # 读取训练数据
    with open(os.path.join(data_dir, "train.json"), 'r') as f:
        train_data = json.load(f)
    
    # 收集所有标签并计算频率
    all_labels = []
    for item in train_data:
        all_labels.extend(item["doc_label"])
    
    total_samples = len(train_data)
    label_counter = Counter(all_labels)
    
    # 计算先验概率
    priors = defaultdict(
        lambda: 0.0,
        {k: v / total_samples for k, v in label_counter.items()}
    )
    
    # 保存先验概率到文件
    with open(os.path.join(data_dir, "math1_priors.json"), 'w') as f:
        json.dump(dict(priors), f, ensure_ascii=False, indent=2)
    
    # 保存所有唯一标签到文件
    unique_labels = list(set(all_labels))
    with open(os.path.join(data_dir, "math1_labels.txt"), 'w') as f:
        for label in unique_labels:
            f.write(f"{label}\n")
    
    print(f"处理完成:")
    print(f"- 训练样本数: {total_samples}")
    print(f"- 唯一标签数: {len(unique_labels)}")
    print(f"- 总标签实例数: {len(all_labels)}")
    print(f"- 先验概率文件已保存到: {os.path.join(data_dir, 'math1_priors.json')}")
    print(f"- 标签文件已保存到: {os.path.join(data_dir, 'math1_labels.txt')}")

if __name__ == "__main__":
    prepare_math1_files()