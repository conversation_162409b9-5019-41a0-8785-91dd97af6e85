{"初识1-10": 1.000000043429446, "初识单双": 1.000000043429446, "感知数与量": 1.0000009008193804, "画图游戏": 1.000000043429446, "数与量的对应(1-5)": 1.0000036032663107, "数与量的对应(1-10)": 1.000000043429446, "大小比较": 1.000000043429446, "判断草地大小": 1.000000043429446, "比多少问题进阶": 1.000000043429446, "比多少推理": 1.000000043429446, "多少比较": 1.000000043429446, "感受数量的比较": 1.0000027024525358, "一一对应感受多少": 1.0000009008193804, "甜度比较": 1.000000043429446, "等量代换比轻重": 1.0000009008193804, "两种物品的重量比较": 1.000000043429446, "三种物品的重量比较": 1.0000009008193804, "比高矮": 1.000000043429446, "间接比长短": 1.000000043429446, "判断路线长短": 1.000000043429446, "生活中的长度": 1.000000043429446, "绳子比长短": 1.000000043429446, "数格子比长短": 1.000000043429446, "直接比长短": 1.000000043429446, "认读写数(1-5)": 1.0000018016368923, "比较大小(1-5)": 1.000008107307159, "比较综合应用(1~5)": 1.000000043429446, "不等式谜(1-5)": 1.000000043429446, "认识\">\"\"<\"和\"=\"(5以内)": 1.000000043429446, "一个数比另一个数多几或少几": 1.000000043429446, "用一一对应的方法比较大小": 1.000000043429446, "排队问题(问\"有\"或\"第\")": 1.000000043429446, "排序": 1.000048641572929, "判断事情先后顺序": 1.000000043429446, "数的顺序(1-5)": 1.0000027024525358, "0的认识": 1.0000027024525358, "6-9的认识": 1.0000108097092466, "6-9的比大小": 1.000006305696425, "6-9的读写": 1.000000043429446, "6-10的含义": 1.000000043429446, "10以内数的顺序": 1.0000108097092466, "6-10的大小关系": 1.000000043429446, "比较大小(1-10)": 1.000000043429446, "不等式谜(1-10)": 1.0000018016368923, "打包比较(一对多比较)": 1.000000043429446, "认识\">\"\"<\"和\"=\"(10以内)": 1.000000043429446, "10以内数的比大小": 1.000027023768652, "跳格子(不同方向跳格子)": 1.000000043429446, "10以内数的应用": 1.000000043429446, "6~9数的顺序": 1.000000043429446, "9以内的基数与序数": 1.0000117105062056, "排队问题单主\"第\"\"第\"问题(10以内)": 1.000000043429446, "区分\"第几\"和\"有几\"": 1.000000043429446, "区分基数与序数(10以内)": 1.000000043429446, "11-20各数的读写": 1.000006305696425, "11~19的读写和大小比较": 1.000000043429446, "计数器认数(20以内)": 1.0000018016368923, "11~19的认识和组成": 1.000000043429446, "十几就是十和几": 1.000000043429446, "11-20各数的认识": 1.0000018016368923, "不同数位上数字的含义": 1.000000043429446, "小棒摆数": 1.000000043429446, "数的组成（20以内）": 1.0000162144629754, "20以内数的比大小": 1.0000162144629754, "11-20各数的顺序": 1.0000117105062056, "区分基数与序数(11-20)": 1.0000018016368923, "分组数的应用(100以内)": 1.000000043429446, "跳格子(几个几个跳格子)": 1.000000043429446, "分组数": 1.000015313675358, "相邻的整十数(100以內)": 1.000000043429446, "根据计数器读数、写数(100以内)": 1.0000198175947606, "先写数再比较大小(100以内)": 1.000006305696425, "数的大小比较(100以内)": 1.000020718373036, "连续数(100以内)": 1.0000450386802755, "估数(100以内)": 1.0000072065027261, "描述数之间的大小关系(多得多...)(万以内)": 1.0000162144629754, "数的相对大小关系(100以内)": 1.000060350767633, "按数的特点分类(100以内)": 1.000006305696425, "由数位上数的关系写数(100以內)": 1.000063052844656, "枚举法解数的拆分": 1.000000043429446, "100以内数的组成": 1.0002872666523144, "100以内数的写法": 1.0000072065027261, "图形表示数(100以内)": 1.000000043429446, "再添多少就是100": 1.0000036032663107, "最值问题(组数)": 1.000000043429446, "百数表": 1.0000504430080472, "数数(数到1000)": 1.0000009008193804, "估计(1000以内)": 1.0000036032663107, "读数和写数(1000以内)": 1.0000144128858726, "数的组成(1000以内)": 1.000028825293442, "数数(数到10000)": 1.000008107307159, "读数和写数(10000以内)": 1.0000585493736114, "数的组成(10000以内)": 1.000030626810759, "组数问题(数字和已知)": 1.000000043429446, "组数问题(组三位数)": 1.0000126113012964, "组数问题(组四位数)": 1.00003693206252, "方框中填数字(万以内数的比大小)": 1.0000189168146167, "两数比大小(万以内)": 1.000020718373036, "多数比大小(万以内)": 1.000020718373036, "数轴上表示数(万以内)": 1.000009908910419, "求一个数接近哪个整百数或几百几十数": 1.0000018016368923, "十万的认识-数数": 1.000000043429446, "大数的写法": 1.000000043429446, "读出、写出计数器上的数(亿以内)": 1.000008107307159, "改正错误并正确读写(亿以内)": 1.000000043429446, "根据条件写数(亿以内)": 1.0000036032663107, "亿以内数的读法": 1.000009908910419, "亿以内数的写法": 1.0000054048882554, "整万数的读法和写法": 1.0000009008193804, "按要求组最大数或最小数(亿以内)": 1.0000072065027261, "十万的认识-组成": 1.0000009008193804, "数的组成(万以上)": 1.0000009008193804, "数的组成(亿以内)": 1.0000054048882554, "根据读0个数的组数问题(亿以内)": 1.0000072065027261, "读出、写出计数器上的数(亿以上)": 1.000000043429446, "亿以上数的读法": 1.0000036032663107, "亿以上数的写法": 1.000008107307159, "按要求组最大数或最小数(亿以上)": 1.0000045040782173, "根据条件写数(亿以上)": 1.0000027024525358, "根据读0个数的组数问题(亿以上)": 1.0000036032663107, "10的再认识": 1.000000043429446, "认识计数单位十万、百万、千万、亿": 1.0000018016368923, "认识十万以上的计数单位": 1.000000043429446, "认识含亿级的数位顺序表": 1.0000126113012964, "含万级数位顺序表的应用": 1.0000045040782173, "数位、数级和数位顺序表的认识": 1.0000018016368923, "十进制计数法": 1.0000090081097233, "比较大小的灵活运用(亿以内)": 1.0000036032663107, "比较大小的灵活运用(亿以上)": 1.000000043429446, "两个数比较大小(亿以内)": 1.0000090081097233, "两个数比较大小(亿以上)": 1.000000043429446, "多个大数比较大小(亿以内)": 1.0000018016368923, "多个数比较大小(亿以上)": 1.000000043429446, "感知十万的大小": 1.000000043429446, "自然数的产生": 1.000000043429446, "认识自然数": 1.0000144128858726, "认识数字": 1.0000045040782173, "以\"亿\"作单位的改写与取近似数(小数)": 1.000000043429446, "整万数的改写": 1.000028825293442, "整亿数的改写": 1.0000072065027261, "求亿以内数的近似数": 1.0000090081097233, "求亿以上数的近似数": 1.000000043429446, "区分近似数与准确数": 1.0000018016368923, "通过近似数反推原数(亿以上)": 1.000000043429446, "求万以内数的近似数": 1.000060350767633, "运用\"四舍五入\"法解决问题": 1.000000043429446, "因数的运用-": 1.0000351305713577, "因数和倍数的意义及关系": 1.0001062837907877, "找出一个数的因数": 1.0000585493736114, "运用因数解决实际问题": 1.0000576486737986, "4的倍数特征": 1.0000162144629754, "找出一个数的倍数-": 1.0000135120945186, "从条件出发解决倍数问题": 1.0000009008193804, "解决一个数的因数是另一个数的倍数的实际问题": 1.0000027024525358, "一个数的因数是另一个数的倍数": 1.0000072065027261, "运用倍数解决实际问题": 1.000009908910419, "因数与倍数综合-": 1.0000423364911701, "2、5的倍数特征": 1.000152211956364, "2的倍数特征": 1.0000189168146167, "5的倍数特征": 1.0000144128858726, "解决有关2、5的倍数的实际问题": 1.000006305696425, "解决有关2的倍数的实际问题": 1.000006305696425, "解决有关5的倍数的实际问题": 1.0000144128858726, "3的倍数特征（考法需整合）": 1.0000945758344542, "解决有关3的倍数的实际问题": 1.0000072065027261, "2、3倍数的运用": 1.000030626810759, "2、5、3倍数的运用（一）": 1.0000567479721179, "3、5倍数的运用": 1.000027023768652, "解决有关2、3的倍数的实际问题": 1.0000045040782173, "解决有关2、5、3的倍数的实际问题": 1.000015313675358, "解决有关3、5的倍数的实际问题": 1.000008107307159, "9的倍数特征": 1.000027023768652, "偶数的认识（辨析、判断、组偶数）": 1.000000043429446, "奇数的认识（辨析、判断、推理）": 1.000000043429446, "奇数与偶数的运用": 1.000028825293442, "运算结果奇偶性综合运用-": 1.0000522444356934, "差的奇偶性": 1.0000072065027261, "和的奇偶性": 1.0000540458558673, "积的奇偶性": 1.0000252222363888, "合数的认识": 1.0000135120945186, "质数的认识": 1.0000450386802755, "因、倍、质、合、奇、偶数综合运用": 1.0000360313178729, "质合综合运用（辨析、推理）": 1.000000043429446, "运用质数的特征解决组合质数的问题": 1.000027023768652, "质数与合数解决实际问题": 1.0000126113012964, "分解质因数": 1.0000108097092466, "分解质因数及其应用": 1.0000045040782173, "运用推理法解决质数积的问题": 1.0000009008193804, "公因数和最大公因数的意义": 1.000048641572929, "求两个数公因数/最大公因数的方法": 1.0000666555878683, "认识互质数": 1.0000117105062056, "用求公因数的方法解决实际问题": 1.0001513114507807, "公倍数和最小公倍数的意义": 1.0000666555878683, "求两个数的最小公倍数的方法": 1.000020718373036, "求两个数的最小公倍数的特殊情况": 1.0000504430080472, "用公倍数、最小公倍数解决复杂的实际问题": 1.0000567479721179, "用公倍数、最小公倍数解决简单的实际问题": 1.0001017807680121, "用最小公倍数解决拼瓷砖问题": 1.000008107307159, "运用最小公倍数解决植树问题": 1.000008107307159, "倍的含义": 1.0000054048882554, "解决几倍多几和少几的问题": 1.0000045040782173, "与倍相关的应用": 1.0000072065027261, "运用画图法解决倍数问题": 1.000006305696425, "小数的认识": 1.0034150597512335, "小数": 1.000000043429446, "小数的意义(两位及以上小数)": 1.000048641572929, "小数的意义(一位小数)": 1.000000043429446, "用分数和小数表示阴影部分": 1.0000990789319353, "在数线上表示小数": 1.0000531451467143, "带单位的小数比较": 1.0000729603165752, "求存期": 1.0000072065027261, "长方体棱长的认识及运用-": 1.0000441379524418, "合数的运用9": 1.0000027024525358, "质数的运用": 1.000017115248724, "哥德巴赫猜想的运用-": 1.0000108097092466, "数的认识及分类（含负数）-": 1.000021619149443, "考试成绩中的实际问题（正负数）-": 1.0000072065027261, "用正负数解决其它实际问题-": 1.0000180160326044, "加法问题结构": 1.000008107307159, "求一个数比另一个数多（少）几": 1.0000234206966525, "求比一个数多（少）几的数": 1.0000360313178729, "线段图法解决问题": 1.0000090081097233, "减法问题结构": 1.0000045040782173, "提出问题并解决问题（100以内数）": 1.0000234206966525, "税率的意义-": 1.0000072065027261, "运用乘、除法各部分间的关系巧解算式": 1.0000054048882554, "加、减、乘、除各部分间的关系的计算与应用": 1.0000324283206032, "用含有括号的四则混合运算解决实际问题": 1.0000693576256643, "直尺测量(毫米、厘米、分米)": 1.0000018016368923, "钱数问题": 1.0000180160326044, "付款与找零": 1.0000252222363888, "购物预算（钱数已知）": 1.0000189168146167, "队列问题": 1.000008107307159, "最省钱问题": 1.0000441379524418, "四个方向描述简单的行走路线": 1.000021619149443, "涂色的小正方体个数": 1.000028825293442, "运用乘法交换律和结合律解决实际问题": 1.000054946563152, "乘法运算定律的综合运用": 1.000027023768652, "乘法运算定律和除法的运算性质的综合运用": 1.0000468401303384, "运用除法的运算性质解决实际问题": 1.000045939406241, "倍数的运用-": 1.0000036032663107, "按要求分图形": 1.0000072065027261, "等分(按指定的份数平均分)": 1.00003693206252, "两位数乘两位数，积的位数": 1.000027023768652, "连乘的计算与应用": 1.0000594500715563, "连除的计算与应用": 1.000060350767633, "方向变化推理题（四个方向）": 1.0000378328052988, "方向变化推理题（八个方向）": 1.000008107307159, "填算符（除数是一位数的除法）": 1.0000009008193804, "除法意义的理解与运用（移后删）": 1.0000315275666152, "表内乘法、除法解决问题（1-6）": 1.0000108097092466, "商中间有0的一位数除法的实际问题": 1.0000414357577323, "除法计算与运用(2-6)": 1.000028825293442, "几百几十（几千几百）数除以一位数的口算运用": 1.0000108097092466, "油桶问题（除数是一位数的除法）": 1.0000072065027261, "长方体和正方体体积综合": 1.0000450386802755, "等体积变形运用（长正方体）": 1.000045939406241, "正方体底面积": 1.000008107307159, "长方体底面积": 1.0000198175947606, "正方体棱长变化的运用": 1.000028825293442, "运用观察法解决组合立体图形的问题": 1.0000072065027261, "求容积": 1.000021619149443, "长正方体拼接问题": 1.000021619149443, "长正方体拼结的表面积问题": 1.000040535022426, "长/正方体挖取部分的体积问题-（考法需重放）": 1.0000432372227401, "长方体和正方体棱长综合-": 1.0000297260530346, "灵活选择估算策略解决问题": 1.0000531451467143, "数小正方体的数量，并求添加几个小正方体后能成为大正方体": 1.000000043429446, "逻辑推理题": 1.0000027024525358, "复式统计表中开放性问题": 1.000015313675358, "质、合、奇、偶数综合运用": 1.0000567479721179, "因、倍、奇、偶数综合运用": 1.0000324283206032, "十几减9的计算方法": 1.0000054048882554, "理解加法与减法之间的互逆关系（十几减9）": 1.0000072065027261, "被减数、减数和差中的规律": 1.000006305696425, "理解加法与减法之间的互逆关系（十几减8、7、6）": 1.000008107307159, "理解加法与减法之间的互逆关系（十几减5、4、3、2）": 1.0000027024525358, "十几减5、4、3、2的实际问题": 1.000020718373036, "火柴棒游戏（20以内）": 1.0000009008193804, "十几减7、6的应用": 1.0000036032663107, "补砖块问题": 1.0000144128858726, "图形识别与匹配": 1.0000108097092466, "巧用加法运算定律和减法的运算性质": 1.0000135120945186, "求纳税额": 1.0000540458558673, "十几减8的计算方法": 1.0000054048882554, "想加算减(十几减7、6)": 1.000000043429446, "想加算减（十几减5、4、3、2）": 1.0000009008193804, "破十法(十几减5、4、3、2)": 1.0000018016368923, "平十法(十几减5、4、3、2)": 1.000000043429446, "求收入额-": 1.0000018016368923, "税率有关的其它实际问题": 1.0000108097092466, "求税后额": 1.000008107307159, "求税前额": 1.0000045040782173, "用同样的平面图形进行拼组": 1.0000234206966525, "分率、分数量比较（如1/4与1/4米）-": 1.000061251461842, "其他倍数特征": 1.0000144128858726, "图形算式问题": 1.0000090081097233, "错中求解（十几减几）": 1.0000018016368923, "公因数与最大公因数的运用-": 1.000009908910419, "公倍数与最小公倍数的运用": 1.000045939406241, "加、减、乘、除运算定律/性质的综合运用": 1.0000567479721179, "两位数乘两位数的笔算(有进位)的实际问题": 1.0000945758344542, "积末尾0的个数（两位数乘两位数笔算）": 1.0000027024525358, "两位数乘两位数的笔算(不进位)的应用": 1.0000324283206032, "整十、整百、整千数除以一位数的应用": 1.000021619149443, "两位数除以一位数的口算除法的应用": 1.0000180160326044, "自然数的运用": 1.000006305696425, "探究周长（面积）相等的图形面积（周长）变化的情况": 1.0000198175947606, "求所剩图形的面积与周长问题": 1.0000036032663107, "探究面积": 1.0000036032663107, "长方体、正方体表面积综合（考法需整合）": 1.0000117105062056, "长方体侧面积": 1.0000036032663107, "正方体侧面积": 1.0000009008193804, "运用乘法运算定律简算": 1.0000594500715563, "真、假、带分数的综合运用": 1.000064854219998, "真分数的运用": 1.0000522444356934, "假分数的运用": 1.000022519923982, "定义新运算": 1.0000126113012964, "等高等体积的圆柱和圆锥-": 1.000006305696425, "等底等体积的圆柱与圆锥-": 1.000017115248724, "等底等高的圆柱和圆锥-": 1.0000423364911701, "和公因数、公倍数有关的新定义问题": 1.0000009008193804, "用逆推法解决年龄问题": 1.0000054048882554, "等积变形问题（含圆柱）-": 1.0000144128858726, "算式比大小（十几减8）": 1.0000027024525358, "移多补少（十几减几）": 1.0000027024525358, "三位数除以一位数的笔算的应用（被除数首位能整除）": 1.000000043429446, "竖切圆锥体积的计算": 1.0000090081097233, "圆锥展开图与表面积": 1.0000009008193804, "推理前几天或后几天是几月几日": 1.0000108097092466, "在钟面上画相应的时间": 1.0000054048882554, "解决有关小数大小比较的实际问题（一位小数）": 1.000063052844656, "分段计算费用问题": 1.0000135120945186, "轴对称图形的初步应用": 1.0000189168146167, "正方体棱长的认识及运用-": 1.0000054048882554, "不计算判断结果": 1.000021619149443, "钟表上分针的旋转": 1.0000072065027261, "根据平移的方向和距离解决问题": 1.000015313675358, "运用对称解决剪纸问题": 1.000008107307159, "根据所给数字组数写满足要求的算式": 1.0000135120945186, "多位数乘一位数的错中求解问题": 1.0000027024525358, "两位数乘一位数的口算(有进位)的应用": 1.0000054048882554, "几百几十数乘一位数的口算(有进位)的应用": 1.0000126113012964, "两位数乘整百数的口算及应用": 1.000006305696425, "根据小数的意义进行单位换算(元角分/长度/质量)": 1.0000126113012964, "隐藏的差倍问题（两位数除以一位数）": 1.000008107307159, "小数与单位换算(综合)": 1.0000972776985457, "由涂色情况反求体积-": 1.0000072065027261, "加法、乘法交换律和结合律的综合应用": 1.0000090081097233, "一个数添（少）几是另一个数": 1.0000054048882554, "数的表征": 1.0000144128858726, "100以内数的读法": 1.0000036032663107, "用固定数珠子在计数器上表示数": 1.000008107307159, "几十几添上一个数是多少": 1.0000018016368923, "认识100": 1.0000072065027261, "计数器上增添珠子后表示的数": 1.0000072065027261, "数的排列规律（100以内）": 1.0000117105062056, "数的大小比较的应用": 1.0000189168146167, "加减法(整十数加一位数及相应的减法)的应用": 1.0000261230034544, "探索珠子的个数与摆出的数的个数之间的关系": 1.0000135120945186, "数的顺序（100以内）的应用": 1.000008107307159, "整十数加、减整十数的应用": 1.0000090081097233, "用数的组成解决问题": 1.000028825293442, "口算两位数加整十数(不进位)的应用": 1.000008107307159, "错中求解(乘法分配律)": 1.0000378328052988, "组数问题": 1.0000054048882554, "小数的数位顺序表与计数单位综合应用": 1.0000531451467143, "根据读0个数的组数问题(小数)": 1.0000324283206032, "重叠问题（100以内）": 1.0000027024525358, "逻辑推理": 1.0000018016368923, "利用加减混合运算解决实际问题": 1.0000036032663107, "低级单位换算高级单位": 1.0000477408525679, "高级单位换算低级单位": 1.000027023768652, "根据小数的意义进行元角分的单位换算": 1.0000108097092466, "根据小数的意义进行长度单位的换算": 1.0000324283206032, "漏看小数点写数问题": 1.0000261230034544, "小数的读法(一位小数)": 1.0000378328052988, "小数的读法(两位及以上)": 1.0001170908549581, "小数的读法和写法(两位及以上)": 1.0000261230034544, "小数的写法(两位及以上)": 1.000040535022426, "纯循环小数和混循环小数": 1.0000018016368923, "循环小数的简写": 1.0000144128858726, "循环小数的意义": 1.0000162144629754, "循环小数比大小": 1.0000351305713577, "循环小数与周期问题": 1.0000027024525358, "小数的化简": 1.0000387335462093, "小数的性质": 1.0003511779002316, "运用小数的性质解决问题": 1.0000513437228045, "小数的计数单位": 1.0001215937189933, "小数的数位顺序表": 1.000063953533261, "小数的组成": 1.0000819669131027, "按要求写数": 1.0000522444356934, "按要求写小数与组数问题": 1.0000144128858726, "小数组数问题": 1.0000234206966525, "利用小数点移动引起小数大小的变化解决实际问题": 1.000148609922828, "认识小数点移动的规律": 1.0000108097092466, "小数点向右移动与小数的大小变化(小数乘法)": 1.000000043429446, "小数点移动规律的逆用": 1.000063052844656, "小数点移动规律的正用": 1.0002278477602624, "小数的大小比较（一位小数）": 1.0000666555878683, "多个小数的大小比较": 1.0000810662618551, "多个小数的大小比较(元角分)": 1.000000043429446, "方框里填最大最小数(小数比大小)": 1.0000360313178729, "解决有关小数大小比较的实际问题": 1.0000756623151457, "两个小数的大小比较": 1.000021619149443, "小数化成分数": 1.000027924531981, "分数化成小数": 1.000065754904867, "小数的改写": 1.0000792649537567, "以\"万\"作单位的改写与取近似数(小数)": 1.000000043429446, "改写与近似数": 1.0000009008193804, "根据近似数求原数问题(小数)": 1.0001855293503756, "用\"四舍五入\"法取循环小数的近似值": 1.000000043429446, "分数的各部分名称以及读写法": 1.0000018016368923, "认识几分之一": 1.000008107307159, "平均分与分数-": 1.0000711589748554, "分数的意义": 1.000380879926812, "几分之一的应用": 1.000000043429446, "认识几分之几": 1.000021619149443, "认识一个物体的几分之几": 1.000000043429446, "认识整体的几分之几(整体是多个物体)": 1.000000043429446, "分数单位的意义": 1.0000666555878683, "单位\"1\"的意义": 1.000000043429446, "分数与除法的转化": 1.0000360313178729, "几分之一的大小比较": 1.0000090081097233, "同分母分数的大小比较": 1.0000180160326044, "分数比大小综合(同分母/同分子)": 1.000000043429446, "利用分数墙找相等的分数": 1.000000043429446, "分数墙的应用": 1.0000027024525358, "分数的大小比较": 1.0001441073388957, "根据分数大小比较解决实际问题(同分母/同分子)": 1.000000043429446, "利用分数墙比较两个数的大小": 1.000000043429446, "通分法比较分数大": 1.000000043429446, "运用分数通分比较大小来解决问题": 1.0001008801578541, "假分数的意义和特征": 1.0000252222363888, "认识带分数": 1.0000324283206032, "真分数的意义和特征": 1.000009908910419, "按要求写分数": 1.0000072065027261, "运用推理法和尝试法求分数问题": 1.0000117105062056, "带分数与假分数互化": 1.000015313675358, "假分数、整数、带分数互化": 1.0000198175947606, "分数基本性质": 1.0000945758344542, "分数的基本性质应用": 1.0000297260530346, "根据分数基本性质还原分数": 1.0000090081097233, "运用分数的基本性质进行改写": 1.0000243214674547, "约分的意义和方法": 1.0000522444356934, "通分": 1.000000043429446, "通分的意义和方法": 1.0000396342852518, "最简分数的意义": 1.0000918739535536, "比较分数与小数的大小": 1.0000810662618551, "分数与小数互化的应用": 1.0000162144629754, "倒数的概念": 1.0000090081097233, "求一个数的倒数": 1.0000117105062056, "百分数的意义": 1.0000585493736114, "百分数在具体情景中的含义": 1.0000036032663107, "成数的意义": 1.00003693206252, "求合格率（删）": 1.000000043429446, "分数与百分数": 1.000000043429446, "百分数的读法和写法": 1.0000018016368923, "含百分数的大小比较": 1.000008107307159, "百分数和分数、小数的互化": 1.0000252222363888, "百分数化分数": 1.0000036032663107, "百分数化小数": 1.0000018016368923, "分数化百分数": 1.0000018016368923, "小数化百分数": 1.0000018016368923, "温度高低的比较": 1.0000090081097233, "温度中的负数": 1.000028825293442, "0的认识和理解": 1.0000009008193804, "正负数的读法和写法": 1.000027023768652, "成绩中的负数": 1.000008107307159, "海拔高度的表示": 1.000028825293442, "楼层中的负数": 1.000006305696425, "时区时差中的负数": 1.0000090081097233, "收支中的负数": 1.0000108097092466, "位置中的负数": 1.0000234206966525, "一个数据两种理解（正负数）-": 1.000000043429446, "用正、负数表示相反意义的量-": 1.000006305696425, "数图结合解决负数的实际问题": 1.0000045040782173, "正数与负数的概念、意义": 1.0000072065027261, "质量中的负数": 1.000030626810759, "借助直线上的点体会正负数": 1.000000043429446, "用直线上的点表示正、负数": 1.000045939406241, "正、负数的大小比较": 1.000021619149443, "1-5的分与合解决实际问题": 1.0000054048882554, "1-5的分与合的简单应用": 1.000000043429446, "1-5的分与合": 1.0000018016368923, "加法的含义(1-5)": 1.000000043429446, "加法横式谜(1-5)": 1.0000009008193804, "看图列加法算式(1-5)": 1.000000043429446, "1~5的计算": 1.000000043429446, "1~5加法的含义": 1.0000009008193804, "加法计算(1-5)": 1.000008107307159, "加法算式各部分名称(1-5)": 1.000000043429446, "加法应用(1-5)": 1.0000108097092466, "减法的含义(1-5)": 1.000000043429446, "减法横式谜(1-5)": 1.000000043429446, "看图列减法算式(1-5)": 1.000000043429446, "1-5减法的含义": 1.0000027024525358, "1-5的减法": 1.000000043429446, "减法计算(1-5)": 1.0000036032663107, "减法算式各部分名称": 1.000000043429446, "减法计算的实际应用(1-5)": 1.0000036032663107, "0的加减法的实际应用": 1.000000043429446, "得数为0的减法": 1.000008107307159, "算式中有0的加减法": 1.0000054048882554, "6、7的分合的应用": 1.000000043429446, "6、7的分合与计算": 1.000000043429446, "6的加减法": 1.000000043429446, "6、7的减法的实际应用": 1.000006305696425, "6、7的加减法的计算": 1.000000043429446, "6 、7的加减法的应用": 1.000021619149443, "8、9的分合的应用": 1.0000180160326044, "8、9的分合与计算": 1.000000043429446, "8、9的加减法的计算": 1.000008107307159, "9以内的加减法应用": 1.000000043429446, "发现问题并用多种方法解决": 1.000000043429446, "8、9的加减法的应用": 1.0000180160326044, "看图列式计算(求部分)": 1.000000043429446, "求一共(1-10)": 1.000000043429446, "数字游戏(9以内)": 1.000000043429446, "图文算式(10以内)": 1.0000045040782173, "文字应用题(求部分)": 1.0000009008193804, "6-10的拆分": 1.000000043429446, "6-10的拆分(应用)": 1.000000043429446, "6-10的分与合": 1.000000043429446, "分与合提升": 1.000000043429446, "求部分(1-10)": 1.000000043429446, "数的拆分(6~10)": 1.000000043429446, "10的分与合": 1.0000144128858726, "10的计算": 1.0000090081097233, "10的加减法的应用": 1.0000126113012964, "按群计数": 1.000000043429446, "好玩的\"抢10\"": 1.000000043429446, "推算(10)": 1.000000043429446, "计算(10)": 1.000000043429446, "加法计算(6-10)": 1.000000043429446, "减法计算(6-10)": 1.000000043429446, "看图列加法算式(6-10)": 1.000000043429446, "多个数的多步运算(连加连减1-10)": 1.000000043429446, "看图列式(10以内的数)": 1.000000043429446, "连加计算(9以内)": 1.000000043429446, "连加计算(10以内)": 1.0000072065027261, "连减计算(9以内)": 1.000000043429446, "连减的应用（10以内）": 1.000009908910419, "补全问题并解答(10以内)": 1.000000043429446, "填数游戏(多步运算)": 1.000000043429446, "加减综合应用(1~5)": 1.000000043429446, "多个数的多步运算(加减混合1-10不进退位)": 1.000000043429446, "加减混合计算(10以内)": 1.000008107307159, "解决问题(列综合算式)": 1.000000043429446, "加减混合运用(10以内加减混合)": 1.0000108097092466, "摸牌游戏(10以内)": 1.000000043429446, "有隐含条件的问题": 1.000000043429446, "10以内的连加连减与加减混合": 1.000000043429446, "加法表(10以内)": 1.000000043429446, "10以内加法表": 1.000000043429446, "加法表和减法表(0-5)": 1.000000043429446, "减法表(10以内)": 1.000000043429446, "10以内减法表": 1.0000009008193804, "9以内的加、减法表": 1.000000043429446, "十几加几的不进位加法的应用": 1.0000117105062056, "十几减几的不退位减法": 1.000017115248724, "与10相关的计算": 1.0000027024525358, "不进位不退位的加减法(11~19)": 1.000006305696425, "看图列式(十几减9)": 1.0000027024525358, "平十法(十几减9)": 1.0000009008193804, "破十法(十几减9)": 1.0000018016368923, "数墙（20以内）": 1.000000043429446, "减号后面的数不变": 1.000000043429446, "平十法(十几减7、6）": 1.000000043429446, "破十法(十几减7、6）": 1.000000043429446, "解决含有多余条件的实际问题": 1.000045939406241, "求还差多少": 1.0000009008193804, "十几减8的实际问题": 1.000020718373036, "十几减9的实际问题": 1.0000252222363888, "减号前面的数不变": 1.000000043429446, "看图列式(十几减5、4、3、2)": 1.000000043429446, "十几减5、4、3、2的计算": 1.0000117105062056, "减法表(20以内退位减)": 1.000000043429446, "9加几": 1.000015313675358, "8、7、6加几": 1.0000198175947606, "8加几的进位加法": 1.0000198175947606, "7、6、5加几的进位加法": 1.000000043429446, "7、6加几": 1.000000043429446, "5、4、3、2加几": 1.0000036032663107, "加法表(20以内进位)": 1.000000043429446, "加法位置原理(20以内)": 1.000000043429446, "加法表及应用(20以内进位)": 1.000000043429446, "摸牌游戏": 1.000000043429446, "十几加几和十几减几的应用": 1.000000043429446, "猜数游戏": 1.000000043429446, "加减混合连加连减(20以内进位)": 1.0000018016368923, "加减混合连加连减(20以内退位)": 1.0000054048882554, "11~20加减法的简单应用": 1.000000043429446, "比多比少问题(20以内)": 1.000000043429446, "求补多少一样多": 1.000000043429446, "求相差几": 1.000000043429446, "加减法(整十数加一位数及相应的减法)解决实际问题": 1.0000396342852518, "口算(整十数加一位数及相应的减法)": 1.000009908910419, "整十数加、减整十数解决实际问题": 1.0000090081097233, "口算整十数加、减整十数": 1.0000027024525358, "口算两位数加一位数(不进位)": 1.000015313675358, "错题改正(两位数加一位数、整十数)": 1.0000036032663107, "口算加法的综合应用(两位数加两位数/一位数/整十数)": 1.000000043429446, "口算两位数加整十数(不进位)": 1.0000027024525358, "口算两位数减一位数(不退位)": 1.0000072065027261, "错题改正(两位数减一位数、整十数)": 1.0000009008193804, "口算两位数减整十数的应用": 1.0000243214674547, "口算两位数减整十数(不退位)": 1.0000054048882554, "笔算两位数加两位数(不进位加)": 1.0000162144629754, "口算两位数加两位数(不进位)": 1.0000054048882554, "笔算两位数减两位数(不退位减)": 1.0000027024525358, "口算两位数减两位数(不退位)": 1.0000027024525358, "口算两位数加一位数(进位)": 1.0000162144629754, "口算两位数减一位数(退位)": 1.0000126113012964, "比多少应用题(一步)(100以内)": 1.000006305696425, "笔算两位数加两位数(进位加)": 1.0000108097092466, "加法应用题(100以内)": 1.000017115248724, "口算两位数加两位数(进位加)": 1.0000027024525358, "笔算两位数减两位数(退位减)": 1.0000135120945186, "减法应用题(100以内)": 1.0000045040782173, "口算两位数减两位数(退位减)": 1.0000027024525358, "数学游戏": 1.000000043429446, "笔算(连加)": 1.0000009008193804, "连加口算的计算与应用": 1.000009908910419, "连加应用题": 1.0000045040782173, "笔算(连减)的计算与应用": 1.000000043429446, "连减解决实际问题（100以内）": 1.0000468401303384, "连减应用题": 1.000006305696425, "加减法流程图(100以内口算)": 1.000000043429446, "比多少应用题(两步)": 1.0000018016368923, "笔算(加减混合)": 1.000006305696425, "从条件出发解决有关加减法运算的实际应用题": 1.000000043429446, "加减混合应用题": 1.0000126113012964, "口算(加减混合)": 1.0000108097092466, "连续两问综合应用题": 1.0000009008193804, "列等式(100以内)": 1.000000043429446, "数墙(100以内)": 1.000000043429446, "几百几十数的加法(笔算)": 1.000009908910419, "几百几十数的加法(口算)": 1.000006305696425, "几百几十数的加法解决问题": 1.000009908910419, "几百几十数的减法(笔算)": 1.0000054048882554, "几百几十数的减法(口算)": 1.0000009008193804, "几百几十数的减法解决问题": 1.0000054048882554, "整百、整千数、几百几十数的加减法": 1.0000315275666152, "两位数加两位数的口算(不进位)": 1.0000045040782173, "两位数加两位数的口算(进位)": 1.0000117105062056, "两位数加两位数的口算的应用题": 1.0000108097092466, "两位数减两位数的口算(不退位)": 1.0000018016368923, "两位数减两位数的口算(退位)": 1.0000045040782173, "两位数减两位数的口算的应用题": 1.0000117105062056, "三位数加两、三位数(不进位)的计算": 1.000009908910419, "三位数加两、三位数(不进位)的应用": 1.0000045040782173, "三位数加两、三位数(一次进位)的计算": 1.000017115248724, "三位数加两、三位数(一次进位)的应用": 1.0000045040782173, "三位数加两、三位数(连续进位)的计算": 1.000008107307159, "三位数加两、三位数(连续进位)的应用": 1.0000036032663107, "三位数减两、三位数(不退位)的计算": 1.0000045040782173, "三位数减两、三位数(不退位)的应用": 1.0000009008193804, "三位数减两、三位数(一次退位)的计算": 1.0000108097092466, "三位数减两、三位数(一次退位)的应用": 1.0000054048882554, "三位数减两、三位数(连续退位)的计算": 1.0000027024525358, "三位数减两、三位数(连续退位)的应用": 1.0000018016368923, "三位数减两、三位数(连续退位，中间有0)的计算": 1.0000009008193804, "三位数减两、三位数(连续退位，中间有0)的应用": 1.0000009008193804, "解决起点为0的有关里程表的实际问题": 1.000000043429446, "借助直观图和线段图解决起点非0的里程问题": 1.000000043429446, "三位数加两、三位数的验算": 1.0000027024525358, "三位数减三位数的验算": 1.000006305696425, "三位数连加运算": 1.0000027024525358, "三位数连加的简单应用": 1.000006305696425, "三位数连减应用": 1.0000009008193804, "三位数连减运算": 1.000000043429446, "几百几十数的加减混合运算解决问题": 1.0000054048882554, "两位数加减混合运算应用题": 1.0000027024525358, "三位数加减混合运算": 1.000030626810759, "最大、最小的几位数问题": 1.000008107307159, "比较数的大小解决实际问题": 1.000009908910419, "珠子摆数解决实际问题": 1.0000054048882554, "摸硬币游戏": 1.0000009008193804, "组成比例（选比、补数、互倒、因数）": 1.000000043429446, "图与比例(国旗、正长三平)": 1.000000043429446, "比较小数大小的方法": 1.0001188920061745, "长度/面积/体积/容积中的负数": 1.0000009008193804, "选用一种图形作单位来测量面积": 1.000062152154183, "探索面积与周长的关系（方格纸）": 1.000006305696425, "小数比大小的应用": 1.000027924531981, "图形剪切后的面积与周长": 1.0000036032663107, "辨析情景中分数大小（单位1不同）-": 1.0000189168146167, "1平方厘米小正方形的拼接问题（无图）": 1.0000432372227401, "分数与单位换算（假/带）-": 1.0000045040782173, "分数比大小（真、假、带、整）-": 1.0000180160326044, "长（正）方形裁剪后的面积问题": 1.0000135120945186, "分数与除法关系的应用-": 1.0001513114507807, "分数量的理解（如3/4千克）-": 1.0000522444356934, "长方形与正方形互化后的面积问题": 1.0000162144629754, "真假带分数认识综合-": 1.0000846688556386, "用长（正）方形进行拼组解决面积问题": 1.0000504430080472, "单位换算（分数）-": 1.000033329072723, "长（正）方形的剪拼（涉及单位换算）": 1.0000090081097233, "长方形与正方形综合应用解决实际问题": 1.0000045040782173, "已知面积，求长（宽）或边长（涉及单位换算）": 1.000006305696425, "露出部分推整体": 1.000028825293442, "隐藏的差倍问题（三位数除以一位数）": 1.0000090081097233, "隐藏的和倍问题（两位数除以一位数）": 1.000008107307159, "隐藏的和倍问题（三位数除以一位数）": 1.0000180160326044, "两位数乘两位数估算解决实际问题": 1.000021619149443, "分数的产生": 1.0000072065027261, "统计表的选择": 1.0000045040782173, "单式统计表": 1.0000018016368923, "添加分数单位后，变成某数": 1.0000117105062056, "画平面图": 1.0000018016368923, "含字母的分数问题-": 1.000033329072723, "组数问题（除数是一位数的除法）": 1.0000126113012964, "口算两位数加两位数(不进位加)的应用": 1.0000009008193804, "两位数加一位数(不进位)的实际问题": 1.0000072065027261, "图文算式（100以内的加减法）": 1.0000117105062056, "口算两位数加整十数(不进位)解决实际问题": 1.0000117105062056, "根据条件推理比例": 1.0000108097092466, "口算两位数加一位数(不进位)的应用": 1.0000027024525358, "倒数与比例-": 1.000021619149443, "连加解决实际问题": 1.0000342298229745, "解比例在平面图形中的应用-": 1.0000045040782173, "两位数加两位数(不进位)解决实际问题": 1.000006305696425, "口算两位数加一位数(进位)的应用": 1.000021619149443, "两位数加一位数(进位)解决实际问题": 1.0000261230034544, "数轴与分数-": 1.000006305696425, "运用小数点移动规律计算": 1.0000495422914222, "用比例基本性质求值（解比例）-": 1.000027924531981, "口算两位数减整十数解决实际问题": 1.0000090081097233, "两位数减一位数（退位）的应用": 1.0000180160326044, "口算两位数减一位数(不退位)解决实际问题": 1.0000117105062056, "连减的计算与应用": 1.0000144128858726, "两位数减两位数(不退位)的应用": 1.0000045040782173, "两位数减两位数(不退位)解决实际问题": 1.0000054048882554, "口算两位数减一位数(不退位)的应用": 1.000008107307159, "两位数减两位数(退位减)的应用": 1.0000108097092466, "两位数除以一位数(被除数首位能被整除)的应用": 1.000008107307159, "加减混合运算解决实际问题（100以内）": 1.0000027024525358, "解决小数与单位换算的实际问题": 1.0000711589748554, "图文算式（万以内）": 1.0000009008193804, "两位数除以一位数的笔算(有余数)解决实际问题": 1.0000027024525358, "由表中数据探究两量的比例关系（正比例）-": 1.0000261230034544, "由表中数据探究两量的比例关系（反比例）-": 1.000060350767633, "从分数角度认识整数-": 1.000020718373036, "比例判断综合-": 1.0002098404302793, "谚语中的数学": 1.000008107307159, "七巧板的变形问题（如四巧板）": 1.0000009008193804, "同一物体多个比例尺问题（一物两图）-": 1.0000108097092466, "求实际周长、面积问题-": 1.0000351305713577, "比例尺与行程综合应用题（相遇时间、出发时间、速度差、慢车速度）-": 1.000000043429446, "求鸽巢数量-": 1.0000252222363888, "求鸽子总数-": 1.0000297260530346, "解决与小数近似数有关的实际问题": 1.0000243214674547, "把较大数改写成用\"万\"或\"亿\"作单位的数(小数)": 1.000000043429446, "方格纸上问题综合-": 1.0000009008193804, "两位数乘一位数的口算(不进位)的应用": 1.000000043429446, "不规则图形的拼接问题": 1.0000009008193804, "照样子写计算过程": 1.0000027024525358, "笔算两位数加两位数(不进位加)的应用": 1.0000090081097233, "平行底面切圆锥相关问题-": 1.0000009008193804, "笔算两位数加一位数(不进位)的应用": 1.000000043429446, "笔算两位数加两位数(进位加)（删）": 1.000000043429446, "笔算两位数加两位数(进位加)的应用": 1.0000108097092466, "笔算两位数加两位数(进位加)解决实际问题": 1.000017115248724, "两位数减两/一位数（不）退位的综合问题": 1.000000043429446, "笔算两位数减两位数(不退位减)的应用（删）": 1.000000043429446, "笔算两位数减两位数(不退位减)解决实际问题（删）": 1.000000043429446, "笔算两位数减两位数(退位减)（删）": 1.000000043429446, "笔算两位数减两位数(退位减)解决实际问题": 1.000015313675358, "被除数、除数、商之间的关系（除数是一位数）": 1.0000054048882554, "结合复式统计表提出问题": 1.0000018016368923, "笔算(连加)解决实际问题": 1.0000036032663107, "加法竖式谜（100以内）": 1.0000144128858726, "减法竖式谜（100以内）": 1.0000045040782173, "一个数比某数多（少）几，求某数": 1.0000036032663107, "用小棒摆平面图形": 1.000008107307159, "组合平面图形计数": 1.0000072065027261, "谁比谁多/少几分之几的理解-": 1.0000036032663107, "十几减几的不退位减法解决实际问题": 1.0000018016368923, "通过分数基本性质变化后比较大小-": 1.0000342298229745, "求分数与除法的实际问题（结果用假/带分数表示）-": 1.0000108097092466, "分数基本性质解决含字母的分数问题-": 1.0000036032663107, "用分数基本性质解决分数值仍不变的问题-": 1.000064854219998, "用分数基本性质推理求分数-": 1.0000072065027261, "求因数、公因数、最大公因数问题-": 1.00003693206252, "将分数化为最简分数-": 1.0000261230034544, "运用最简分数推理求值（加减的数/字母）-": 1.0000180160326044, "求最大公因数、最小公倍数-": 1.0001098861753919, "含字母的分数通分中的推理问题-": 1.000008107307159, "与最小公倍数有关的推理问题-": 1.0000234206966525, "圆柱中的排水法（多个物体）-": 1.0000045040782173, "周期问题（三位数除以一位数）": 1.0000009008193804, "长方体棱长的实际问题-": 1.0000009008193804, "用反比例解决行程问题-": 1.000008107307159, "7-9连减与除法": 1.0000108097092466, "正比例在行程中的应用（路程、时间、辨析、能否到）": 1.000000043429446, "由实际/图上长度，求体积问题-": 1.0000018016368923, "由题中数据探究比例关系（正比例）-": 1.0000072065027261, "积末尾0的个数（口算）": 1.000021619149443, "两位数乘两位数积的规律": 1.0000036032663107, "两位数乘两位数的简便运算": 1.0000126113012964, "含字母的分数运算中的推理问题-": 1.000009908910419, "复杂情况计算比例尺-": 1.0000009008193804, "优惠问题（两位数乘两位数）": 1.0000054048882554, "比例尺、图上距离、实际距离综合-": 1.0000090081097233, "由题中数据探究比例关系（反比例）-": 1.0000018016368923, "表中两量的不同比例关系-": 1.0000072065027261, "由图上/实际比，求实际/图上比-": 1.0000027024525358, "比例尺更该后图上/实际距离的计算-": 1.0000045040782173, "比例尺与百分数（复印问题）-": 1.0000054048882554, "三位数除以一位数的应用（有余数）": 1.0000054048882554, "用所给数字组最简分数-": 1.0000009008193804, "分数与小数互化（计算）-": 1.0000675562690016, "两、三位数乘一位数的笔算与应用(综合)": 1.000000043429446, "用所给数字组真、假、带分数-": 1.0000045040782173, "运用分数约分比大小解决实际问题-": 1.0000027024525358, "用所给数字组相等的分数-": 1.0000018016368923, "约分后比较分数大小-": 1.0000090081097233, "与公因数/最大公因数有关的推理-": 1.0000072065027261, "错中求解推理分数-": 1.0000027024525358, "分数、小数综合推理问题-": 1.000006305696425, "平移、旋转在拼图中的应用-": 1.000009908910419, "分数与数轴-": 1.0000036032663107, "涂色/画图理解分数基本性质的变式-": 1.0000027024525358, "含字母的公因数、最大公因数问题-": 1.0000036032663107, "结果需约分的实际应用-": 1.000015313675358, "倍数、公倍数、最小公倍数-": 1.0000135120945186, "含字母的最大公因数、最小公倍数推理问题-": 1.0000045040782173, "与最大公因数、最小公倍数有关的推理问题-": 1.0000072065027261, "旋转的特征": 1.0000108097092466, "注水中的折线图问题-": 1.0000036032663107, "找次品的方法-": 1.0000135120945186, "已知次品轻/重找次品（多个次品）": 1.000008107307159, "推理解决与正负数有关的复杂问题-": 1.0000018016368923, "成数的运用-": 1.000000043429446, "利润和成数综合求定价-": 1.0000009008193804, "笔算两位数加一位数(进位)": 1.000000043429446, "笔算两位数加一位数(进位)的应用": 1.0000018016368923, "求免税额度-": 1.0000009008193804, "贷款利息-": 1.000000043429446, "先打折后提价-": 1.0000045040782173, "先提价后打折-": 1.0000045040782173, "求促销中的折扣-": 1.0000045040782173, "不等式横式谜（100以内加减法）": 1.0000027024525358, "利率、税率综合问题-": 1.0000009008193804, "笔算两位数减一位数(退位)": 1.0000018016368923, "圆柱的认识-": 1.0000144128858726, "移多补少(100以内退位)": 1.0000027024525358, "圆柱配底问题-": 1.0000036032663107, "长/正方形卷成圆柱问题（涉及底面信息、高）": 1.0000036032663107, "长/正方形旋转成圆柱问题（求底面量、高）": 1.0000027024525358, "等式横式谜（100以内笔算加减法）": 1.000006305696425, "笔算(连减)解决实际问题": 1.0000045040782173, "认识圆柱的容积、体积": 1.0000018016368923, "圆锥的侧面积-": 1.000000043429446, "圆锥体积的变化-": 1.000006305696425, "图文算式（竖式计算）": 1.000006305696425, "瓶中水/沙（含圆锥的正倒放）-": 1.000008107307159, "三角形旋转的体积问题（含圆锥）-": 1.0000135120945186, "组数问题（100以内加减法）": 1.0000036032663107, "找规律填数（两位数加减两位数）": 1.0000009008193804, "推理求最简分数-": 1.0000351305713577, "推理求原分数-": 1.0000243214674547, "横式谜（除数是一位数的除法）": 1.0000027024525358, "推理法求带分数-": 1.000008107307159, "推理法求假分数-": 1.0000018016368923, "与平年和闰年有关的实际问题": 1.0000441379524418, "蜗牛爬井": 1.0000009008193804, "已知经过时间，求开始（结束）时刻": 1.000093675209355, "长方体切/拼正方体的棱长问题-": 1.000000043429446, "正方体切/拼为长方体棱长问题-": 1.000000043429446, "古代计时方法与现代时间对照": 1.0000036032663107, "常见时间单位": 1.000008107307159, "与星期有关的换算": 1.0000090081097233, "数线上的小数（一位小数）": 1.000020718373036, "用分数和小数表示阴影部分（一位小数）": 1.0000324283206032, "与最大/小因数、最大/小倍数有关的问题-": 1.0000162144629754, "猜猜我是谁（一位小数）": 1.0000018016368923, "两数之间的小数": 1.000020718373036, "数轴上表示数(1000以内)": 1.0000090081097233, "填数问题（一位小数）": 1.0000162144629754, "根据条件写数(1000以内)": 1.000008107307159, "由算式填合适的数（一位小数）": 1.0000009008193804, "比大小（一位小数加减法）": 1.0000009008193804, "根据条件写数(10000以内)": 1.000008107307159, "小数加减法的简算（一位小数）": 1.0000009008193804, "加减算式中某部分的变化问题": 1.0000108097092466, "小数加、减法混合运算解决问题（一位小数）": 1.000022519923982, "学科情境中的搭配问题": 1.0000342298229745, "长正方体挖洞的表面积问题-": 1.0000072065027261, "较复杂的取币值问题": 1.0000243214674547, "高变化有关的长、正方体表面积计算-": 1.0000009008193804, "高变化有关的长、正方体体积计算-": 1.0000072065027261, "小小设计师": 1.0000198175947606, "瓶中水（长、正方体）-": 1.0000189168146167, "长方体体积的复杂综合应用-": 1.0000018016368923, "根据除余关系求除数": 1.0000180160326044, "利用数量关系的比较解决问题": 1.0000045040782173, "人民币的面额的应用": 1.0000117105062056, "人民币中的移多补少": 1.0000018016368923, "同分母分数连减在实际生活中的应用-": 1.0000180160326044, "分数加减混合运算的运算顺序及应用-": 1.0000117105062056, "异分母分数连加在实际生活中的应用-": 1.00003693206252, "含字母的同分母分数加减推理问题-": 1.0000054048882554, "分数加减中的错中求解-": 1.0000036032663107, "铺砖问题（涉及单位换算）": 1.0000693576256643, "异分母分数连减在实际生活中的应用-": 1.000065754904867, "同分母分数连加在实际生活中的应用-": 1.0000072065027261, "挖空（粉刷）问题": 1.0000135120945186, "同分母分数加法的计算、运用-": 1.0000477408525679, "同分母分数减法的计算、运用-": 1.0000162144629754, "异分母分数加法的计算、运用-": 1.0000585493736114, "异分母分数减法的计算、运用-": 1.0000252222363888, "改变正方形的边长，求周长的变化量": 1.000000043429446, "异分母分数连加计算-": 1.000017115248724, "异分母分数连减计算-": 1.0000027024525358, "异分母分数加法在实际生活的应用-": 1.0000315275666152, "同分母分数减法在实际生活的应用-": 1.000015313675358, "差不变原理计算面积": 1.0000018016368923, "推理第几天是几月几日": 1.0000036032663107, "年、月、日的应用": 1.0000036032663107, "计算经过的年数": 1.0000234206966525, "单式折线、单式条形统计图对比-": 1.0000054048882554, "与小时有关的单位换算": 1.0000054048882554, "数学中的语文（与时间有关）": 1.000006305696425, "钟表快慢与时间计算": 1.0000027024525358, "敲钟问题中的推理": 1.0000009008193804, "高度中的负数-": 1.000000043429446, "水位中的负数-": 1.000000043429446, "植树问题之量体温问题-": 1.0000009008193804, "根据日历表解决问题": 1.0000072065027261, "组成比例（购物、过隧道）-": 1.000000043429446, "看图列式（谁比谁多/少几）": 1.0000054048882554, "看图列式（知多/少几）": 1.0000045040782173, "结合题意编题/故事": 1.0000027024525358, "和差问题（100以内数）": 1.0000027024525358, "正比例在工程问题中的应用-": 1.000006305696425, "削最大正方体的体积问题-": 1.000015313675358, "长/正方体切割的体积问题-": 1.0000135120945186, "不规则几何体体积（可分割为长正方体）-": 1.0000009008193804, "小数大小比较的应用（一位小数）": 1.0000090081097233, "一位小数加、减法的应用": 1.000027023768652, "列方程解含1个未知数的工程问题-": 1.0000072065027261, "平行四边形切拼成长方形": 1.000000043429446, "提出问题并解答（一位小数的加减运算）": 1.000008107307159, "含字母的分数与整数相乘的推理": 1.0000027024525358, "含字母的分数比大小-": 1.0000027024525358, "比例尺与行程综合应用题（车费、行驶时间、行驶速度）-": 1.000000043429446, "小数加、减法解决较复杂购物问题": 1.000020718373036, "小数加减法的运用": 1.0000315275666152, "一位小数加减法的应用（有实际情境）": 1.0000072065027261, "小数连加解决实际问题（一位小数）": 1.0000252222363888, "小数连减解决实际问题（一位小数）": 1.000015313675358, "已知两量比，求两量关系-": 1.0000054048882554, "图形的运动": 1.000000043429446, "含百分数的估算问题-": 1.000000043429446, "圆柱体积有关的复杂应用（两容器）-": 1.0000009008193804, "由物品填合适的人民币单位": 1.0000198175947606, "认识常见的人民币": 1.0000072065027261, "小数加减混合简算": 1.0000036032663107, "求与圆有关的阴影部分面积-": 1.000006305696425, "错中求解（分数乘除）-": 1.0000009008193804, "有优惠的购物问题-": 1.000000043429446, "用字母表示平面图形中的量-": 1.0000036032663107, "看线段图列方程-": 1.0000009008193804, "铺砖问题（不涉及单位换算）": 1.000009908910419, "由展开图信息求圆锥体积-": 1.000000043429446, "直角梯形旋转的体积问题（含圆锥）-": 1.000008107307159, "水中浸物问题（长/正方体中放圆锥）-": 1.000000043429446, "加法算式改写为乘法算式": 1.000017115248724, "看图列连加的算式": 1.000000043429446, "用乘法的意义改写算式(2-6)": 1.0000009008193804, "初步认识乘法的意义": 1.000000043429446, "乘法算式的读法及各部分名称": 1.0000036032663107, "根据文字列乘法算式(初步认识乘法)": 1.000000043429446, "看图写/找乘法算式(初步认识乘法)": 1.000000043429446, "用乘法的意义改写算式(7-9)": 1.0000018016368923, "用点子图表示乘法": 1.0000009008193804, "图文算式(2-5)": 1.0000009008193804, "5的乘法口诀应用题": 1.0000180160326044, "5的乘法口诀理解": 1.000000043429446, "巩固2、5乘法口诀": 1.000000043429446, "看图写算式(5和2)": 1.000000043429446, "2-5的乘法应用题": 1.0000072065027261, "填数游戏(2-6的乘法口诀)": 1.000000043429446, "2的乘法口诀的应用": 1.000000043429446, "编制2的乘法口诀(2x9)": 1.000000043429446, "3的乘法口诀的应用": 1.000000043429446, "编制3的乘法口诀": 1.000000043429446, "2-4乘法口诀的应用题-": 1.0000315275666152, "4的乘法口诀的应用": 1.000000043429446, "2-4的乘法口诀理解": 1.000000043429446, "编制2-4的乘法口诀-": 1.000000043429446, "6的乘法口诀的理解": 1.000000043429446, "2-6的乘法应用题": 1.0000018016368923, "2-6乘法口诀的计算-": 1.000000043429446, "有趣的乘法(表内2-6)": 1.0000009008193804, "7的乘法应用题（旧版）": 1.000000043429446, "7的乘法口诀（旧版）": 1.000000043429446, "8的乘法应用题（旧版）": 1.000000043429446, "8的乘法口诀（旧版）": 1.000000043429446, "9的乘法口诀（旧版）": 1.000000043429446, "积的特征(9的乘法)（旧版）": 1.000000043429446, "乘加、乘减(表内)": 1.0000009008193804, "乘加乘减应用题(表内混合运算)": 1.0000450386802755, "2-5的乘加乘减应用题": 1.000017115248724, "表内乘加乘减应用题": 1.000008107307159, "9的乘加乘减应用题": 1.000021619149443, "简单乘法应用题(5和2)": 1.000000043429446, "2-5的乘加、乘减计算": 1.0000054048882554, "乘法表的应用": 1.000000043429446, "找规律(表内乘法)": 1.0000018016368923, "除法的计算与运用(7、8、9)（旧版）": 1.000000043429446, "填乘法表(1-6)": 1.000000043429446, "乘法口诀表": 1.0000027024525358, "包含分(按每几个一份平均分)": 1.000040535022426, "大数目物品列表平均分": 1.000022519923982, "连减与除法(2~5)": 1.000000043429446, "拼图形与除法": 1.000000043429446, "平均分的综合理解与应用": 1.0000198175947606, "理解平均分（旧版）": 1.000000043429446, "有隐藏条件的除法应用题(2-6)": 1.0000324283206032, "初步认识除法": 1.00003693206252, "连减与除法(2-6)": 1.0000162144629754, "连减与除法(2-5)": 1.000000043429446, "除法的计算(2~5)": 1.000000043429446, "除法应用题(2~5)": 1.000000043429446, "用乘法口诀求商(2-6)": 1.0000711589748554, "图文算式(2-6)": 1.0000027024525358, "只能算一个除法算式的口诀(2-6)": 1.0000018016368923, "用7的乘法口诀求商（旧版）": 1.000000043429446, "用8的乘法口诀求商（旧版）": 1.000000043429446, "用9的乘法口诀求商（旧版）": 1.000000043429446, "用乘法口诀求商(7、8、9)（旧版）": 1.000000043429446, "还原问题的\"火车图\"": 1.000000043429446, "简单的还原问题(倒推型)": 1.000000043429446, "简单的还原问题(一半还多/少)": 1.0000009008193804, "简单的还原问题(一半型)": 1.0000018016368923, "根据乘法口诀求商(6-9)（旧版）": 1.000000043429446, "连乘、连除和乘除混合运算": 1.000000043429446, "连乘、连除和乘除混合运算的应用": 1.000000043429446, "乘除法应用题(7的口诀)": 1.000000043429446, "乘除法的应用题(表内)": 1.0000423364911701, "乘除应用题综合(2-5)": 1.000000043429446, "除法应用题(7、8、9)": 1.000087370781365, "填数游戏(表内)": 1.000000043429446, "有多余条件的除法应用题(1-9)": 1.000000043429446, "有隐藏条件的除法应用题(1-9)": 1.0000090081097233, "乘除法应用题(8的口诀)": 1.000000043429446, "乘除法的计算与应用(2-6)": 1.0000495422914222, "列多个算式的应用题（2-6含除法）-": 1.000055847268569, "除法应用题(2-6)（旧版）": 1.000000043429446, "购物方案(表内)": 1.0000009008193804, "有余数除法的计算与运用": 1.000048641572929, "认识余数": 1.0000432372227401, "有余数除法竖式": 1.0000513437228045, "余数与除数的关系": 1.000208039656215, "根据除余关系求被除数": 1.0000378328052988, "根据除余关系求除数和被除数": 1.0000072065027261, "根据除余关系求余数": 1.0000351305713577, "根据除余关系求余数和被除数": 1.000008107307159, "表内除法竖式": 1.0000018016368923, "除法竖式的计算-试商法": 1.000022519923982, "等余问题": 1.000017115248724, "有余数除法的应用题": 1.0000747616508234, "运用三位数除以一位数的笔算解决问题（有余数）": 1.000008107307159, "两位数除以一位数(被除数首位能整除)有余数的笔算": 1.000000043429446, "两位数除以一位数(被除数首位能整除)有余数的实际应用题": 1.000000043429446, "两位数除以一位数(有余数)的笔算": 1.0000018016368923, "两位数除以一位数的笔算(有余数)的应用": 1.0000009008193804, "三位数除以一位数的笔算（有余数）": 1.000000043429446, "除余关系的应用（两、三位数除以一位数）": 1.0000513437228045, "复杂周期问题": 1.000000043429446, "简单周期问题": 1.0000846688556386, "解决周期相关的实际问题": 1.000000043429446, "运用口算乘法解决实际问题": 1.0000126113012964, "运用口算来法解决问题": 1.000000043429446, "整十、整百、整千数乘一位数的口算": 1.0000126113012964, "解决两位数乘一位数的实际问题": 1.0000009008193804, "两位数乘一位数的口算乘法": 1.000000043429446, "两位数乘一位数的口算(不进位)": 1.000000043429446, "两位数乘一位数的口算(有进位)": 1.0000477408525679, "解决两、三位数乘一位数(不进位)的实际问题": 1.000000043429446, "两、三位数乘一位数(不进位)的笔算": 1.000000043429446, "解决两、三位数乘一位数(不连续进位)的实际问题": 1.000000043429446, "两、三位数乘一位数(不连续进位)的笔算": 1.000000043429446, "解决两、三位数乘一位数(连续进位)的实际问题": 1.000000043429446, "解决三位数乘一位数(乘数末尾有0)的实际问题": 1.000000043429446, "两、三位数乘一位数(连续进位)的笔算": 1.000000043429446, "有关两位数乘一位数(连续进位)的实际应用": 1.000000043429446, "有关三位数乘一位数(连续进位)的实际应用": 1.000009908910419, "几百几十数乘一位数的口算的实际问题": 1.000022519923982, "两位数乘一位数的口算的实际问题": 1.000028825293442, "几百几十数乘一位数的口算(有进位)": 1.0000297260530346, "0的相关乘法计算": 1.0000036032663107, "解决三位数乘一位数(乘数中间有0)的实际问题": 1.000000043429446, "三位数乘一位数(乘数末尾有0)的笔算": 1.000000043429446, "三位数乘一位数(乘数中间有0)的笔算": 1.000000043429446, "判断积的末尾0的个数(三位数乘两位数)": 1.0000018016368923, "积末尾或中间0的个数的问题": 1.000008107307159, "连乘运算": 1.000000043429446, "关于整十、整百、整千数除以一位数末尾0的个数": 1.000000043429446, "整十、整百、整千数除以一位数的口算": 1.0000495422914222, "整十、整百、整千数除以一位数的实际问题": 1.0000351305713577, "关于几百几十数(几千几百数)除以一位数末尾0的个数": 1.0000018016368923, "几百几十数除以一位数的实际应用题": 1.000000043429446, "几百几十（几千几百）数除以一位数的口算": 1.0000468401303384, "几百几十（几千几百）数除以一位数的口算解决实际问题": 1.0000504430080472, "整十数、整百数除以一位数的实际应用题": 1.000000043429446, "两位数除以一位数(每一位都能整除)的笔算": 1.000000043429446, "两位数除以一位数的口算": 1.0000243214674547, "两位数除以一位数的验算": 1.000000043429446, "运用两位数除以一位数的笔算(每一位都能整除)解决问题": 1.000000043429446, "两位数除以一位数的口算除法的实际问题": 1.0000378328052988, "两位数除以一位数的笔算(被除数首位能被整除)": 1.0000387335462093, "两位数除以一位数(被除数首位不能被整除)解决实际问题": 1.00003693206252, "两位数除以一位数(被除数首位不能整除)的应用": 1.0000117105062056, "两位数除以一位数的笔算(被除数首位不能被整除)": 1.0000180160326044, "运用三位数除以一位数的笔算(每一位都能整除)解决问题": 1.0000045040782173, "三位数除以一位数的笔算(被除数首位能整除)": 1.0000252222363888, "三位数除以一位数(每一位都能整除)的应用": 1.0000045040782173, "三位数除以一位数(每一位都能整除)的笔算": 1.0000117105062056, "三位数除以一位数(最高位不能整除)的笔算的应用": 1.0000126113012964, "三位数除以一位数(最高位不能整除)的笔算": 1.000027924531981, "运用三位数除以一位数的笔算(最高位不能整除)解决实际问题": 1.0000180160326044, "三位数除以一位数的笔算的实际问题（被除数首位能整除）": 1.0000144128858726, "三位数除以一位数的应用(商是两位数)": 1.0000351305713577, "三位数除以一位数的笔算(商是两位数)": 1.000028825293442, "运用三位数除以一位数的笔算解决实际问题(商是两位数)": 1.000062152154183, "0除以一个数(不是0)": 1.0000198175947606, "商中间有0的一位数除法(被除数中间有0)": 1.000022519923982, "商末尾有0的一位数除法的应用(被除数末尾有0)": 1.000028825293442, "商末尾有0的一位数除法(被除数末尾有0)": 1.0000144128858726, "根据商末尾0的情况填数": 1.0000414357577323, "商末尾有0的一位数除法(有余数)": 1.0000072065027261, "商末尾有0的一位数除法的实际问题": 1.0000180160326044, "商中间有0的一位数除法(被除数中间没有0)": 1.000027924531981, "商中间有0的一位数除法的应用": 1.000068456948267, "不计算判断商是几位数（除数是一位数）": 1.000064854219998, "用乘法和除法两步计算解决问题": 1.000022519923982, "运用乘除混合运算解决问题": 1.0000072065027261, "两位数乘整十（百）数的口算": 1.000048641572929, "两位数乘整十数的应用": 1.0000450386802755, "运用两位数乘整十（百）数的口算解决实际问题": 1.0000801656087397, "两位数乘两位数的笔算(不进位)": 1.0000846688556386, "两位数乘两位数的笔算(不进位)的实际问题": 1.0000540458558673, "两位数乘两位数的笔算(有进位)": 1.0001071843897402, "两位数乘两位数的笔算(有进位)的应用": 1.0000531451467143, "用连乘解决实际问题": 1.0001432068165075, "连乘运算应用": 1.0000009008193804, "三位数乘两位数的口算(几百几十数)": 1.0000117105062056, "整百数乘整十数的口算": 1.0000009008193804, "几百几十数乘整十数(不进位)的口算": 1.000000043429446, "几百几十数乘整十数的口算": 1.0000054048882554, "三位数乘两位数笔算": 1.0000297260530346, "三位数乘两位数积的位数问题": 1.0000036032663107, "三位数乘两位数(末尾有0的乘法)": 1.0000027024525358, "解决三位数乘两位数(因数中间有0)实际问题": 1.0000072065027261, "三位数乘两位数(中间有0的乘法)": 1.000000043429446, "乘法解决实际问题": 1.000000043429446, "解决三位数乘两位数(因数末尾有0)实际问题": 1.0000054048882554, "解决三位数乘两位数(因数中无0)的实际问题": 1.0000045040782173, "三位数乘两位数口算解决实际问题": 1.0000045040782173, "口算几百几十数除以整十数": 1.0000036032663107, "口算除数是整十数的除法": 1.0000009008193804, "用除数是整十数的除法解决简单的实际问题": 1.0000018016368923, "口算整百数除以整十数": 1.0000018016368923, "口算整十数除以整十数": 1.0000036032663107, "几百几十数除以整十数的笔算方法": 1.000000043429446, "两位数除以整十数的笔算除法": 1.0000036032663107, "三位数除以整十数的笔算除法(商是两位数)": 1.000000043429446, "三位数除以整十数商是两位数的笔算除法": 1.000000043429446, "三位数除以整十数商是一位数的笔算除法": 1.0000018016368923, "用三位数除以整十数解决简单的实际问题": 1.000006305696425, "判断商是几位数(除数是整十数)": 1.000000043429446, "被除数的最值问题(三位数除以两位数的有余数除法)": 1.000000043429446, "除数是两位数除法的实际应用": 1.000000043429446, "三位数除以两位数(商是两位数)的笔算除法": 1.0000072065027261, "三位数除以两位数的笔算": 1.0000018016368923, "商的个位是0的笔算除法": 1.000000043429446, "用三位数除以两位数(商是两位数)解决简单的实际问题": 1.0000423364911701, "判断商是几位数(除数是两位数)": 1.0000144128858726, "除数不接近整十数的试商": 1.0000054048882554, "除数接近整十数的笔算除法解决问题(用\"四舍\"法试商)": 1.000000043429446, "除数接近整十数的笔算除法解决问题(用\"五入\"法试商)": 1.000000043429446, "除数接近整十数的笔算方法(用\"四舍\"法试商)": 1.000000043429446, "除数接近整十数的笔算方法(用\"五入\"法试商)": 1.000000043429446, "试商问题综合(商是一位数)": 1.0000045040782173, "两位数除以两位数-试商": 1.000000043429446, "三位数除以两位数(商是一位数)-试商": 1.000000043429446, "被除数和除数末尾都有0的除法": 1.000000043429446, "用连除解决实际问题": 1.0002170434518702, "用连除的知识简算(除数是两位数)": 1.0000027024525358, "用乘除混合解决实际问题": 1.0000594500715563, "除加、除减(表内)": 1.000000043429446, "除加除减应用题(表内混合运算)": 1.0000477408525679, "根据分步运算列综合算式(含括号)(表内混合运算)": 1.0000144128858726, "优惠策略": 1.000027924531981, "含有小括号的四则混合运算": 1.0000180160326044, "够不够问题": 1.0000090081097233, "含有括号的小数混合运算": 1.0000045040782173, "看图列综合算式并计算": 1.0000090081097233, "用两步计算解决问题(表内混合运算)(不含括号)": 1.000000043429446, "求中间量比多少": 1.000000043429446, "小括号对运算结果的影响": 1.0000018016368923, "用含有括号的四则混合运算解决常见数学问题": 1.0000342298229745, "含有中括号的四则混合运算": 1.000008107307159, "四则运算中的错中求解": 1.0000144128858726, "有括号的四则混合运算": 1.0000414357577323, "将分步算式改写成带小括号或中括号的综合算式": 1.0000693576256643, "24点游戏": 1.000015313675358, "估算(100以内)": 1.0000072065027261, "估算解决问题(100以内)": 1.0000045040782173, "估算够不够问题(整百、整千数加减)": 1.0000108097092466, "几百几十数加、减几百几十数的估算计算": 1.0000315275666152, "整十、整百和整千数的加减法估算": 1.000008107307159, "用整百数或几百几十数的估算解决问题": 1.0000252222363888, "两、三位数乘一位数的估算": 1.0000126113012964, "两、三位数乘一位数的估算应用": 1.0000144128858726, "运用估算判断积的可能性": 1.000006305696425, "两位数乘两位数的估算": 1.0002539570625806, "运用推理法解决有关两位数乘两位数的填数问题": 1.000028825293442, "部分估计整体法估算": 1.000000043429446, "基准数法估算": 1.000000043429446, "三位数乘两位数的估算": 1.0000252222363888, "除数是一位数的估算": 1.0002629599063362, "运用除数是一位数的除法估算解决问题": 1.0000837682099946, "两位数除两位数的估算方法": 1.0000036032663107, "两位数除三位数的估算方法": 1.0000045040782173, "小数的不进位加法和不退位减法(一位小数)": 1.000000043429446, "一位小数加法的计算(不进位)": 1.0000261230034544, "一位小数减法的计算(不退位)": 1.0000018016368923, "一位小数加法的计算(进位)": 1.0000441379524418, "一位小数减法的计算(退位)": 1.0000018016368923, "小数加法(小数部分位数不同)": 1.0000036032663107, "小数加法(小数部分位数相同)": 1.000009908910419, "小数减法(小数部分位数不同)": 1.0000072065027261, "小数减法(小数部分位数相同)": 1.0000045040782173, "利用小数加减法解决综合问题": 1.0001459083780704, "小数连加应用题": 1.0000162144629754, "小数连减应用题": 1.0000234206966525, "运用一位小数加法解决问题": 1.0000765629776, "运用一位小数减法解决问题": 1.0000576486737986, "包含整数的小数加减法": 1.000008107307159, "小数的加减混合运算": 1.0000675562690016, "小数加减法简算综合": 1.0000900726902813, "小数加、减法解决较复杂的实际问题（一位小数）": 1.0000297260530346, "小数加减法应用题": 1.00024315340372, "用计算器计算小数加、减法": 1.0000108097092466, "小数乘整数的实际应用": 1.0000297260530346, "小数乘整数计算": 1.0000162144629754, "应用小数乘整数进行单位换算": 1.0000036032663107, "用逆推法解决小数乘整数应用题": 1.0000018016368923, "小数乘法中倍的应用": 1.0000126113012964, "小数乘小数的计算-": 1.000000043429446, "小数乘小数的算理、算法": 1.000006305696425, "小数乘小数解决实际问题": 1.0000126113012964, "小数点的位置（小数乘小数）": 1.000000043429446, "积与因数的大小关系（小数乘法）": 1.000017115248724, "根据积的近似值反求原数": 1.000008107307159, "积的变化规律（小数乘小数）-": 1.000000043429446, "小数除法商中间有0": 1.000000043429446, "小数的整数部分能被整除": 1.000000043429446, "整数除以整数商是小数的除法9": 1.0000009008193804, "除数是整数的小数除法实际问题9": 1.0000162144629754, "小数点向左移动与小数的大小变化(小数除法)": 1.0000018016368923, "小数运算解决和差倍问题": 1.0000072065027261, "除数是小数的基本算理及算法": 1.0000027024525358, "判断商与1的大小关系（除数是小数）": 1.0000027024525358, "有余数的小数除法（除数是小数）": 1.000000043429446, "除数是小数的小数除法实际问题": 1.000015313675358, "用\"四舍五入\"法求小数的近似数": 1.000000043429446, "用\"四舍五入\"法取积的近似值（小数乘法）": 1.000000043429446, "用\"四舍五入\"法取积的近似值的实际应用（小数乘法）": 1.000000043429446, "商的近似数末尾有0的处理方法（小数除法）": 1.000000043429446, "用\"四舍五入\"法取商的近似值（小数除法）": 1.000000043429446, "用\"四舍五入\"法取商的近似值实际应用（小数除法）": 1.000000043429446, "用“四舍五入”法解决极值问题（小数除法）": 1.000000043429446, "进一法（小数）": 1.0000198175947606, "进一法应用题": 1.000055847268569, "进一、去尾解决问题": 1.0000504430080472, "去尾法（小数）": 1.0000072065027261, "去尾法应用题": 1.0000738609846334, "商与被除数的大小关系（除数是小数）": 1.0000108097092466, "小数连除": 1.000006305696425, "小数混合运算之错中求解": 1.000000043429446, "错中求解（小数乘小数）": 1.000000043429446, "估算解决实际问题（小数乘法）": 1.000000043429446, "小数乘小数的估算-": 1.000000043429446, "小数乘加、乘减混合运算": 1.0000027024525358, "小数四则混合运算顺序": 1.0000108097092466, "小数除加、除减混合运算": 1.0000018016368923, "带中括号的四则混合运算的应用": 1.000000043429446, "小数混合运算解决问题(不含除法)": 1.0000072065027261, "混合运算解决实际问题（含小数除法）": 1.000000043429446, "1减几分之几的计算": 1.0000108097092466, "同分母分数加法的含义及计算方法": 1.0000504430080472, "同分母分数加、减法": 1.000177425354644, "同分母分数减法的含义及计算方法": 1.0000351305713577, "同分母分数连加计算": 1.0000198175947606, "同分母分数连减计算": 1.0000108097092466, "1减几分之几的应用": 1.0000144128858726, "同分母分数加法在实际生活的应用": 1.0000360313178729, "同分母分数加、减法的应用": 1.0000990789319353, "运用假设法解决填分数算式的问题": 1.000000043429446, "异分母分数加法的计算方法": 1.000054946563152, "异分母分数减法的计算方法": 1.000020718373036, "解决问题(一半的一半应用题)": 1.0001125879442356, "异分母分数减法在实际生活的应用": 1.000062152154183, "分数加减混合运算": 1.0000594500715563, "同分母分数加减混合运算": 1.000000043429446, "同分母分数连加的计算方法": 1.000000043429446, "同分母分数连减的计算方法": 1.0000009008193804, "分数加、减混合运算解决问题": 1.000086470141324, "转化法": 1.0000009008193804, "利用分数墙计算同分母分数加减法": 1.0000009008193804, "求一个数比另一个数多(或少)几分之几": 1.0000108097092466, "求几分之几": 1.000000043429446, "求一个数是另一个数的几分之几": 1.0002035376883862, "求一个数是另一个数的几分之几的实际应用": 1.0000351305713577, "通过转换单位\"1\"求一个数是另一个数的几分之几": 1.000000043429446, "分数乘整数的计算": 1.000009908910419, "分数乘整数的意义": 1.000006305696425, "整数乘分数的实际应用": 1.0000027024525358, "整数乘分数的意义": 1.000000043429446, "分数乘分数的计算": 1.0000090081097233, "分数乘分数的意义": 1.0000090081097233, "分数乘分数的运用": 1.0000036032663107, "分数乘小数的计算": 1.0000108097092466, "分数连乘的计算": 1.000006305696425, "分数乘法中因数与积的大小关系": 1.000008107307159, "分数乘分数的比较大小": 1.000015313675358, "分数与整数相乘比较大小": 1.000000043429446, "利用整体的几分之几解决问题": 1.000000043429446, "简单求一个数的几分之几是多少": 1.0000324283206032, "理解一个数的几分之几是多少(分数乘法)": 1.000000043429446, "稍复杂的求一个数的几分之几是多少": 1.0000144128858726, "通过统一单位\"1\"求一个数的几分之几是多少": 1.000000043429446, "连续求一个数的几分之几是多少": 1.000017115248724, "解决商品提价、降价问题": 1.0000036032663107, "解决增减幅度一致的问题": 1.000000043429446, "求比一个数多/少几分之几的实际应用": 1.000000043429446, "求比一个数多/少几分之几的部分": 1.0000009008193804, "分数除以整数的意义": 1.000008107307159, "分数除以整数的计算": 1.000006305696425, "分数除以整数的应用": 1.0000126113012964, "分数与除法": 1.0000027024525358, "一个数除以分数的计算": 1.000015313675358, "一个数除以分数的简单应用及错中求解": 1.0000009008193804, "一个数除以分数的意义": 1.0000018016368923, "一个数除以分数的应用": 1.0000189168146167, "分数连除": 1.0000027024525358, "商与被除数的大小关系（分数除法）": 1.000022519923982, "已知一个数的几分之几是多少，求这个数（考法需整合）": 1.0000189168146167, "已知比一个数多(少)几分之几的数是多少，求这个数": 1.000027023768652, "分数乘除混合运算": 1.000009908910419, "分数乘除混合运算的应用题": 1.0000126113012964, "分数混合运算(不含分数除法)": 1.0000072065027261, "分数加减乘混合运算的应用题": 1.000020718373036, "巧用分数乘法运算律进行简便计算": 1.0000108097092466, "分数四则混合运算": 1.0000252222363888, "稍复杂的分数乘法解决问题": 1.000000043429446, "四则运算的意义和计算方法": 1.0000396342852518, "四则运算之间的关系": 1.000000043429446, "百分数四则混合运算": 1.0000135120945186, "运用百分比的知识填写表格": 1.000000043429446, "求百分率": 1.0000468401303384, "求一个数是另一个数的百分之几": 1.0000234206966525, "求一个数比另一个数多/少百分之几": 1.0000828675624827, "求一个数的百分之几是多少": 1.0000378328052988, "解决商品提价、降价问题(百分数)": 1.0000090081097233, "解决增减幅度一致的问题(百分数)": 1.0000018016368923, "求比一个数多(或少)百分之几的数是多少": 1.0000135120945186, "已知一个数的百分之几是多少，求这个数": 1.0000396342852518, "已知比一个数多(或少)百分之几的数是多少，求这个数": 1.000006305696425, "已知原价和折扣，求便宜的钱数": 1.0000243214674547, "折扣的意义": 1.00003693206252, "已知原价和折扣，求现价": 1.0000243214674547, "已知现价和折扣，求原价": 1.0000261230034544, "利润和折扣综合求折扣": 1.0000072065027261, "求税率": 1.000006305696425, "本金、利息、利率的含义": 1.000017115248724, "存款利息-": 1.0000450386802755, "求利率-": 1.0000108097092466, "求本金": 1.0000036032663107, "复利问题计算": 1.0000126113012964, "求成数": 1.0000234206966525, "增/减几成的数-": 1.0000072065027261, "求原来的量-": 1.000015313675358, "已知成本和利润率，求售价": 1.000000043429446, "已知成本和售价，求利润率": 1.0000009008193804, "已知售价和利润率，求成本": 1.0000036032663107, "用假设法解决涨幅问题": 1.000000043429446, "折扣和成数综合": 1.0000027024525358, "加减法算式性质": 1.000000043429446, "加减互逆关系(1-5)": 1.000000043429446, "加、减法各部分间的关系的计算与运用": 1.000045939406241, "加法的意义": 1.0000342298229745, "加法各部分之间的关系": 1.0000351305713577, "加减互逆关系(20以内退位)": 1.000000043429446, "加减互逆关系(6-10)": 1.000000043429446, "减法的意义": 1.0000342298229745, "减法各部分之间的关系": 1.000015313675358, "枚举列算式": 1.000000043429446, "认识加减法各部分名称": 1.0000009008193804, "想加算减(十几减8)": 1.0000009008193804, "想加算减(十几减9)": 1.000000043429446, "乘、除法各部分间的关系的最值问题": 1.0000054048882554, "乘法的意义": 1.0000450386802755, "乘法各部分之间的关系": 1.000020718373036, "除法的意义": 1.0000378328052988, "除法各部分之间的关系(无余数)": 1.0000180160326044, "除法各部分之间的关系(有余数)": 1.000028825293442, "根据商的取值确定一位数除数（删）": 1.000000043429446, "根据算式各部分之间的关系解决填数问题": 1.000000043429446, "有关0的运算": 1.0001783258060821, "不含括号的表内混合运算": 1.0000144128858726, "不含括号的四则混合运算的运算顺序": 1.0000180160326044, "含有两级运算的运算顺序": 1.0000126113012964, "同级运算的运算顺序": 1.0000135120945186, "不含括号的四则混合运算": 1.000008107307159, "用不含括号的三步混合运算解决实际问题": 1.000022519923982, "含有小括号的表内混合运算的运算顺序": 1.000015313675358, "含有中括号的四则混合运算的运算顺序": 1.000020718373036, "加减法应用(小括号)": 1.000000043429446, "加小括号的综合问题(表内混合运算)": 1.0000144128858726, "加小括号改变运算顺序问题": 1.0000036032663107, "加小括号或中括号改变运算顺序问题": 1.000040535022426, "加小括号问题综合": 1.0000180160326044, "口算(小括号)": 1.0000072065027261, "加法交换律": 1.000061251461842, "加法交换律和结合律的综合运用": 1.0000837682099946, "加法结合律": 1.0000297260530346, "运用加法运算定律解决实际问题": 1.000063052844656, "减法的运算性质": 1.0000468401303384, "连减巧算": 1.000000043429446, "运用减法的运算性质解决实际问题": 1.000055847268569, "运用减法的运算性质简算": 1.0000693576256643, "乘法交换律": 1.000020718373036, "乘法交换律和结合律的综合运用": 1.0000477408525679, "乘法结合律": 1.000030626810759, "乘法分配律": 1.0000729603165752, "乘法分配律的逆运算": 1.0000189168146167, "乘法分配律的应用": 1.0000324283206032, "运用除法的运算性质进行简算": 1.000021619149443, "除法的运算性质": 1.0000396342852518, "小数连加简算": 1.0000441379524418, "小数连减简算": 1.000028825293442, "分子是1的异分母分数加法的简便算法": 1.000021619149443, "分子是1的异分母分数减法的简便算法": 1.0000045040782173, "整数加法运算定律推广到分数": 1.0001017807680121, "分数提取公因数": 1.000006305696425, "应用分数乘法分配律进行简便计算": 1.0000072065027261, "综合应用分数乘法交换、结合律进行简便计算": 1.0000036032663107, "运算律与简便运算": 1.0000027024525358, "百分数的简便运算": 1.000000043429446, "运用乘法交换律和结合律简算": 1.000030626810759, "运用乘法分配律解决实际问题": 1.0001170908549581, "运用加法运算定律简算": 1.0000729603165752, "求一个数是另一个数的几倍": 1.000065754904867, "倍数应用题(表内)": 1.000000043429446, "根据倍数关系画图": 1.000000043429446, "看图求倍数(表内)": 1.0000036032663107, "看图求倍数(表内2-5)": 1.000000043429446, "求倍数应用题(2~5)": 1.000000043429446, "求一个数的几倍是多少": 1.0000441379524418, "看图求多倍量(2~5)": 1.0000009008193804, "求多倍量应用题(2-5)": 1.000000043429446, "有关增加或减少的倍数问题": 1.0000126113012964, "已知一个数的几倍是多少，求这个数(2-5)": 1.000000043429446, "已知一个数的几倍是多少，求这个数": 1.0000162144629754, "差相等的减法算式": 1.0000009008193804, "根据和的变化规律比大小": 1.000000043429446, "和不变规律(1-9)": 1.000000043429446, "和不变规律(20以内进位加)": 1.000000043429446, "两个加数一增一减(100以内)": 1.000000043429446, "积不变的规律": 1.0000018016368923, "积不变的规律的应用": 1.000008107307159, "两个因数都变化的规律(积变化的规律)": 1.0000054048882554, "两个因数都变化的规律的应用(积变化的规律)": 1.0000018016368923, "一个因数变化的规律": 1.0000072065027261, "一个因数变化的规律的应用": 1.000000043429446, "运用积的变化规律解决图形问题": 1.000008107307159, "运用积的变化规律解决问题": 1.0000027024525358, "解决文字叙述问题(小数点移动规律)": 1.0000738609846334, "商不变的规律": 1.000015313675358, "商的变化规律": 1.0000819669131027, "商的变化规律的实际应用": 1.000000043429446, "商的变化规律在小数除法中的应用": 1.0000009008193804, "应用商不变的规律进行简便计算": 1.0000036032663107, "运用商不变的规律解决实际问题": 1.0000009008193804, "商不变规律中余数的变化": 1.0000072065027261, "商的变化规律（小数除法）": 1.0000090081097233, "算盘的应用(大数)": 1.0000027024525358, "算盘表示数(1000以内)": 1.0000162144629754, "算盘上拨数(1000以内)": 1.0000090081097233, "计算器按键损坏问题": 1.0000027024525358, "使用计算器计算": 1.000000043429446, "组数求积的最大值或最小值": 1.000000043429446, "奇怪的142857": 1.000000043429446, "神奇的9": 1.0000045040782173, "用计算器计算小数乘除法": 1.0000072065027261, "用计算器探索规律": 1.0000027024525358, "有趣的\"回文数\"": 1.000000043429446, "用字母表示单价、数量、总价间的关系": 1.000006305696425, "用字母表示工效、工时、工总间的关系": 1.0000009008193804, "用字母表示数量关系(ax±bx)": 1.000015313675358, "用字母表示数量关系(a±bx)或(ax±b)": 1.0000468401303384, "用字母表示数量关系ax或x÷a或a÷x": 1.0000315275666152, "用字母表示数量关系a±x或x±a": 1.000030626810759, "用字母表示速度、时间、路程间的关系": 1.0000108097092466, "用字母表示计算公式": 1.000020718373036, "用字母表示运算律": 1.0000036032663107, "用字母关系式总结数字、图形变化规律9": 1.0000045040782173, "等量代换在算式计算和实际问题中的运用-": 1.0000018016368923, "天平上的等量代换-": 1.0000027024525358, "含两个对象等量代换求质量": 1.0000027024525358, "含三个对象等量代换求质量": 1.0000072065027261, "简单的天平代换": 1.0000036032663107, "图形中的等量代换": 1.0000090081097233, "方程的意义": 1.0000297260530346, "运用自然数间的关系列方程": 1.0000009008193804, "等式的性质1": 1.0000144128858726, "等式的性质2": 1.0000072065027261, "解方程：x±a=b这种类型": 1.000015313675358, "解方程：ax=b这种类型(a≠0)": 1.0000162144629754, "解方程：a-x=b这种类型": 1.0000045040782173, "解方程：ax±b=c这种类型(a≠0)": 1.000022519923982, "用数与形探索规律-": 1.0000009008193804, "用字母表示立体图形中的量-": 1.0000045040782173, "与比有关的工程问题-": 1.0000045040782173, "方中圆问题（一方一圆）-": 1.0000045040782173, "圆中方问题（一圆一方）-": 1.0000009008193804, "画扇形-": 1.0000009008193804, "工程中的百分数问题": 1.0000009008193804, "列方程解决班级人数变化问题-": 1.0000009008193804, "列方程解决稍复杂实际问题-": 1.0000009008193804, "与量的交换、添加、减少有关的求比问题-": 1.0000018016368923, "求溶液问题中的比-": 1.0000009008193804, "求利润-": 1.0000054048882554, "求量之比（简单实际问题）-": 1.0000036032663107, "搭配路线": 1.0000234206966525, "数阵图（一位小数）": 1.0000018016368923, "数对、位置与简单行程问题": 1.0000018016368923, "知盈利亏损情况，求成本-": 1.0000009008193804, "班级人数变化的百分数问题-": 1.0000009008193804, "用方程法解分数乘法应用题": 1.0000027024525358, "求总量的实际问题（含比）-": 1.0000045040782173, "知比原价便宜的钱数，求原价-": 1.0000009008193804, "推理法求真分数-": 1.0000072065027261, "组合体中添/去掉小正方体的摆法或位置问题（三）": 1.0000018016368923, "已知部分量占总量的分率，求总量-": 1.0000018016368923, "百分数有关的求总量问题-": 1.0000018016368923, "方格图中的数对表示位置-": 1.0000036032663107, "方格图中的数对找位置-": 1.0000018016368923, "估算的运用（小数乘法）-": 1.000000043429446, "与百分数有关的补充条件/问题": 1.0000036032663107, "梯形面积公式正用-": 1.000000043429446, "梯形面积公式逆用-": 1.0000018016368923, "用字母表示数量关系（a±b）÷X或a÷x±b÷x": 1.0000018016368923, "方程解有关的问题-": 1.0000018016368923, "看图列方程-": 1.0000009008193804, "用比例方程解决行程问题-": 1.0000009008193804, "用比例方程解决工程问题-": 1.000000043429446, "化简比（百分数）": 1.000000043429446, "由实际问题，找两量的正确图线-": 1.0000027024525358, "由积木上数字绘制几何体的平面图形（三）": 1.0000009008193804, "复式统计表的信息提取": 1.0000396342852518, "分数连乘的实际应用-": 1.0000054048882554, "排水法求物体体积（单个物体）-": 1.000000043429446, "排水法求物体体积（多个物体）-": 1.0000009008193804, "可能性大小的计算-": 1.0000054048882554, "扇形统计图中获取信息计算-": 1.000008107307159, "等差数列计算-": 1.0000009008193804, "根据可能性大小反求个数-": 1.0000027024525358, "用小棒摆立体图形-": 1.0000009008193804, "五日游中路程时间速度问题-": 1.0000009008193804, "五日游中的费用问题-": 1.0000045040782173, "含扇形的组合图形中阴影部分周长/面积-": 1.0000009008193804, "三个量的鸡兔同笼问题": 1.000008107307159, "根据运动情况绘制示意图-": 1.0000009008193804, "画图法解鸡兔同笼": 1.0000027024525358, "梯形与三角形面积问题-": 1.0000009008193804, "平面图形转动中的顶点轨迹问题-": 1.000000043429446, "容器中含物的注水问题（含圆锥）-": 1.000000043429446, "容器中含物的注水问题（含圆柱）-": 1.000000043429446, "根据方向，角度，距离在坐标图中找位置/绘制点-": 1.000000043429446, "图形运动中的面积问题（含圆）-": 1.000000043429446, "推理可能性最大（小）的事件/结果/数字...": 1.000000043429446, "由结果可能性大小反求原来组成情况-": 1.000000043429446, "运行图中的行程问题（复式折线图）-": 1.000000043429446, "图形/符号/物品间的运算-": 1.000000043429446, "组合平面图形中的比-": 1.000000043429446, "列方程解决实际问题ax=bc(a≠0)-": 1.000000043429446, "知部分量，求总量-": 1.000000043429446, "质数、合数有关的数字推理问题-": 1.000000043429446, "质数有关的数字推理问题-": 1.000000043429446, "合数有关的数字推理问题-": 1.000000043429446, "质数有关的实际应用题-": 1.000000043429446, "合数有关的实际应用题-": 1.000000043429446, "晴/雨天问题": 1.000000043429446, "2倍数的运用-": 1.000000043429446, "5倍数的运用-": 1.000000043429446, "2、5倍数的运用-": 1.000000043429446, "偶数的运用-": 1.000000043429446, "奇数的运用-": 1.000000043429446, "3倍数的运用-": 1.000000043429446, "质数与合数的认识-": 1.000000043429446, "方向与路程的综合问题（四个方向）": 1.000000043429446, "其他倍数的运用-": 1.000000043429446, "价格中的负数": 1.000000043429446, "求现在的量-": 1.000000043429446, "圆柱容积的应用-": 1.000000043429446, "几何体中削圆锥（求高/底面量）-": 1.000000043429446, "成数先减少后增加-": 1.000000043429446, "圆柱侧面积逆用-": 1.000000043429446, "含圆锥的组合体的体积（挖去型）-": 1.000000043429446, "利润和成数综合求成本-": 1.000000043429446, "正负数的计算": 1.000000043429446, "周期问题（两位数除以一位数）": 1.000000043429446, "折扣与百分率-": 1.000000043429446, "图形的份数与乘除法运算": 1.000000043429446, "两种（及以上）物品搭配组合（除数是一位数的口算）": 1.000000043429446, "两种（及以上）物品搭配组合（除数是一位数的笔算）": 1.000000043429446, "成数先增加后减少-": 1.000000043429446, "求应纳税部分-": 1.000000043429446, "天平称重": 1.000009908910419, "一般油桶问题": 1.000000043429446, "结合平面图形的特点推断图形": 1.000000043429446, "其他推理问题": 1.000000043429446, "由高的增/减，求侧面积-": 1.000000043429446, "长/正方形旋转成圆柱问题（求表面积）-": 1.000000043429446, "圆柱表面积逆用-": 1.000000043429446, "看图列式（十几减7、6）": 1.000000043429446, "看图列式（十几减8）": 1.000000043429446, "十几减7、6的计算方法": 1.000000043429446, "圆柱切拼成近似长方体的表面积": 1.000000043429446, "数独问题": 1.000000043429446, "求圆柱的容积-": 1.000000043429446, "两圆柱间与体积有关的计算-": 1.000000043429446, "由圆柱侧面展开图求体积-": 1.000000043429446, "由高和表面积增/减变化求体积问题": 1.000000043429446, "1000以内数的综合应用": 1.000000043429446, "认识\"一千\"": 1.000000043429446, "圆柱体积（容积）实际应用（容球、大分小、图象、卷纸）": 1.000000043429446, "两容器的水中浸物问题-": 1.000000043429446, "圆锥上的最短路线": 1.000000043429446, "圆锥体积有关的实际问题（图象、空余）": 1.000000043429446, "10000以内数的综合应用": 1.000000043429446, "水中浸物问题（圆柱中放圆锥）-": 1.000000043429446, "圆柱与圆锥关系（求体积/体积关系）-": 1.000000043429446, "圆柱与圆锥关系（求高/高的关系）-": 1.000000043429446, "圆柱与圆锥关系（求底面积/底面积关系）-": 1.000000043429446, "等积变形（圆锥与圆柱间的倒水问题）-": 1.000000043429446, "削成最大的圆锥（圆柱削圆锥）-": 1.000000043429446, "削成最大的圆锥（正方体削圆锥）-": 1.000000043429446, "削成最大的圆锥（长方体削圆锥）-": 1.000000043429446, "比例的意义（写比例、写比）": 1.000000043429446, "组成比例（线段图、知比值、补最大数）": 1.000000043429446, "图与比例(阴影、两图间、数变化)": 1.000000043429446, "用比例基本性质推理求值（解比例）-": 1.000000043429446, "解比例的实际应用（补全、次数、总量）": 1.000000043429446, "笔算加法的综合应用（两位数加两位数/整十数/一位数）": 1.000000043429446, "笔算减法的综合运用（两位数减两位数/一位数）": 1.000000043429446, "口算减法的综合应用（两位数减两位数/一位数/整十数）": 1.000000043429446, "解比例在立体图形中的应用-": 1.000000043429446, "求图上周长、面积问题-": 1.000000043429446, "比例尺有关的综合应用题（结合工程）-": 1.000000043429446, "拆长方体纸盒": 1.000000043429446, "正比例的应用（弹簧、蜡烛、浓度、百米、黄金比）": 1.000000043429446, "营养午餐": 1.000000043429446, "正比例在行程中的应用（油表、数线图、到时时刻）": 1.000000043429446, "反比例的应用（植树、天平、图形、水上升）": 1.000000043429446, "反比例解决自行车运动问题（公式、齿轮转动圈数，一圈距离）": 1.000000043429446, "反比例解决自行车运动问题（结构、全程、齿数比、圈数比）": 1.000000043429446, "反比例解决变速自行车运动问题（速度种类、最远组合、选组合）": 1.000000043429446, "反比例解决变速自行车运动问题（全程、蹬的圈数，不同齿数比）": 1.000000043429446, "反比例解决自行车运动问题（半径比、比转圈、车轮转动圈数）": 1.000000043429446, "比较液体多少": 1.000000043429446, "行列与数对": 1.000000043429446, "等量代换（5以内）": 1.000000043429446, "数轴上表示数(亿以内)": 1.000000043429446, "亿以内数的综合应用": 1.000000043429446, "分数乘整数的实际应用": 1.000000043429446, "一个数与分数乘法的错中求解": 1.000000043429446, "整数乘分数的应用": 1.000000043429446, "整数乘分数的计算": 1.000000043429446, "运用假设法和分类讨论法解决分数乘法问题": 1.000000043429446, "分数乘小数的应用": 1.000000043429446, "分数乘小数的实际问题": 1.000000043429446, "改正错误并正确读写(亿以上)": 1.000000043429446, "2、5、3倍数的运用（二）": 1.000000043429446, "2、5、3倍数的运用（三）": 1.000000043429446, "质合综合运用（分类、最值）": 1.000000043429446, "质合综合运用（猜数、错中求、程序算）": 1.000000043429446, "分数乘分数的实际应用": 1.000000043429446, "偶数的认识（与2倍数关系、按要求写偶数）": 1.000000043429446, "奇数的认识（找奇数、与2倍数关系）": 1.000000043429446, "循环小数在计算中的运用-": 1.000000043429446, "小数除法估算的实际应用-": 1.000000043429446, "掷骰子中的相关问题-": 1.000000043429446, "整数乘法运算律在分数乘法中的应用": 1.000000043429446, "整数乘法运算律在分数乘法中的实际应用": 1.000000043429446, "含亿级数位顺序表的应用": 1.000000043429446, "亿以上数的综合应用": 1.000000043429446, "用数对、方向和距离确定位置": 1.000000043429446, "应用位置和方向及行程问题的知识解决实际问题": 1.000000043429446, "商不变的规律（分数除法）": 1.000000043429446, "分数四则混合运算的运用": 1.000000043429446, "一个数是另一个数的几分之几/几倍（分数除法）": 1.000000043429446, "分数四则混合运算的实际应用": 1.000000043429446, "小数与整数连乘的实际应用-": 1.000000043429446, "解决含有两个单位“1”的问题（分数除法）": 1.000000043429446, "小数与小数连乘的实际应用-": 1.000000043429446, "看线段图列式（分数除法）": 1.000000043429446, "找对应量和对应分率，求单位“1”的问题": 1.000000043429446, "积不变的性质（小数乘小数）-": 1.000000043429446, "数轴上表示数(亿以上)": 1.000000043429446, "通过近似数反推原数(亿以内)": 1.000000043429446, "定义新运算（ 分数除法）": 1.000000043429446, "由所列方程写数量关系式-": 1.000000043429446, "列方程解决实际问题ax-ab=c或a(x-b)=c": 1.000000043429446, "应用比的意义解决问题": 1.000000043429446, "三位数乘两位数综合计算": 1.000000043429446, "比值的应用": 1.000000043429446, "其他常见的数量关系的应用(如工程问题)": 1.000000043429446, "与圆的半径、直径有关的计算": 1.000000043429446, "梯形与长/正方形面积问题-": 1.000000043429446, "半圆周长的应用": 1.000000043429446, "计算不规则图形的面积-": 1.000000043429446, "植树问题之贴瓷砖问题-": 1.000000043429446, "认识圆的面积": 1.000000043429446, "圆中半径、直径、周长、面积的关系": 1.000000043429446, "圆环的认识": 1.000015313675358, "外方内圆和外圆内方的理解": 1.000000043429446, "组合法计算扇形的面积": 1.000000043429446, "利用整体法（R²－r²）求圆环的面积": 1.000000043429446, "求组合图形的周长": 1.000000043429446, "圆的面积的实际应用": 1.000000043429446, "正方体的展开与折叠": 1.000000043429446, "百分数表示任务进度/折扣等": 1.000000043429446, "看图列式（与百分数有关）": 1.000000043429446, "平行四边形周长相关的计算": 1.000000043429446, "认识等腰直角梯形": 1.000000043429446, "甲比乙多/少几分之几的含义": 1.000000043429446, "画梯形": 1.000000043429446, "解“ax±bx=c”型方程（？）": 1.000000043429446, "百分数混合运算的应用": 1.000000043429446, "由两量之间百分数的关系比较大小": 1.000000043429446, "用方程解决天平上物重问题-": 1.000000043429446, "扇形统计图中的开放性问题": 1.000000043429446, "圆的周长在圆滚动中的应用": 1.000000043429446, "商是一位数的笔算运用": 1.000000043429446, "列方程解决简单的\"行程问题\"": 1.000000043429446, "列方程解决复杂的\"行程问题\"": 1.000000043429446, "商是两位数的笔算运用": 1.000000043429446, "初步认识乘法意义的运用": 1.000000043429446, "5的乘法口诀理解运用": 1.000000043429446, "5的乘法口诀的计算-": 1.000000043429446, "2-4的乘法口诀运用": 1.000000043429446, "2-4的乘法口诀的计算-": 1.000000043429446, "2-5乘加、乘减的理解-": 1.000000043429446, "6的乘法口诀的运用": 1.000000043429446, "6的乘法口诀的计算-": 1.000000043429446, "除数是整十数的计算运用": 1.000000043429446, "2-6的乘法口诀的理解-": 1.000000043429446, "商的变化规律和商的不变规律综合应用": 1.000000043429446, "分一分求份数-": 1.000000043429446, "分一分求每份数-": 1.000000043429446, "分一分求份数、每份数-": 1.000000043429446, "理解平均分-": 1.000000043429446, "分一分完成除法算式（求每份数、份数)-": 1.000000043429446, "分一分完成除法算式（求每份数)-": 1.000000043429446, "分一分完成除法算式（求份数)-": 1.000000043429446, "看图列乘、除法算式-": 1.000000043429446, "2-6乘法口诀运用（列/找/写算式）-": 1.000000043429446, "2-6含除法的计算-": 1.000000043429446, "2-6乘法口诀求值（求份数/每份数）-": 1.000000043429446, "2-6乘法口诀求商的运用-": 1.000000043429446, "除法应用题（2-6）-": 1.000000043429446, "通过连减认识除法-": 1.000000043429446, "除法初步认识的运用-": 1.000000043429446, "方向与生活-": 1.000000043429446, "认识厘米（二）": 1.000000043429446, "曹冲称象的故事与数学思想": 1.000000043429446, "测量身体上的长度-": 1.000000043429446, "身体上的计量单位-": 1.000000043429446, "上课规矩": 1.000000043429446, "有关质量的常识": 1.000000043429446, "看情境图比较大小（1-5）": 1.000000043429446, "吨的应用": 1.000000043429446, "有关0的加减法的应用": 1.000000043429446, "比较大小的应用": 1.000000043429446, "减法计算的应用（1-5）": 1.000000043429446, "6~9的数比大小的应用": 1.000000043429446, "会画缺少的部分（6~9）": 1.000000043429446, "6、7的加法的实际应用": 1.000000043429446, "找规律填数（10以内）": 1.000000043429446, "8、9的加减法的实际应用": 1.000000043429446, "认识10": 1.000000043429446, "10的加减法的实际应用": 1.000000043429446, "连减计算（10以内）": 1.000000043429446, "连加的应用（10以内）": 1.000000043429446, "比较两条线段的长短": 1.000000043429446, "7的乘法口诀-": 1.000000043429446, "计算（7的乘法口诀）-": 1.000000043429446, "7的乘法应用题-": 1.000000043429446, "8的乘法口诀-": 1.000000043429446, "计算（8的乘法口诀）-": 1.000000043429446, "8的乘法应用题-": 1.000000043429446, "9的乘法口诀-": 1.000000043429446, "计算（9的乘法口诀）-": 1.000000043429446, "9的乘法应用-": 1.000000043429446, "表内乘法错中求解-": 1.000000043429446, "7-9乘法口诀求商（计算）-": 1.000000043429446, "7-9乘法口诀求商（列式计算）-": 1.000000043429446, "7-9乘法口诀求商（看图填空）-": 1.000000043429446, "7-9乘法口诀求商（应用题）-": 1.000000043429446, "7-9乘法口诀求商（运用）-": 1.000000043429446, "归总再等分的实际问题（表内）-": 1.000000043429446, "表内混合计算-": 1.000000043429446, "8的乘法口诀运用-": 1.000000043429446, "多种/步计算的应用题（表内）-": 1.000000043429446, "十加几及其对应的减法计算": 1.000000043429446, "十加几及其对应减法的应用": 1.000000043429446, "十几加几的不进位加法的计算": 1.000000043429446, "9加几的运用": 1.000000043429446, "9加几的实际应用": 1.000000043429446, "8、7、6加几的应用": 1.000000043429446, "8、7、6加几的实际应用": 1.000000043429446, "5、4、3、2加几的实际应用": 1.000000043429446, "两种思路求总数": 1.000000043429446, "连加/连减/混合运算的实际应用（20以内不进位不退位）": 1.000000043429446, "5、4、3、2加几的应用": 1.000000043429446, "加法实际应用(1-5)": 1.000000043429446, "小数乘整数的估算-": 1.000000043429446, "多重分类（或层层分类）-": 1.000000043429446, "小数乘小数运用（乘数不全）-": 1.000000043429446, "小数乘法运算律的运用-": 1.000000043429446, "小数乘法与数轴-": 1.000000043429446, "小数乘小数在加油行程中的应用-": 1.000000043429446, "解决比较/够不够的实际问题（小数混合运算不含除法）-": 1.000000043429446, "相遇问题中的应用（小数混合运算不含除法）-": 1.000000043429446, "小数混合运算解决一半逆还原问题（不含除法）-": 1.000000043429446, "小数乘整数中去掉小数点-": 1.000000043429446, "理解小数乘整数-": 1.000000043429446, "小数乘整数的运用（乘数不全）-": 1.000000043429446, "解决折返、对折实际问题（小数乘整数）-": 1.000000043429446, "解决弹簧挂物问题（小数混合运算不含除法）-": 1.000000043429446, "小数点的位置（小数乘整数）-": 1.000000043429446, "解决间隔问题（小数混合运算不含除法）-": 1.000000043429446, "面积、周长有关的问题（小数混合运算不含除法）-": 1.000000043429446, "积的变化规律（小数乘整数）-": 1.000000043429446, "积不变的性质（小数乘整数）-": 1.000000043429446, "组小数计算问题-": 1.000000043429446, "定义新运算（分数乘法）": 1.000000043429446, "按不同标准分类（初阶）-": 1.000000043429446, "分类中的其它问题-": 1.000000043429446, "看线段图列式（分数乘法）": 1.000000043429446, "小数除法与数轴": 1.000000043429446, "追击问题中的应用（小数混合运算不含除法）-": 1.000000043429446, "小数乘加、乘减的实际问题-": 1.000000043429446, "小数乘法运算律解决其它问题-": 1.000000043429446, "小数混合运算解决其它实际问题（不含除法）-": 1.000000043429446, "植树问题之其它问题-": 1.000000043429446, "认识平方千米": 1.000000043429446, "选择合适的面积单位(公顷、平方米)": 1.000000043429446, "估算解决能否完成/参加的问题（小数乘加/乘减）-": 1.000000043429446, "小数乘加、乘减解决其它分段计费问题-": 1.000000043429446, "分数混合运算与几何图形的综合": 1.000000043429446, "除数是整数的小数除法竖式谜": 1.000000043429446, "文字叙述的小数除法（除数是小数）-": 1.000000043429446, "除数是整数的小数除法解决行程问题": 1.000000043429446, "除数是整数的小数除法解决周长/面积问题": 1.000000043429446, "除数是整数的小数除法解决含倍的问题": 1.000000043429446, "除数是小数的小数除法解决含倍的问题-": 1.000000043429446, "除数是小数的小数除法解决行程问题-": 1.000000043429446, "除数是小数的小数除法解决其它问题-": 1.000000043429446, "角的分类(锐角、直角、钝角、平角和周角)": 1.000000043429446, "除数是整数的小数除法解决其它问题": 1.000000043429446, "小数乘除解决实际问题-": 1.000000043429446, "混合运算解决行程问题（含小数除法）": 1.000000043429446, "混合运算解决面积/周长问题（含小数除法）": 1.000000043429446, "混合运算解决其它实际问题（含小数除法）": 1.000000043429446, "用\"四舍五入\"法求平均数的近似数问题（小数除法）": 1.000000043429446, "移多补少解决平均分问题 （小数除法）": 1.000000043429446, "被除数的整数部分不够商1的小数除法-": 1.000000043429446, "倍数有关小数乘法的复杂应用-": 1.000000043429446, "小数乘整数解决比较的实际问题-": 1.000000043429446, "速度、时间、路程的关系应用": 1.000000043429446, "小数乘小数解决比较的实际问题": 1.000000043429446, "含倍的小数乘法在面积/周长中的应用": 1.000000043429446, "积的近似数的实际应用（小数乘整数）-": 1.000000043429446, "积的近似数的实际应用（小数乘小数）-": 1.000000043429446, "平行四边形和梯形的综合应用": 1.000000043429446, "小数乘法分配律": 1.000000043429446, "小数乘法结合律-": 1.000000043429446, "认识单式条形统计图": 1.000000043429446, "横向单式条形统计图": 1.000000043429446, "与分数乘法有关的移多补少问题": 1.000000043429446, "用两位数除以整十数解决简单的实际问题": 1.000000043429446, "除数不接近整十数的笔算除法解决问题": 1.000000043429446, "求比一个数多/少几分之几的应用": 1.000000043429446, "小数乘法交换律-": 1.000000043429446, "分数乘法运算与几何的综合应用（分数乘整数）": 1.000000043429446, "分数乘法运算与几何的综合运用（分数乘分数）": 1.000000043429446, "分数乘法运算与几何的综合运用": 1.000000043429446, "用两步计算解决问题(表内混合运算)(含括号)": 1.000000043429446, "表内混合运算的计算与应用": 1.000000043429446, "求一个数的几分之几的问题（分数乘整数）": 1.000000043429446, "运用排列组合规律解决判断可能性大小问题--": 1.000000043429446, "运用图示法解决复杂可能性问题--": 1.000000043429446, "多位数乘一位数的计算与应用(综合)": 1.000000043429446, "2-4看图列乘法算式-": 1.000000043429446, "6看图列乘法算式-": 1.000000043429446, "解方程：a(x±b)=c这种类型(a≠0)": 1.0000072065027261, "分数方程解决实际应用": 1.0000072065027261, "解分数方程": 1.0000891720558436, "用方程法解分数除法应用题": 1.0000009008193804, "比多比少求单位\"1\"(方程法)": 1.000000043429446, "解百分数方程": 1.0000234206966525, "列方程解百分数应用题": 1.0000090081097233, "列方程解决实际问题ax+ab=c或a(x＋b)=c": 1.0000180160326044, "列方程解决实际问题ax=b(a≠0)": 1.000015313675358, "列方程解决实际问题ax±b=c(a≠0)": 1.000040535022426, "列方程解决实际问题x±a=b": 1.000008107307159, "列方程解决\"差倍问题\"（ax-bx=c）": 1.000000043429446, "列方程解决\"和倍问题\"（ax+bx=c）": 1.000000043429446, "列方程解决简单的\"年龄问题\"": 1.000000043429446, "列方程解决简单的\"相遇问题\"": 1.000000043429446, "列方程解决稍复杂的\"相遇问题\"": 1.000000043429446, "列方程解决稍复杂的\"追及问题\"": 1.000000043429446, "用工程问题的思想解行程相遇问题": 1.0000090081097233, "列方程解决稍复杂的\"盈亏问题\"": 1.000000043429446, "等式与方程": 1.0000018016368923, "比的意义": 1.0000234206966525, "比的读写法及各部分的名称": 1.000000043429446, "求比值": 1.0000432372227401, "比的基本性质": 1.000027924531981, "运用比的基本性质解决比值变化的问题": 1.0000018016368923, "运用比的基本性质解决比值不变的问题": 1.0000117105062056, "化简比(带单位)": 1.000008107307159, "化简比(分数)": 1.000008107307159, "化简比(小数)": 1.0000045040782173, "化简比(整数)": 1.000008107307159, "化连比": 1.0000054048882554, "整数小数分数化简比": 1.0000045040782173, "比和分数、除法的关系": 1.0000108097092466, "求比中的未知项": 1.0000018016368923, "数的性质": 1.000000043429446, "比与百分数": 1.000000043429446, "数之间的联系": 1.000000043429446, "按比分配": 1.0000018016368923, "已知总量，分量比未知，求分量": 1.0000036032663107, "已知总量和分量比，求分量": 1.0000468401303384, "与图形相关的按比分配问题": 1.0000018016368923, "不变量解决比的问题": 1.0000018016368923, "根据两数的等量关系求比": 1.0000072065027261, "判断可能的分量比": 1.0000072065027261, "认识\"黄金比\"和\"黄金分割\"": 1.000000043429446, "树叶中的比": 1.000000043429446, "树叶中的比的综合应用": 1.000000043429446, "与比有关的行程问题-": 1.0000315275666152, "已知分量差和分量比，求分量或总量": 1.0000144128858726, "已知一个分量和分量比，求其它量": 1.0000072065027261, "与常见图形有关的比和化简比": 1.0000261230034544, "圆与比/倍数的综合问题": 1.0000198175947606, "圆的周长比与半径比、直径比的关系": 1.0000036032663107, "根据两数的分率关系求比": 1.0000018016368923, "化连比的应用": 1.000000043429446, "比例的意义（补全、判断、改写、辨析）": 1.000000043429446, "比例各部分名称": 1.000015313675358, "判定能否组成比例（用比例的意义）": 1.0000999795458285, "比与比例": 1.000000043429446, "比例的基本性质": 1.000271062306153, "根据等式写比例": 1.0001008801578541, "判定能否组成比例（用比例的基本性质）": 1.000020718373036, "比例中某项变化解决比例仍成立问题": 1.0000315275666152, "根据反比例关系计算": 1.0000018016368923, "根据正比例关系计算": 1.0000108097092466, "解比例（概念、计算）": 1.0001170908549581, "解比例的实际应用（溶液、铺砖、影长）": 1.000000043429446, "正比例的判断": 1.0000540458558673, "正比例的意义": 1.0000360313178729, "根据正比例的图象解决问题": 1.0000468401303384, "正比例图象的特点": 1.0000315275666152, "反比例的判断": 1.000028825293442, "反比例的意义": 1.0000324283206032, "反比例图象的特点": 1.0000018016368923, "正、反比例意义辨析": 1.0000144128858726, "影子问题": 1.000022519923982, "正比例的应用（补全、平面图、切割）": 1.000000043429446, "正比例与反比例": 1.0000009008193804, "反比例的应用（补全题、选算式）": 1.000000043429446, "由反比例图象解决问题": 1.0000108097092466, "有趣的平衡-": 1.000009908910419, "比例尺的定义及分类": 1.000062152154183, "数值比例尺（可删）": 1.000000043429446, "线段比例尺（可删）": 1.000000043429446, "线段比例尺与数值比例尺的互化": 1.0000378328052988, "由实际数据计算比例尺": 1.0001296987566082, "由比例尺画平面图/找物体位置": 1.0000378328052988, "由比例尺求实际距离": 1.0001017807680121, "由比例尺求图上距离": 1.000068456948267, "求图形的放大比、缩小比-": 1.0000198175947606, "求图形放大/缩小后的量（边长/周长/面积/体积）-": 1.0000126113012964, "图形放大与缩小的理解": 1.000040535022426, "在方格纸上按一定的比将图形放大": 1.000028825293442, "在方格纸上按一定的比将图形缩小": 1.0000261230034544, "三角形底、高变化引起的面积变化": 1.0000108097092466, "求图形放大/缩小后量之比或倍数（边长/周长/面积/体积）-": 1.000008107307159, "比例尺有关的路线图问题-": 1.0000018016368923, "认识整时": 1.0000027024525358, "认识半时": 1.000000043429446, "画钟表": 1.0000009008193804, "画钟表(几时几分)": 1.0000045040782173, "认读时间(几时几分)": 1.0000180160326044, "认识大约几时": 1.000000043429446, "生活中的时间(几时几分)": 1.0000045040782173, "选正确的钟表": 1.000000043429446, "找规律(几时几分)": 1.000000043429446, "判断时针分针": 1.000000043429446, "认识分": 1.0000009008193804, "时、分、秒时间单位换算(单名数)": 1.0000162144629754, "时与分的换算": 1.0000108097092466, "计量秒的关系": 1.0000009008193804, "认识钟面上的指针": 1.000008107307159, "体验生活中的时分": 1.0000054048882554, "比较时间的长短": 1.0000090081097233, "按时间排序": 1.000000043429446, "时针、分针、秒针的基本走法之间的关系": 1.0000054048882554, "时、分、秒时间单位换算(单名数与复名数互化)": 1.0000090081097233, "常见节日认识": 1.0000198175947606, "年、月、日换算": 1.0000360313178729, "认识大月与小月": 1.000063953533261, "认识月份和季度": 1.0000297260530346, "普通计时法与24时计时法的转化": 1.0000792649537567, "分段计时解决问题": 1.0000036032663107, "计算经过的时间": 1.000020718373036, "计算开始或结束时刻": 1.000020718373036, "计算经过时间(几时几分)": 1.0000045040782173, "时间的计算(时分秒)": 1.0000036032663107, "解决求等车时间的问题": 1.0000018016368923, "与星期几有关的推算（没有日历）": 1.0000297260530346, "24时计时法简单应用": 1.0000261230034544, "24时计算经过的时间": 1.0000990789319353, "根据时间表解决简单的实际问题": 1.0000045040782173, "认识24时计时法、12时计时法": 1.0000198175947606, "时间推理(几时几分)": 1.0000162144629754, "时间排序、推理及比较": 1.000000043429446, "根据已知时间推算过半时后的时间": 1.000000043429446, "根据已知时间推算过几时后的时间": 1.000000043429446, "共同的休息日": 1.000000043429446, "日历中的规律": 1.0000189168146167, "推理几天前或几天后是星期几": 1.000008107307159, "推理昨天或明天是星期几": 1.000000043429446, "日历中的周期问题": 1.0000180160326044, "认识平年和闰年": 1.0000792649537567, "选择合适的时间单位(时、分、秒)": 1.0000108097092466, "人民币单位间的换算": 1.000015313675358, "人民币的单位换算(小面额)（删）": 1.000000043429446, "人民币的兑换": 1.000021619149443, "人民币的兑换(大面额)（删）": 1.000000043429446, "人民币的兑换(小面额)（删）": 1.000000043429446, "人民币兑换(求积、商的近似数)": 1.000000043429446, "认识1元及1元以下的人民币": 1.000000043429446, "认识小面额人民币": 1.0000117105062056, "加减法应用(人民币)": 1.000028825293442, "简单的加、减运算(人民币)": 1.0000045040782173, "人民币的计算与比较": 1.000008107307159, "认识5元及5元以上的人民币": 1.000000043429446, "认识大面额人民币": 1.0000036032663107, "认识克": 1.0000072065027261, "认识千克": 1.0000018016368923, "有关千克的简单应用": 1.000000043429446, "认识质量单位\"吨\"": 1.000000043429446, "千克和克之间的进率及换算": 1.0000252222363888, "质量单位比较大小(吨、千克、克)": 1.000000043429446, "吨、千克、克的计算": 1.000000043429446, "等量秤物品": 1.000000043429446, "解决吨相关的实际问题": 1.0000126113012964, "吨、千克和克之间的进率及换算": 1.000000043429446, "填质量单位(克和千克)": 1.0000261230034544, "填合适的质量单位(吨、千克、克)": 1.000000043429446, "千克与克的相关应用题": 1.0000315275666152, "千克与克的应用": 1.000000043429446, "认识交通标志": 1.0000036032663107, "自选长度单位的测量": 1.0000009008193804, "量与计量": 1.000000043429446, "比大小(米和厘米)": 1.0000027024525358, "多个量比大小(人民币)": 1.000000043429446, "分米、厘米和毫米之间的比较大小": 1.0000036032663107, "面积单位比较大小": 1.000000043429446, "千米和米的计算": 1.000008107307159, "人民币的大小比较": 1.000000043429446, "根据条件提出问题并解答": 1.0000144128858726, "提出问题并列算式(20以内进位)": 1.0000009008193804, "先补充条件，再解答": 1.0000045040782173, "计算": 1.0003520779914894, "看图列减法算式(6-10)": 1.000000043429446, "看图列减法算式(整体与部分)": 1.0000018016368923, "看图列式": 1.0000774636381868, "看图列式(20以内进位加)": 1.000000043429446, "求一共（20以内）": 1.0000072065027261, "求原来": 1.000030626810759, "求原来(20以内)": 1.0000009008193804, "图文算式(1-5)": 1.000000043429446, "图文算式(20以内)": 1.0000009008193804, "图文算式(表内)": 1.0000009008193804, "加法计算(6~9)": 1.000000043429446, "减法计算(6~9)": 1.000000043429446, "图文算式(9以内)": 1.000000043429446, "归一问题（100以内）": 1.000000043429446, "减法解决归一问题": 1.000000043429446, "运用数形结合法解决简单的归一问题": 1.0000027024525358, "加法解决归总问题": 1.000000043429446, "分数除法之和倍、差倍问题": 1.000006305696425, "解决多个数是同一个数的倍数的问题": 1.000008107307159, "半价票问题": 1.000000043429446, "促销方案选择问题（多个方案）": 1.0000540458558673, "单价、数量、总价的关系应用": 1.000000043429446, "单价、数量、总价的数量关系": 1.000006305696425, "根据公式求单价": 1.0000018016368923, "根据公式求数量": 1.000000043429446, "根据公式求总价": 1.0000108097092466, "购买两物问题": 1.000000043429446, "购物中的买几送几问题": 1.0000072065027261, "购物中的满减问题": 1.0000018016368923, "经济问题(表内除法)": 1.0000495422914222, "优惠方案问题(表内除法)": 1.0000243214674547, "折扣中的盈利亏损问题": 1.0000072065027261, "绿色出行": 1.0000009008193804, "根据公式求路程": 1.000021619149443, "根据公式求时间": 1.0000072065027261, "根据公式求速度": 1.000008107307159, "解决千米相关的实际问题": 1.0000108097092466, "速度、时间、路程之间的关系": 1.000015313675358, "列方程解决简单的\"追及问题\"": 1.000000043429446, "两人合作工程问题": 1.0000243214674547, "用工程问题的思想解进出水管问题": 1.0000018016368923, "其它编码": 1.0000045040782173, "邮政编码": 1.000000043429446, "设计编码": 1.0000009008193804, "身份证编码": 1.0000009008193804, "排列中的组数问题（卡片）": 1.0000045040782173, "排列中的组数问题": 1.0001089855820422, "排列思想解决其他类型问题": 1.0001152896962717, "组数与排列(2-3个对象)": 1.0000180160326044, "方案问题(枚举全部方案)": 1.0000090081097233, "列表法解决付钱问题": 1.000000043429446, "列表法解决问题": 1.000008107307159, "与时间相关的计数": 1.000000043429446, "与算式相关的计数": 1.000000043429446, "运用列表法解决购物问题": 1.000000043429446, "运用列表法解决实际问题": 1.000000043429446, "运用列表法解决行程问题": 1.000000043429446, "搭配(2种物品)": 1.0000072065027261, "搭配综合问题": 1.0000018016368923, "两数求和(100以內)": 1.0000027024525358, "取币值": 1.0000045040782173, "握手问题": 1.000006305696425, "实际情境中的搭配": 1.0000972776985457, "组合思想解决问题": 1.0001360025701795, "比赛问题": 1.000008107307159, "体育比赛单循环赛的场数问题": 1.0000027024525358, "用体育比赛策略解决其他问题": 1.000000043429446, "比较型推理问题(三量)": 1.000006305696425, "比较型推理问题(四量)": 1.000000043429446, "比较型推理问题(有数量)": 1.0000018016368923, "九宫数独宫内排除法": 1.000000043429446, "九宫数独唯一数法": 1.000000043429446, "九宫数独行列排除法": 1.000000043429446, "列表法解决推理问题": 1.000006305696425, "排除法解决推理问题": 1.000017115248724, "四宫数独宫内排除法": 1.000000043429446, "四宫数独唯一数法": 1.000000043429446, "四宫数独行列排除法": 1.000000043429446, "集合概念": 1.0000027024525358, "体育比赛中的集合问题": 1.000000043429446, "用韦恩图表示集合": 1.0000072065027261, "运用集合的知识解决较复杂问题（有圈外部分）": 1.000006305696425, "运用集合的知识解决简单问题（无圈外部分）": 1.0000243214674547, "沏茶问题": 1.000045939406241, "合理安排时间": 1.000006305696425, "烙饼问题": 1.0000504430080472, "田忌赛马": 1.0000144128858726, "电话联络问题": 1.000000043429446, "复杂打电话问题": 1.000033329072723, "基础打电话问题": 1.0000495422914222, "方案问题(最优解)": 1.0000054048882554, "租船/车问题": 1.0000720596466492, "租船问题与省钱方案": 1.0000072065027261, "买票问题": 1.0000477408525679, "优惠方案问题(买门票、购物等)": 1.000009908910419, "列表法解决鸡兔同笼": 1.0000036032663107, "假设法解决鸡兔同笼": 1.0000198175947606, "\"百僧分馍\"问题": 1.000000043429446, "倒扣型鸡兔同笼": 1.000040535022426, "列方程解决\"鸡兔同笼问题\"": 1.000000043429446, "用假设的策略解决倍比关系的实际问题": 1.000000043429446, "用假设的策略解决相差关系的实际问题": 1.000000043429446, "列方程解决简单的\"盈亏问题\"": 1.000000043429446, "植树问题(两端种)": 1.000015313675358, "植树问题(两端都不种)": 1.0000027024525358, "植树问题(一端种，一端不种)": 1.0000072065027261, "封闭路线植树问题": 1.0000144128858726, "用小数乘加、乘减解决分段计费问题": 1.0000432372227401, "小数运算解决分段计费问题（小数除法）": 1.0000117105062056, "不知次品轻重找次品": 1.0000108097092466, "已知次品轻/重找次品（1个次品）": 1.0001837284755037, "找规律填数(组数)(20以内)": 1.000000043429446, "数形结合找规律": 1.0000036032663107, "完全平方和": 1.0000027024525358, "用小棒摆其他平面图形": 1.0000108097092466, "用小棒摆三角形": 1.0000045040782173, "杨辉三角": 1.0000036032663107, "运用数与形总结规律": 1.0000513437228045, "求一个笼里的鸽子数-": 1.0001729230694496, "初步认识\"鸽巢原理\"": 1.000000043429446, "较复杂\"鸽巢原理\"（与数字性质/运算有关）": 1.000000043429446, "利用\"鸽巢原理\"解决实际问题-": 1.000000043429446, "最不利": 1.0000990789319353, "球的反弹高度": 1.000000043429446, "平面图形的认识": 1.0000018016368923, "分类图形计数": 1.0000009008193804, "认识各种平面图形": 1.000017115248724, "多边形与平行四边形": 1.000000043429446, "认识四边形、五边形和六边形": 1.000000043429446, "四边形的特点": 1.0000072065027261, "多边形的切分": 1.0000009008193804, "折、剪、拼多边形": 1.000000043429446, "四边形之间的关系": 1.000006305696425, "认识线段": 1.0000090081097233, "画线段": 1.0000072065027261, "画线段、直线和射线": 1.0000045040782173, "过点画直线的规律": 1.000000043429446, "认识射线": 1.0000144128858726, "认识直线": 1.0000027024525358, "线段的再认识": 1.0000036032663107, "直线、射线、线段之间的关系": 1.0000045040782173, "两点间的距离(三角形)": 1.0000243214674547, "两点之间线段最短": 1.000000043429446, "根据垂线的知识发现平行线的关系": 1.000000043429446, "关于垂直的相关判断": 1.000006305696425, "认识垂直": 1.0000108097092466, "相交": 1.0000018016368923, "平行与垂直的综合应用": 1.0000009008193804, "关于平行的相关判断": 1.0000072065027261, "平行线间的距离处处相等": 1.000000043429446, "认识平行": 1.0000018016368923, "点到直线的距离": 1.000006305696425, "点到直线的距离应用": 1.0000117105062056, "画垂线": 1.000009908910419, "画已知直线的平行线": 1.000000043429446, "运用点到直线的距离的知识解决画已知直线的平行线的问题": 1.000000043429446, "认识角": 1.0000162144629754, "画线添角": 1.0000054048882554, "画直角": 1.0000054048882554, "认识锐角、钝角": 1.0000252222363888, "认识直角": 1.000008107307159, "认识直角、锐角、钝角": 1.0000045040782173, "角的定义": 1.0000018016368923, "看错内外圈刻度问题": 1.0000036032663107, "量指定角的度数(一边未与0刻度线重合)": 1.0000054048882554, "量指定角的度数(一边与0刻度线重合)": 1.0000027024525358, "认识角的度量单位": 1.000000043429446, "认识量角器": 1.0000018016368923, "角的大小比较(涉及角的度数)": 1.0000045040782173, "初步认识平角和周角": 1.000000043429446, "认识平角": 1.0000018016368923, "认识周角": 1.000000043429446, "角的分类(锐角、直角、钝角)": 1.000000043429446, "锐角、直角、钝角、平角和周角之间的大小关系": 1.0000108097092466, "画角": 1.0000054048882554, "用量角器画角": 1.0000126113012964, "根据三角尺计算角度": 1.0000018016368923, "三角尺拼角": 1.0000108097092466, "三角板拼角的计算": 1.0000045040782173, "用三角板画角": 1.0000009008193804, "角度直接运算": 1.0000324283206032, "认识长方形": 1.0000090081097233, "认识正方形": 1.0000009008193804, "画长方形": 1.000006305696425, "画正方形": 1.0000027024525358, "运用画垂线和平行线的方法画长方形": 1.0000018016368923, "运用平行线与正方形的特点解决画最大正方形的问题": 1.000000043429446, "比较图形的周长": 1.000021619149443, "多边形周长的简单计算": 1.0000027024525358, "拼一拼，比一比": 1.0000018016368923, "认识周长": 1.0000144128858726, "根据长方形的周长公式求长方形的长和宽": 1.0000054048882554, "长方形周长的运用": 1.0000252222363888, "篱笆围墙(长方形)": 1.000009908910419, "探究长方形的周长计算公式": 1.000000043429446, "铁丝围长方形": 1.0000036032663107, "计算长方形的周长": 1.000009908910419, "长或宽改变求周长": 1.0000036032663107, "正方形周长的应用": 1.0000054048882554, "根据正方形的周长公式求正方形的边长": 1.000006305696425, "计算正方形的周长": 1.000009908910419, "长、正方形周长互化": 1.0000054048882554, "在方格纸中画长、正方形": 1.0000144128858726, "面积单位比较大小(公顷、平方米和平方千米)": 1.000000043429446, "面积单位比较大小(公顷、平方米)": 1.000000043429446, "面积单位间的大小比较": 1.0000342298229745, "面积的大小比较": 1.000027023768652, "面积的意义": 1.000017115248724, "边长增加问题": 1.000008107307159, "改变长方形的长或宽求面积变化量": 1.0000432372227401, "解决有关面积的问题(小数点移动规律)": 1.000028825293442, "洒水车、收割机问题": 1.0000702583011938, "运用长方形面积公式解决实际问题": 1.0000675562690016, "长方形面积反求": 1.000020718373036, "长方形面积与周长的综合应用": 1.0000396342852518, "长方形面积正求": 1.0000990789319353, "改变正方形的边长求面积变化量": 1.000020718373036, "运用正方形面积公式解决实际问题": 1.0000162144629754, "正方形面积反求": 1.000008107307159, "正方形面积与周长的综合应用": 1.0000423364911701, "正方形面积正求": 1.000022519923982, "长、正方形面积综合应用": 1.0000594500715563, "平行四边形的认识": 1.000006305696425, "平行四边形的特征": 1.0000054048882554, "生活中的平行四边形": 1.0000018016368923, "平行四边形的底和高": 1.0000090081097233, "平行四边形具有不稳定性": 1.0000018016368923, "画平行四边形": 1.0000072065027261, "等底等高的平行四边形的面积": 1.0000027024525358, "运用多种方法求平行四边形的面积": 1.0000018016368923, "平行四边形高与底变化引起面积的变化": 1.0000054048882554, "平行四边形面积公式逆用": 1.0000135120945186, "平行四边形面积公式正用": 1.000022519923982, "平行四边形、长方形的面积关系（长拉平/平拉长）": 1.0000108097092466, "与平行四边形的面积和周长公式有关的问题（无法删）": 1.000000043429446, "平行四边形面积有关的实际问题（二）": 1.000000043429446, "平行四边形面积和周长的运用（求底/高/边长/周长/面积）": 1.0000036032663107, "运用平行四边形面积公式解决实际问题（一）": 1.0000027024525358, "平行四边形面积计算公式的推导": 1.0000027024525358, "梯形的概念": 1.0000072065027261, "梯形的上底、下底和高": 1.0000009008193804, "梯形的特征": 1.000000043429446, "认识等腰梯形": 1.0000018016368923, "认识直角梯形": 1.0000009008193804, "画梯形的高": 1.0000036032663107, "梯形底和高": 1.000000043429446, "等腰梯形周长相关的计算": 1.0000072065027261, "梯形面积计算公式的推导": 1.0000018016368923, "推导梯形的面积计算公式": 1.000000043429446, "堆放材料的数量": 1.0000045040782173, "平行线间图形面积问题（含梯、三、平行四边形）": 1.0000234206966525, "梯形的底变化或高变化": 1.0000126113012964, "梯形面积公式的多种应用": 1.000000043429446, "梯形面积公式应用(逆用)": 1.0000180160326044, "梯形面积公式应用(正用)": 1.000021619149443, "一面靠墙的梯形的面积问题": 1.0000090081097233, "运用梯形面积公式解决实际问题": 1.0000054048882554, "梯形、三角形、平行四边形面积综合": 1.000000043429446, "认识三角形": 1.0000792649537567, "三角形高的画法": 1.0000432372227401, "三角形的底和高": 1.000055847268569, "三角形的稳定性": 1.0000711589748554, "等腰三角形判断求周长": 1.0000594500715563, "三角形的三边关系": 1.0002350504832003, "按边分类的应用": 1.000015313675358, "按边分类三角形": 1.000008107307159, "按角分类的应用": 1.000068456948267, "按角分类三角形": 1.000062152154183, "等边三角形": 1.0000252222363888, "等腰三角形": 1.0000297260530346, "三角形的內角和的应用": 1.0000999795458285, "三角形内角和的认识": 1.0000837682099946, "多边形内角和的认识": 1.0000360313178729, "多边形内角和的应用": 1.0000108097092466, "四边形内角和的认识": 1.0000252222363888, "四边形内角和的应用": 1.000055847268569, "等底等高的三角形面积": 1.0000108097092466, "三角形面积计算公式的推导": 1.0000018016368923, "三角形面积公式的多种应用": 1.0000018016368923, "三角形面积公式逆用": 1.0000162144629754, "三角形面积公式正用": 1.0000144128858726, "三角形面积的实际运用": 1.000008107307159, "三角形与平行四边形的面积": 1.000033329072723, "三角形中的等积变形": 1.0000009008193804, "同圆(或等圆)中直径和半径的关系": 1.0000072065027261, "圆的认识": 1.0000108097092466, "圆在生活中的应用": 1.0000009008193804, "长(正)方形中的圆": 1.0000090081097233, "圆的组合图形的对称轴": 1.0000072065027261, "圆是轴对称图形": 1.0000072065027261, "圆周长的意义和测量方式": 1.0000018016368923, "圆的周长公式": 1.000022519923982, "圆周率的意义": 1.0000045040782173, "圆周率的意义(不带比)": 1.000000043429446, "求半圆的周长": 1.0000252222363888, "等周长的圆和其他图形的面积关系": 1.000006305696425, "起跑线问题": 1.0000009008193804, "确定起跑线": 1.000000043429446, "圆的周长公式逆向应用": 1.000008107307159, "圆的周长公式正向应用": 1.0000072065027261, "圆周长在生活中的实际应用": 1.0000027024525358, "圆的周长与面积的综合运用": 1.0000072065027261, "圆的面积公式推导": 1.0000144128858726, "圆的面积计算": 1.0000009008193804, "圆的面积公式逆用": 1.0000009008193804, "圆的面积公式正用": 1.000021619149443, "圆的周长、面积与半径的倍数关系(不带比)": 1.0000036032663107, "圆环面积的实际应用（考法需和圆环面积计算重整）": 1.0000027024525358, "圆环面积的计算": 1.000015313675358, "变形的方中圆（考法需重整）": 1.000021619149443, "变形的圆中方": 1.0000072065027261, "多个圆柱缠绕1周求绳长": 1.000000043429446, "求与圆有关的不规则图形的周长": 1.0000090081097233, "求与圆有关的不规则图形的面积": 1.0000117105062056, "最大活动范围计算": 1.0000027024525358, "扇形的认识": 1.0000045040782173, "时钟问题中弧长的计算": 1.0000009008193804, "扇环的面积": 1.0000009008193804, "扇形的面积": 1.0000045040782173, "时钟问题中面积的计算": 1.0000009008193804, "圆与扇形中的容斥原理求面积": 1.000000043429446, "用圆规画指定大小的圆（可删）": 1.000000043429446, "方格中的圆": 1.000000043429446, "在方格纸上比较图形的面积": 1.000000043429446, "多边形周长的计算": 1.0000009008193804, "平面图形的周长": 1.000000043429446, "组合图形中的面积问题综合": 1.0000072065027261, "利用差不变求组合图形的面积": 1.0000009008193804, "运用分割法求组合图形的面积": 1.0000009008193804, "分割法求不规则图形面积": 1.000027023768652, "添补法求不规则图形的面积": 1.0000027024525358, "拼接后求周长": 1.000028825293442, "巧求面积（割补法、平移法、整体减空白法）": 1.0000360313178729, "巧求长、正方形修路问题": 1.000022519923982, "用平移解决面积问题": 1.0000585493736114, "用平移解决周长问题": 1.0000144128858726, "运用差不变求组合图形的面积": 1.000008107307159, "估算不规则图形的面积": 1.0000072065027261, "钉子板上的多边形": 1.000000043429446, "平面图形的面积": 1.0000009008193804, "运用整体减空白法求面积": 1.000008107307159, "从不同方向观察同一几何体(正、长方体)": 1.0000072065027261, "从不同方向观察同一物体": 1.0000009008193804, "从不同位置看几何体": 1.0000018016368923, "从不同方向观察简单物体": 1.0000036032663107, "从相对位置观察同一物体": 1.000000043429446, "从不同方向观察两个物体的组合体": 1.000000043429446, "从不同位置观察同一几何组合体": 1.0001053831899678, "从多个方向看到的形状相同的几何组合体": 1.000020718373036, "从同一位置观察多个不同几何组合体": 1.0000999795458285, "与添加/去掉一个小正方体后相关的几何组合体问题": 1.0000315275666152, "根据立体图形画出平面图形(观察物体二)": 1.0000324283206032, "根据立体图形画出平面图形(观察物体三)": 1.000022519923982, "从两个方向看用小正方体摆出相应组合体": 1.000027023768652, "从三个方向看用小正方体摆出相应组合体": 1.00003693206252, "从一个方向看用小正方体摆出相应组合体-": 1.0000675562690016, "确定立体图形的摆法-两个方向": 1.0000108097092466, "确定立体图形的摆法-一个方向": 1.0000378328052988, "从两个方向看组合体确定小正方体数量": 1.0000252222363888, "从三个方向看组合体确定小正方体数量": 1.0000378328052988, "根据从两个方向看到的图形推测几何组合体": 1.0000009008193804, "根据从某个方向看到的图形推测几何组合体": 1.000000043429446, "根据从三个方向看到的图形推测几何组合体": 1.0000018016368923, "根据平面图形确定立体图形": 1.000028825293442, "根据看到的立体图形的一个面推测立体图形的形状": 1.0000036032663107, "根据积木上的数字还原几何体": 1.0000315275666152, "由观察到的图形确定立体图形需要的小正方体数量": 1.000021619149443, "找出物品对应的形状": 1.0000009008193804, "立体图形的比较": 1.000000043429446, "立体图形的认识": 1.000000043429446, "长方体的认识": 1.0000765629776, "长方体的特征": 1.000000043429446, "长方体棱长和": 1.0000738609846334, "长方体棱长和的实际问题": 1.0000324283206032, "正方体的认识": 1.0000720596466492, "正方体和长方体的综合运用": 1.000021619149443, "正方体棱长和": 1.0000351305713577, "正方体棱长和的实际问题": 1.000008107307159, "正(长)方体的展开图中相对面的判定": 1.0000117105062056, "正方体的展开图": 1.0000945758344542, "长方体的展开图": 1.0000810662618551, "长方体表面积（考法需整合）": 1.0001089855820422, "生活中的长方体面积问题": 1.0001387041765573, "正方体表面积": 1.0000477408525679, "生活中的正方体面积问题": 1.000009908910419, "长方体棱长变化的运用-": 1.000030626810759, "长方体体积的计算": 1.0001837284755037, "长方体体积公式实际应用": 1.0000702583011938, "正方体体积的计算": 1.0000378328052988, "正方体体积公式实际应用": 1.0000027024525358, "容积有关的复杂实际应用": 1.0000477408525679, "两面涂色": 1.0000036032663107, "没有涂色": 1.0000009008193804, "三面涂色": 1.0000009008193804, "一面涂色": 1.0000009008193804, "卷一卷、折一折": 1.000000043429446, "圆柱的底面、侧面、高、截面": 1.000030626810759, "圆柱的形成-": 1.0000072065027261, "单个圆柱捆扎问题": 1.0000108097092466, "圆柱的展开图认识": 1.000040535022426, "由圆柱表面展开图求体积": 1.0000054048882554, "侧面积的实际应用": 1.0000261230034544, "求圆柱侧面积": 1.0000297260530346, "圆柱底面积的运用-": 1.000000043429446, "圆柱侧面积的变化规律": 1.0000072065027261, "公式法求圆柱表面积": 1.0000531451467143, "挖圆柱的表面积问题-": 1.0000027024525358, "圆柱高变化与表面积问题-": 1.0000162144629754, "圆柱的表面积的较复杂应用": 1.0000189168146167, "圆柱的表面积的应用": 1.0000180160326044, "圆柱的占地面积": 1.0000045040782173, "圆柱的展开图求表面积": 1.0000108097092466, "圆柱体积的变化": 1.0000090081097233, "圆柱体积（容积）实际应用（装水、流速、短板、包装盒）": 1.000000043429446, "圆柱体积公式的逆用": 1.0000090081097233, "圆柱体积公式的正用": 1.0000720596466492, "圆柱体积公式推导": 1.0000234206966525, "长方形旋转形成的圆柱体积计算": 1.0000135120945186, "平面图形的旋转（圆柱、圆锥、组合体...）": 1.0000018016368923, "圆锥的认识（底面、侧面、高）": 1.0000342298229745, "圆锥的形成": 1.0000108097092466, "圆锥的展开图（含底面量计算）-": 1.000017115248724, "圆柱与圆锥的关系（公式/辨析/找图）-": 1.0000234206966525, "由体积和/差，求等底等高圆柱、圆锥体积": 1.0000432372227401, "圆锥体积有关的实际问题（纸盒、倒水）": 1.000000043429446, "圆锥的截面": 1.0000072065027261, "圆锥体积公式的正用": 1.0000324283206032, "圆锥体积公式的逆用": 1.0000252222363888, "小正方体堆叠求面积": 1.000061251461842, "立体图形的表面积": 1.000000043429446, "含圆柱的立体组合图形的表面积": 1.0000126113012964, "小正方体堆叠求体积": 1.000063953533261, "立体图形的体积": 1.0000009008193804, "含圆柱的立体组合图形的体积(叠放型)": 1.0000072065027261, "含圆柱的立体组合图形的体积(挖去型)": 1.000006305696425, "含圆锥的组合体的体积（叠放型）": 1.000009908910419, "复杂水中浸物（求溢出水的体积）": 1.0000450386802755, "排水法求物体体积（方法/步骤）（考法需整合）": 1.0000981783161744, "稍复杂水中浸物": 1.000021619149443, "圆柱中的排水法（单个物体）-": 1.0000468401303384, "利用\"等积变形\"的数学思想解决问题": 1.000000043429446, "瓶中水（含圆柱的正倒放）": 1.0000378328052988, "不规则图形折、剪、拼": 1.000000043429446, "规则图形折、剪、拼": 1.000017115248724, "长方形的剪拼": 1.0000018016368923, "正方形的剪拼": 1.0000018016368923, "给定条件分割四边形的问题": 1.0000045040782173, "角度的简单计算--对折": 1.0000027024525358, "角度的简单计算--折叠": 1.0000072065027261, "角度的简单计算--重叠": 1.0000045040782173, "绳长对折问题": 1.000006305696425, "轴对称中对折问题": 1.0000144128858726, "需要添加几个小正方体": 1.0000072065027261, "长正方体切割的表面积问题（考法需整合）": 1.0000567479721179, "平行底面切/拼圆柱的表面积": 1.0000144128858726, "平行底面切/拼圆柱的体积计算": 1.0000189168146167, "竖切圆柱体的表面积": 1.0000180160326044, "竖切圆柱体积的计算": 1.0000108097092466, "斜切圆柱的体积": 1.0000090081097233, "削成最大的圆柱": 1.0000135120945186, "圆柱体切拼成近似长方体的体积": 1.0000144128858726, "竖切圆锥表面积的变化-": 1.0000072065027261, "初步认识轴对称图形": 1.0000252222363888, "认识轴对称图形和对称轴(平面图形)": 1.0000324283206032, "生活中的对称现象": 1.0000351305713577, "轴对称图形的性质及应用": 1.000017115248724, "镜子问题": 1.0000072065027261, "钟表中的对称问题": 1.0000036032663107, "剪纸的分辨": 1.0000117105062056, "如何得到剪纸图案": 1.0000423364911701, "华容道": 1.000000043429446, "确定平移的方向和距离": 1.0000531451467143, "认识平移": 1.0000243214674547, "生活中的平移": 1.0000090081097233, "图形的平移": 1.0000414357577323, "生活中的旋转": 1.0000072065027261, "通过旋转解决实际问题": 1.0001179914315002, "图形的旋转": 1.0000324283206032, "旋转的含义": 1.0000180160326044, "基础图形的运动-": 1.0000198175947606, "旋转与重合": 1.0000351305713577, "旋转中转盘问题": 1.000006305696425, "对折法数画对称轴": 1.0000162144629754, "画出轴对称图形的对称轴": 1.000000043429446, "根据对称轴补全轴对称图形": 1.0000423364911701, "在方格纸上画平移图形": 1.0000378328052988, "图形旋转的方向和角度": 1.0000927745823882, "判断/绘制图形旋转90°的图形": 1.0000954764576857, "利用平移和旋转探究图形变化的方法": 1.000093675209355, "在方格纸上画出对称，旋转、平移后的图形（考法需整合）": 1.000054946563152, "图形的运动（轴对称、平移、旋转）": 1.000009908910419, "画出与圆有关的图案": 1.0000018016368923, "不同位置观察物体的范围": 1.000000043429446, "物体影长的变化规律及画法": 1.000000043429446, "判断连续拍摄的一组照片拍摄的先后顺序": 1.000000043429446, "拍摄点与照片的位置关系": 1.000000043429446, "判断物体所在的位置(前后)": 1.0000027024525358, "物体之间的相对位置(前后)": 1.000000043429446, "序数描述位置关系(前后)": 1.000000043429446, "判断物体所在的位置(上下)": 1.000000043429446, "物体之间的相对位置(上下)": 1.000000043429446, "序数描述位置关系(上下)": 1.000000043429446, "判断物体所在的位置(左右)": 1.0000045040782173, "生活中的左右": 1.0000009008193804, "物体之间的相对位置(左右)": 1.000000043429446, "序数描述位置关系(左右)": 1.000000043429446, "前后、左右、上下的综合问题": 1.000000043429446, "人或物的位置综合": 1.000000043429446, "空间位置关系": 1.000000043429446, "位置推理": 1.000000043429446, "位置应用": 1.000000043429446, "行进路线描述": 1.000000043429446, "辨认东、西、南、北四个方向": 1.0000711589748554, "根据描述确定位置（八个方向）": 1.0000297260530346, "相对方向(四个方向)": 1.000065754904867, "运用推理法解决方格图中的方位问题（四个方向）": 1.0000018016368923, "在平面图上辨认东、南、西、北": 1.0000693576256643, "认识东北、东南、西北、西南四个方向": 1.0000324283206032, "相对方向(八个方向)": 1.000093675209355, "运用推理法解决物体移动的位置与方向问题": 1.000008107307159, "在地图或平面示意图上辨认方向": 1.0000702583011938, "路线图中的数对问题": 1.000006305696425, "认识数对": 1.0000198175947606, "示意图中物体的位置": 1.0000036032663107, "数对与棋盘中的位置": 1.0000036032663107, "数对与轴对称": 1.000000043429446, "根据数对确定位置": 1.0000117105062056, "具体情境中物体的位置": 1.000008107307159, "数对与平移": 1.0000090081097233, "数对与图形的顶点": 1.0000126113012964, "数对与位置变化的规律": 1.0000054048882554, "数对与行列": 1.0000108097092466, "八个方向描述简单的行走路线": 1.0001071843897402, "描述路线图": 1.0000072065027261, "绘制路线图": 1.000006305696425, "用方向和距离描述某个点的位置": 1.0000324283206032, "用方向和距离确定某个点的位置": 1.000015313675358, "图形与位置": 1.0000036032663107, "残余刻度量长度（厘米）": 1.0000027024525358, "测量线段（厘米）": 1.0000009008193804, "测量折线长度": 1.000000043429446, "描述其他物品的长度和高度": 1.0000027024525358, "测量方式": 1.0000054048882554, "直尺测量（厘米）": 1.0000072065027261, "认识厘米": 1.0000126113012964, "认识米": 1.0000009008193804, "认识分米": 1.0000072065027261, "认识毫米": 1.0000108097092466, "千米的认识": 1.0000126113012964, "单位换算(米和厘米)": 1.0000072065027261, "米、厘米的应用": 1.000006305696425, "长度的简单计算问题(米、厘米)-": 1.0000009008193804, "长度与比较(米和厘米)": 1.0000018016368923, "分米、厘米和毫米之间的单位换算": 1.0000090081097233, "分米、厘米和毫米之间的单位计算": 1.0000018016368923, "解决有关分米、厘米、毫米的问题": 1.0000072065027261, "千米和米的大小比较": 1.0000027024525358, "千米与米的换算": 1.0000027024525358, "选择合适的长度单位(米和厘米)": 1.000021619149443, "选择合适的长度单位(分米、毫米)": 1.000017115248724, "选择合适的长度单位(千米、米、厘米、毫米)": 1.000006305696425, "估一估，量一量(米)": 1.000000043429446, "量一量，比一比": 1.0000027024525358, "估计距离/路程": 1.000000043429446, "认识公顷": 1.000000043429446, "认识公顷和平方千米(含小数)": 1.000000043429446, "公顷、平方米和平方千米的单位换算与计算": 1.000000043429446, "公顷与平方米的换算与计算": 1.000000043429446, "图形的面积计算并换算(公顷、平方米和平方千米)": 1.000000043429446, "与公顷、平方千米有关的实际问题": 1.000000043429446, "公顷、平方米和平方千米的单位换算(含小数)": 1.000000043429446, "面积单位换算应用": 1.0000513437228045, "面积单位间的换算": 1.0000963770790496, "与公顷有关的实际问题": 1.000000043429446, "选择合适的面积单位(公顷、平方千米)": 1.0000126113012964, "认识面积单位": 1.0001278976502102, "含容积单位的比大小": 1.000000043429446, "含体积单位的比大小": 1.0000117105062056, "容量的大小比较": 1.000000043429446, "升与毫升的大小比较": 1.000000043429446, "解决升与毫升的实际问题": 1.000000043429446, "认识毫升": 1.000000043429446, "认识容积单位": 1.0000585493736114, "认识容量": 1.0000009008193804, "认识升": 1.000000043429446, "容积的运用": 1.0000117105062056, "容量与分数": 1.000000043429446, "升与毫升在生活中的应用": 1.000000043429446, "体积单位的认识": 1.0000711589748554, "体积定义": 1.000027023768652, "含体积单位的计算": 1.0000018016368923, "体积单位间的进率和换算": 1.0000495422914222, "含容积单位的计算": 1.0000072065027261, "容积单位的换算": 1.000060350767633, "升与毫升的换算": 1.000000043429446, "升与立方分米的关系": 1.000000043429446, "填合适的单位(容积）": 1.0000360313178729, "按不同标准分类、计数(初阶)": 1.000000043429446, "按给定标准分类(初阶)": 1.000000043429446, "按给定标准分类计数（初阶）": 1.000000043429446, "按要求分类(100以内数的比较)": 1.000000043429446, "按指定标准分类(初阶)": 1.000000043429446, "根据分类结果判断分类依据": 1.000000043429446, "自选标准分类计数": 1.000000043429446, "旅游": 1.000000043429446, "认识象形统计图": 1.000000043429446, "象形统计图和统计表": 1.0000009008193804, "用\"正\"字记录数据": 1.000000043429446, "用调查法收集数据及认识简单的统计表": 1.000027924531981, "制作复式统计表": 1.0000414357577323, "认识复式统计表": 1.0000045040782173, "以一当一的条形统计图": 1.0000252222363888, "以一当多的条形统计图": 1.0000198175947606, "以一当二的条形统计图": 1.0000045040782173, "以一当五的条形统计图": 1.000015313675358, "读取复式条形统计图并回答问题": 1.0000144128858726, "复式条形统计图综合应用": 1.0001278976502102, "绘制复式条形统计图": 1.000000043429446, "认识复式条形统计图": 1.0000009008193804, "根据单式折线统计图分析、预测": 1.0000972776985457, "绘制单式折线统计图": 1.0000108097092466, "认识折线统计图及其特点": 1.000055847268569, "根据复式折线统计图分析、预测": 1.0001260965363423, "绘制复式折线统计图": 1.0000180160326044, "认识复式折线统计图及其特点": 1.0000243214674547, "根据数据绘制扇形统计图": 1.000009908910419, "解决扇形统计图圆心角的度数问题": 1.0000018016368923, "认识扇形统计图": 1.0000072065027261, "选择合适的统计图": 1.0000324283206032, "公式法求平均数": 1.0000738609846334, "认识平均数": 1.0000090081097233, "小数运算解决平均数问题": 1.0000009008193804, "移多补少求平均数": 1.0000045040782173, "在统计表中计算平均数": 1.00003693206252, "根据统计表解决问题": 1.0000819669131027, "统计表综合问题": 1.0000027024525358, "根据表中数据解决问题": 1.0000783642969056, "根据数据完成统计表和统计图": 1.0000018016368923, "节约用水": 1.0000045040782173, "扇形统计图和条形统计图的比较": 1.0000180160326044, "扇形统计图中获取信息填空-（考法需整合）": 1.000027924531981, "分段整理数据": 1.0000018016368923, "分段整理数据，绘制单式统计图(表)": 1.000000043429446, "运用分段整理数据解决实际问题": 1.0000018016368923, "分组整理数据，绘制复式统计图(表)": 1.000000043429446, "平面图上某点运动结合s-t图形求面积-": 1.0000009008193804, "行程折线图": 1.000008107307159, "运行图中的行程问题（单式折线图）": 1.000054946563152, "不确定现象和确定现象": 1.0000009008193804, "摸球问题": 1.000015313675358, "抛硬币问题": 1.0000072065027261, "三种事件的认识": 1.000000043429446, "事件发生的不确定性和确定性": 1.0000126113012964, "根据可能性的大小设计方案": 1.0000054048882554, "判断事件发生的可能性的大小": 1.0000567479721179, "可能性判断游戏规则的公平": 1.000009908910419, "根据可能性大小进行推测": 1.0000027024525358, "卡片求和问题": 1.0000018016368923, "运用列表法解决可能性问题": 1.0000036032663107, "探索两位数乘11的计算规律": 1.000008107307159, "算式找规律问题": 1.000000043429446, "找规律(算式)": 1.000000043429446, "找规律(100以内)": 1.0000027024525358, "找规律填数(两位数加一位数、整十数)": 1.000000043429446, "找规律填数(两位数减一位数、整十数)": 1.000000043429446, "递减规律": 1.000000043429446, "递增规律": 1.000000043429446, "找规律填数（20以内）": 1.0000018016368923, "找规律填数(10000以内)": 1.000008107307159, "找规律填数(1000以内)": 1.000015313675358, "找规律(2-6的乘法口诀)": 1.000000043429446, "根据规律写小数": 1.0000108097092466, "根据规律补全图形或数": 1.0000009008193804, "根据规律判断第几个图形是什么": 1.0000018016368923, "图形找规律(形状、颜色)": 1.0000036032663107, "找规律(图形)": 1.0000036032663107, "找规律画图": 1.0000018016368923, "位置变化规律": 1.000000043429446, "框数游戏": 1.000000043429446, "数表中规律": 1.000006305696425, "间隔问题(20以内)": 1.0000045040782173, "三位数加三位数的巧算": 1.0000018016368923, "三位数减三位数的巧算": 1.0000036032663107, "巧求\"头同尾合十\"的两位数乘两位数": 1.000000043429446, "巧求\"尾同头合十\"的两位数乘两位数": 1.000000043429446, "有趣的乘法计算": 1.000008107307159, "小数乘除简算": 1.0000018016368923, "分数裂差": 1.000000043429446, "表内除法错中求解": 1.0000126113012964, "除数是两位数除法的错中求解问题": 1.0000036032663107, "错中求解(乘除法各部分间的关系)": 1.000015313675358, "错中求解(加减法各部分间的关系)（万以内）": 1.000008107307159, "错中求解(表内混合运算)": 1.0000144128858726, "简单小数的加法和减法错中求解": 1.0000324283206032, "错中求解(两位数、几百几十数)": 1.000006305696425, "三位数乘两位数的错中求解问题": 1.000000043429446, "万以内的加法和减法错中求解": 1.0000009008193804, "数形结合正方形数": 1.0000144128858726, "连续自然数求和的巧算": 1.000000043429446, "极限思想": 1.0000027024525358, "数的大小比较": 1.000000043429446, "变化后变相同(100以内)": 1.000000043429446, "不等化等(1-5)": 1.0000036032663107, "移多补少(结合倍)": 1.0000027024525358, "移多补少(10以内)": 1.0000018016368923, "移多补少(求多多少)(100以内)": 1.0000027024525358, "移多补少(求给多少)(100以内)": 1.0000036032663107, "移多补少(求原来)(100以内)": 1.0000072065027261, "移多补少（20以内）": 1.0000027024525358, "用不同的估算策略解决实际问题": 1.0000036032663107, "除号后添/去括号": 1.0000009008193804, "带括号的四则混合运算中的趣味题目": 1.000008107307159, "减号后添/去括号": 1.0000090081097233, "巧填算符(不含中括号)": 1.000006305696425, "解决添运算符号的问题": 1.0000018016368923, "巧填算符(表内混合运算)": 1.0000036032663107, "巧填算符(100以内)": 1.0000009008193804, "填算符": 1.0000018016368923, "填算符(加减乘2-6)": 1.0000009008193804, "填算符(加减乘除1-9)": 1.0000054048882554, "填算符(加减乘除2-6)（移题后删）": 1.0000054048882554, "不等式方框里最大能填几": 1.000000043429446, "不等式填空(100以内)": 1.000000043429446, "括号里最大能填几": 1.000000043429446, "不等式方框里能填几": 1.000000043429446, "三位数乘两位数积的大小比较": 1.0000009008193804, "算式比大小(10以内)": 1.0000018016368923, "算式比大小(20以内不进退位)": 1.000000043429446, "算式比大小(20以内退位减)": 1.000000043429446, "算式比大小(5和2)": 1.000000043429446, "算式比大小(两位数加一位数、整十数)": 1.000000043429446, "算式比大小(两位数减一位数、整十数)": 1.000000043429446, "算式比大小(20以内进位加)": 1.0000036032663107, "除法竖式谜": 1.000017115248724, "几百几十数加、减几百几十数竖式谜": 1.0000027024525358, "两、三位数乘一位数的竖式谜": 1.000000043429446, "两位数乘两位数的数字谜": 1.0000162144629754, "万以内加法竖式谜": 1.0000027024525358, "万以内减法竖式谜": 1.000000043429446, "小数乘法竖式谜": 1.0000009008193804, "除数是小数的小数除法竖式谜": 1.000000043429446, "小数加法竖式谜": 1.0000126113012964, "小数减法竖式谜": 1.0000009008193804, "有关0的除法竖式谜": 1.000000043429446, "两、三位数乘一位数的填数问题": 1.000000043429446, "运用推理法解决除法竖式谜问题": 1.000000043429446, "不等式横式谜(乘法口诀)": 1.000006305696425, "乘除法横式谜(1-9)": 1.000028825293442, "乘法横式谜(表内2-6）": 1.000000043429446, "乘法横式谜(表内)": 1.000000043429446, "除法横式谜(表内2-6)": 1.0000252222363888, "除数是一位数的填数问题": 1.0000504430080472, "横式谜(1-10)": 1.000000043429446, "横式谜(11-20)": 1.0000009008193804, "横式谜(20以内)": 1.0000045040782173, "加减法横式谜(100以内)": 1.0000072065027261, "加减法一元推算(20以内)": 1.0000009008193804, "数图找规律(加减乘除1-9)": 1.000000043429446, "小数加法数字谜": 1.0000378328052988, "小数减法数字谜": 1.000015313675358, "横式谜(20以内进位加)": 1.0000018016368923, "三阶幻方和数阵图(100以内)": 1.000000043429446, "封闭型数阵图(100以内)": 1.0000018016368923, "封闭型数阵图(1-10)": 1.0000036032663107, "封闭型数阵图(20以内)": 1.0000036032663107, "封闭型数阵图(整百数)": 1.0000072065027261, "辐射型数阵图(100以内)": 1.000000043429446, "辐射型数阵图(1-10)": 1.000000043429446, "辐射型数阵图(20以内)": 1.000000043429446, "数阵图(100以上)": 1.000000043429446, "填数游戏": 1.000000043429446, "整数": 1.000000043429446, "奇偶性在实际问题中的应用": 1.0000315275666152, "数角(角的初步认识)": 1.0000054048882554, "分类枚举法数三角形": 1.0000018016368923, "分类枚举数平行四边形": 1.000000043429446, "简单平面图形计数": 1.000020718373036, "平行四边形计数": 1.0000027024525358, "数平行线和垂线": 1.000008107307159, "数三角形": 1.000021619149443, "数四边形": 1.0000009008193804, "数线段、直线和射线": 1.0000144128858726, "数线段-解决实际问题": 1.0000009008193804, "数线段与连线计数": 1.000000043429446, "四边形计数(有序思考)": 1.000000043429446, "梯形计数": 1.0000027024525358, "与图形相关的计数": 1.000000043429446, "长方形计数": 1.0000018016368923, "正方形计数": 1.000000043429446, "正方形数": 1.0000027024525358, "数射线": 1.000000043429446, "枚举法数长方形": 1.000000043429446, "枚举法数正方形": 1.0000018016368923, "从一个方向看组合体确定小正方体数量": 1.0000054048882554, "数组合图形小正方体的数量": 1.0000180160326044, "有隐藏的立方体": 1.000000043429446, "组合图形数小正方体数量的规律": 1.000006305696425, "用规定数字组数填数": 1.0000009008193804, "可能性": 1.0000918739535536, "平均数与数据分析": 1.0000009008193804, "统计图表的绘制与计算": 1.000000043429446, "统计图表的认识与选择": 1.000000043429446, "玩中认图形": 1.000000043429446, "不规则图形求周长": 1.0000117105062056, "改变边长求周长": 1.000000043429446, "剪后求周长": 1.000009908910419, "方格图中的面积": 1.000033329072723, "平行四边形中的等积变形": 1.000000043429446, "运用平移、\"等积变形\"解决复杂的面积问题": 1.000000043429446, "长方形和平行四边形的一半": 1.000000043429446, "七巧板拼图问题": 1.0000180160326044, "认识七巧板": 1.0000126113012964, "七巧板中的应用": 1.000000043429446, "用几块七巧板摆拼平面图形": 1.000000043429446, "相遇的行程问题": 1.0000036032663107, "往返行程问题": 1.000008107307159, "解决发车问题": 1.0000117105062056, "解决复杂应用题": 1.0000009008193804, "解决简单应用题": 1.000000043429446, "从条件出发解决实际问题": 1.000000043429446, "从条件出发解决有关四则混合运算的实际应用题": 1.000000043429446, "从条件出发解决综合实际问题": 1.000000043429446, "解决分段计费问题": 1.0000036032663107, "从条件出发解决归一、归总问题": 1.000000043429446, "分数乘除解归一归总问题": 1.000006305696425, "解决归一归总问题": 1.000008107307159, "图示法解决问题": 1.0000009008193804, "小数运算解决归一归总问题": 1.0000045040782173, "和倍问题": 1.0000441379524418, "差倍问题": 1.000020718373036, "改动替换一个数后的平均数": 1.0000018016368923, "平均数的实际问题": 1.000022519923982, "平均数的应用": 1.0000027024525358, "用平均数比较数据的总体情况": 1.0000072065027261, "用平均数倒推": 1.000027924531981, "增加去掉一个数后的平均数": 1.0000117105062056, "单人工程问题": 1.0000027024525358, "解决工程问题": 1.000000043429446, "三人合作工程问题": 1.0000036032663107, "单双数(20以内)": 1.000000043429446, "简单页码问题": 1.0000027024525358, "求页数（20以内）": 1.0000072065027261, "根据时间规律解决问题": 1.0000009008193804, "钟面上角的计算问题": 1.0000135120945186, "间隔问题的相关应用": 1.0000180160326044, "两种物体间隔排列的规律": 1.000000043429446, "测量中的间隔问题": 1.0000009008193804, "锯木头问题": 1.0000072065027261, "敲钟问题": 1.0000036032663107, "有关时间推算的锯木头问题": 1.0000018016368923, "有关时间推算的爬楼梯问题": 1.0000009008193804, "排队问题单主角\"第\"\"第\"问题(20以内)": 1.000000043429446, "排队问题单主角\"有\"\"第\"问题(20以内)": 1.000000043429446, "排队问题单主角\"有\"\"有\"问题（20以内）": 1.000000043429446, "排队问题基础": 1.000000043429446, "双主角排队问题": 1.0000045040782173, "方阵问题": 1.000028825293442, "植树问题之爬楼梯问题": 1.0000027024525358, "串珠子问题": 1.000000043429446, "基本周期问题": 1.000063052844656, "分组法解决鸡兔同笼": 1.0000018016368923, "年龄中的倍数问题": 1.000008107307159, "利润和折扣综合求成本": 1.0000126113012964, "利润和折扣综合求定价": 1.000009908910419, "根据浓度和溶液求溶质": 1.0000054048882554, "根据溶质和浓度求溶液": 1.0000009008193804, "两种不同浓度溶液混合问题": 1.000000043429446, "浓度的认识": 1.0000009008193804, "浓度问题中的增加溶质或溶剂问题": 1.0000036032663107, "浓度问题中的蒸发问题": 1.0000009008193804, "报数问题的必胜策略": 1.0000027024525358, "过河问题": 1.0000072065027261, "排队问题--求最短时间": 1.0000072065027261, "找规律综合": 1.0000018016368923, "组数求积的最大值或最小值(不含0)(三位数乘两位数)": 1.000000043429446, "组数求积的最大值或最小值(含0)(三位数乘两位数)": 1.0000009008193804, "巧用\"差\"解决问题": 1.000000043429446, "抓住不变量解决连瓶重问题": 1.000000043429446, "运用多种策略解决问题": 1.000000043429446, "测量中的重叠问题": 1.0000054048882554, "容斥法求重叠图形的面积": 1.000000043429446, "读取秤上数值": 1.000009908910419, "分析法": 1.000000043429446, "列表法": 1.000000043429446, "数形结合法": 1.000000043429446, "体验统一长度单位": 1.000000043429446, "初步认识立体图形": 1.0000180160326044, "立体图形的稳定性（单个图形）": 1.0000018016368923, "多种立体图形的拼搭": 1.0000072065027261, "相同正方体拼搭": 1.0000180160326044, "2-6的乘加、乘减计算": 1.000000043429446, "2-6的乘加乘减应用题": 1.0000009008193804, "7的乘加、乘减计算": 1.0000054048882554, "7的乘加乘减应用题": 1.0000162144629754, "8的乘加、乘减计算": 1.0000009008193804, "8的乘加乘减应用题": 1.000020718373036, "巧用小数乘法运算律进行简算": 1.000021619149443, "小数四则混合运算（乘法运算律）": 1.000028825293442, "9的乘法应用题（旧版）": 1.000000043429446, "9的乘加、乘减计算": 1.0000054048882554, "分类枚举法数角(角的认识)": 1.0000045040782173, "有余数的小数除法（除数是整数）": 1.000000043429446, "除数是整数的基本算理及算法9": 1.0000018016368923, "除数是整数的小数除法计算9": 1.000008107307159, "判断商与1的大小关系（除数是整数的小数除法）9": 1.0000009008193804, "除数是整数的小数运算比大小9": 1.0000009008193804, "除数是整数的小数除法运用9": 1.0000036032663107, "商不变的性质（小数除法）-": 1.0000144128858726, "除数是小数的除法计算9": 1.0000144128858726, "除数是小数的除法运用9": 1.0000045040782173, "除数是小数的算式比大小9": 1.0000072065027261, "小数运算解决错中求解问题（除数是整数）": 1.0000018016368923, "小数运算解决错中求解问题（除数是小数）": 1.0000027024525358, "解含有两个未知数的方程": 1.0000126113012964, "化简含字母的式子": 1.000030626810759, "线段图与方程": 1.0000072065027261, "小数乘小数的运用-": 1.000006305696425, "数的组成(亿以上)": 1.0000018016368923, "计算工具的认识": 1.0000036032663107, "小数乘整数的算理、算法-": 1.000000043429446, "小数乘整数的运用-": 1.000000043429446, "错中求解（小数乘整数）9": 1.0000027024525358, "错中求解（含字母/未知数）": 1.0000036032663107, "用字母表示自然数9": 1.000021619149443, "数线图与字母9": 1.0000009008193804, "油桶问题(两、三位数的加减法)": 1.0000027024525358, "利润与折扣的综合问题": 1.0000045040782173, "接送问题": 1.000000043429446, "环形路线": 1.000000043429446, "行程中的变速及平均速度问题": 1.000000043429446, "火车过桥问题": 1.0000009008193804, "流水行船问题": 1.0000009008193804, "错车问题": 1.000000043429446, "扶梯问题": 1.000000043429446, "买票问题(多位数乘一位数)": 1.0000072065027261, "三角形与长/正方形的面积": 1.0000045040782173, "图形中的百分数问题": 1.000000043429446, "行程中的百分数问题": 1.000006305696425, "表与时间": 1.0000009008193804, "商是循环小数的算式的规律": 1.000000043429446, "一亿有多大": 1.0000162144629754, "错中求解(100以内)": 1.000022519923982, "尺规作图": 1.0000018016368923, "10以内加减法中的运算规律": 1.000000043429446, "分数乘整数的运用": 1.0000018016368923, "解小数方程": 1.0000036032663107, "含圆的组合图形的面积": 1.000033329072723, "铁丝围正方形": 1.0000027024525358, "画三角形": 1.0000144128858726, "时间比较(几时几分)": 1.0000036032663107, "加法应用题(不进位加)(100以内)": 1.0000036032663107, "笔算两位数加一位数(不进位)": 1.000006305696425, "不进位/进位加法的计算运用": 1.000000043429446, "减法应用题(不退位)(100以内)": 1.0000018016368923, "两位数加减法的计算运用(100以内)": 1.000000043429446, "连加、连减、加减混合的应用(100以内)": 1.000000043429446, "整数乘加、乘减运算": 1.000000043429446, "百分数、分数、小数、比之间的互化": 1.000006305696425, "表内乘法口诀（1-9）-": 1.000000043429446, "购物问题(100以内)": 1.0000036032663107, "不退位/退位减法的计算运用": 1.0000018016368923, "比多少的计算运用(100以内)": 1.0000009008193804, "\"提问题\"、\"填条件\"问题(100以内)": 1.000000043429446, "认识含万级的数位顺序表": 1.0000828675624827, "两数比大小(1000以内)": 1.0000018016368923, "算盘表示数(10000以内)": 1.0000009008193804, "算盘上拨数(10000以内)": 1.0000027024525358, "包含分求份数（列除法算式）": 1.000028825293442, "平均分求每份数（列除法算式）": 1.0000414357577323, "角的大小比较(初步认识角)": 1.0000036032663107, "与直角有关的应用": 1.0000027024525358, "角的初步认识的应用(直角、锐角、钝角)": 1.0000036032663107, "数阵图(几百几十数)": 1.000000043429446, "角的认识的应用(直角、锐角、钝角、平角和周角)": 1.0000018016368923, "量角应用": 1.000000043429446, "相同长方体拼搭": 1.0000018016368923, "两位数与两位数的不进位/进位加的口算运用": 1.0000009008193804, "两位数与两位数不退位/退位减的口算运用": 1.0000009008193804, "两位数加减的口算运用": 1.0000018016368923, "两位数加减的口算应用题": 1.0000018016368923, "乘法意义的应用题": 1.0000018016368923, "由增减成数，求量间百分率关系-": 1.0000072065027261, "含字母式子的计算9": 1.0000144128858726, "2-5的乘法口诀": 1.0000108097092466, "用字母表示含倍数关系的量9": 1.0000072065027261, "6的乘法口诀应用题": 1.000017115248724, "2-6的乘法口诀的理解（旧）": 1.000000043429446, "等量关系与方程9": 1.0000009008193804, "加法、乘法求总数问题的对比-": 1.0000108097092466, "梯形与平行四边形的面积问题": 1.000006305696425, "画梯形（知面积）-": 1.000000043429446, "6的乘加、乘减计算": 1.0000018016368923, "6的乘加乘减应用题": 1.000006305696425, "从不同方向观察简单立体图形": 1.0000072065027261, "分数列项计算": 1.0000045040782173, "解决总价问题(表内乘法)": 1.000008107307159, "扇形圆心角": 1.0000009008193804, "表内乘加、乘减计算": 1.0000009008193804, "表内乘法应用题（1-9）-": 1.0000036032663107, "表内乘法计算（1-9)-": 1.0000018016368923, "与平均数有关的实际问题（正负数）": 1.000000043429446, "体育运动中的实际问题（正负数）": 1.000008107307159, "选择合适的时间单位(时、分)": 1.0000018016368923, "根据描述确定位置（四个方向）": 1.000017115248724, "方向与路程的综合问题（八个方向）": 1.0000297260530346, "由原价、现价、售价关系求折扣": 1.000022519923982, "用不同的方法记录数据": 1.0000477408525679, "两位数除以一位数(被除数首位能被整除)解决实际问题": 1.000021619149443, "除数是一位数的估算的应用": 1.000063052844656, "几个小部分拼接成大长方体": 1.0000009008193804, "除数是一位数除法的错中求解问题": 1.0000234206966525, "利用被减数、减数、差的关系解决问题": 1.0000009008193804, "由一道乘法算式计算两道除法算式(2-6)": 1.000022519923982, "购物问题(2-6)": 1.000006305696425, "用除法解决问题": 1.0000324283206032, "乘、除法各部分间的关系的计算与应用": 1.0000513437228045, "含有小括号的四则混合运算的运算顺序": 1.000028825293442, "购物问题": 1.000009908910419, "算式比大小(整数的四则混合运算)": 1.000006305696425, "整数的四则混合运算": 1.0000783642969056, "圆柱展开图的运用（涉及圆柱底面信息、高）": 1.000027023768652, "质因数的含义": 1.000000043429446, "质数、合数、因数、倍数的运用-": 1.000022519923982, "加法运算定律和减法运算性质的综合运用": 1.000021619149443, "同级运算的计算与应用（乘除混合、连乘、连除）": 1.000027023768652, "运用乘法分配律简算": 1.0001513114507807, "两位数乘两位数的错中求解问题": 1.000021619149443, "买票问题（两位数乘两位数）": 1.000015313675358, "图文算式（两位数乘两位数）": 1.0000045040782173, "估一估（有关面积和长度）": 1.0000045040782173, "倒数的比大小9": 1.000022519923982, "平移和旋转的综合": 1.0002368511452697, "倒数的运用9": 1.0000315275666152, "乘除法的计算(表内)": 1.0000315275666152, "计算经过的天数": 1.0000756623151457, "一天时间的理解": 1.0000162144629754, "含有小括号的表内混合运算": 1.0000324283206032, "小数的含义": 1.0001125879442356, "小数的写法（一位小数）": 1.0000054048882554, "小数加减法的错中求解（一位小数）": 1.0000234206966525, "求两数公因数、最大公因数的特殊情况": 1.0001324004022, "计数器表示数(1000以内)": 1.0000117105062056, "简单分数方程（分数加减）-": 1.00003693206252, "计数器表示数(10000以内)": 1.0000072065027261, "横式谜、算式谜(整百、整千数加减法)": 1.0000072065027261, "用几百几十、整十、整百和整千数的估算解决问题": 1.0000180160326044, "找规律填数（复杂）-": 1.0000045040782173, "根据已给数找出规律继续填数9": 1.000000043429446, "鸡兔同笼变型题": 1.0001017807680121, "确定平移后的图形": 1.0000072065027261, "三角形的分类综合应用": 1.0000594500715563, "在点子图上画三角形": 1.0000144128858726, "特殊三角形内角和的应用(等腰/等边/等腰直角/直角三角形)": 1.0001657193160538, "特殊四边形内角和的应用(长方形/正方形)": 1.0000297260530346, "小数加减法": 1.000250355872822, "小数加减法应用题(涉及小数简算)": 1.0001071843897402, "立体图形中的平面图形": 1.0000432372227401, "算式比大小（十几减9）": 1.0000018016368923, "十几减9的计算": 1.0000054048882554, "十几减9的应用": 1.0000054048882554, "平十法（十几减8）": 1.0000009008193804, "破十法（十几减8）": 1.0000027024525358, "十几减8的应用": 1.000006305696425, "十几减8的计算": 1.0000054048882554, "十几减7、6的计算": 1.0000036032663107, "算式比大小（十几减7、6）": 1.0000072065027261, "十几减7、6的实际问题": 1.0000198175947606, "算式比大小（十几减5、4、）": 1.0000045040782173, "十几减5、4、3、2的应用": 1.0000090081097233, "认识计数单位": 1.0000117105062056, "数的顺序（100以内）": 1.000030626810759, "用珠子摆数": 1.000027924531981, "两位数减一位数（退位）解决实际问题": 1.000022519923982, "数量关系的比较": 1.000017115248724, "分步解决问题的策略": 1.0000360313178729, "直条统计图": 1.0000378328052988, "运用乘、除法的意义和各部分之间的关系解决实际问题": 1.0000324283206032, "运用加、减法各部分间的关系巧解算式": 1.0000243214674547, "运用加、减法的意义和各部分之间的关系解决实际问题": 1.000017115248724}