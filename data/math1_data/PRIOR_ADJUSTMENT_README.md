# Math1数据集先验概率调整

本文档说明如何为math1_points.txt中的标签生成和使用先验概率调整。

## 概述

根据图片中的先验概率计算方法，我们实现了以下公式：

```
ŝᵢ = sᵢ · log₁₀(A · pᵢ + 10)
```

其中：
- `sᵢ`: 原始分数
- `pᵢ`: 先验概率
- `A`: 调整参数（默认为1.0）
- `ŝᵢ`: 调整后的分数

## 生成的文件

运行 `scripts/prepare_math1_data.py` 后会生成以下文件：

1. **math1_priors.json**: 所有训练数据中标签的先验概率
2. **math1_points_priors.json**: math1_points.txt中每个标签的先验概率
3. **math1_points_adjustment_factors.json**: 每个标签的调整因子

## 使用方法

### 1. 生成先验概率文件

```bash
cd /home/<USER>/ZhouSQ/DCX/xmc.dspy
python scripts/prepare_math1_data.py
```

### 2. 在代码中使用

```python
from scripts.prepare_math1_data import apply_prior_adjustment_to_scores

# 原始分数字典
scores = {
    "小数的认识": 0.8,
    "分数的意义": 0.7,
    "初识1-10": 0.6
}

# 应用先验概率调整
adjusted_scores = apply_prior_adjustment_to_scores(scores, A=1.0)

# 查看结果
for label, original_score in scores.items():
    adjusted_score = adjusted_scores[label]
    print(f"{label}: {original_score} → {adjusted_score}")
```

### 3. 单个分数调整

```python
from scripts.prepare_math1_data import apply_prior_adjustment

# 调整单个分数
original_score = 0.5
prior_probability = 0.01
A = 1.0

adjusted_score = apply_prior_adjustment(original_score, prior_probability, A)
```

## 调整效果

### 统计信息

- **处理标签数**: 3,178个
- **找到先验概率的标签数**: 2,180个
- **缺失先验概率的标签数**: 998个（使用默认值1e-6）
- **调整因子范围**: 1.000000 ~ 1.003415
- **平均调整因子**: 1.000016

### 调整特点

1. **高频标签**（高先验概率）：分数会略微增加
   - 例如："小数的认识"（先验概率0.079）→ 调整因子1.0034
   
2. **低频标签**（低先验概率）：分数基本不变
   - 例如："初识1-10"（先验概率1e-6）→ 调整因子1.0000

3. **调整幅度**：通常很小（0.01%-0.35%），但能反映标签重要性

## 示例结果

| 标签 | 先验概率 | 原始分数 | 调整后分数 | 变化率 |
|------|----------|----------|------------|--------|
| 小数的认识 | 0.078945 | 0.8 | 0.802732 | +0.34% |
| 分数的意义 | 0.008774 | 0.7 | 0.700267 | +0.04% |
| 初识1-10 | 0.000001 | 0.6 | 0.600000 | +0.00% |

## 参数调整

可以通过调整参数`A`来控制先验概率的影响程度：

- `A = 0.1`: 影响很小
- `A = 1.0`: 标准影响（默认）
- `A = 5.0`: 影响较大

## 测试

运行测试脚本验证功能：

```bash
python scripts/test_prior_adjustment.py
```

## 注意事项

1. 缺失的标签会被分配默认先验概率1e-6
2. 调整幅度通常很小，主要用于微调分数
3. 高频标签会获得轻微的分数提升
4. 可以根据具体需求调整参数A的值

## 文件结构

```
data/math1_data/
├── math1_points.txt                      # 原始标签列表
├── math1_priors.json                     # 所有标签的先验概率
├── math1_points_priors.json              # math1_points标签的先验概率
└── math1_points_adjustment_factors.json  # 调整因子
```
