{"小学数学新知识树": 1.0, "数与代数": 0.703324967331107, "应用题": 0.057766899670199746, "常见的数学问题": 0.057704673207359315, "找次品": 0.004978117027234449, "已知次品轻/重找次品（1个次品）": 0.004231399473149281, "选出称量次数最少的方法": 0.004231399473149281, "补充知识点15195": 0.004231399473149281, "补充知识点15196": 0.004231399473149281, "数的运算": 0.32050776793677793, "分数的四则运算": 0.03972122544647487, "分数的加、减法": 0.022318558005434443, "异分母分数加、减法的应用": 0.004521789633071291, "异分母分数减法在实际生活的应用": 0.0014312086453299039, "求剩余部分占单位1\"的几分之几的问题\"": 0.0014312086453299039, "补充知识点7953": 0.0014312086453299039, "异分母分数加、减法": 0.003982493621787559, "异分母分数加法的计算、运用-": 0.0013482400282093297, "直接计算": 0.0035469083819045445, "补充知识点7932": 0.0013482400282093297, "异分母分数减法的计算、运用-": 0.000580780319844019, "补充知识点7944": 0.000580780319844019, "图形与几何": 0.23185580054344443, "图形与变换": 0.025699529153097842, "旋转三要素及旋转图形": 0.0021364418908547842, "图形旋转的方向和角度": 0.0021364418908547842, "求旋转角度(非90°)": 0.0021364418908547842, "补充知识点20752": 0.0021364418908547842, "补充知识点20753": 0.0021364418908547842, "补充知识点20754": 0.0021364418908547842, "旋转与旋转现象": 0.00587002966128062, "通过旋转解决实际问题": 0.002717222210698803, "观察图形旋转规律，画/找出下一个图形-": 0.002717222210698803, "补充知识点20581": 0.002717222210698803, "补充知识点20582": 0.002717222210698803, "补充知识点20583": 0.002717222210698803, "数的认识": 0.22243886250025927, "分数的认识": 0.05546452054510381, "分数化小数": 0.00545518657567775, "比较分数与小数的大小": 0.0018667938852129182, "比较分数小数大小": 0.0018667938852129182, "补充知识点3203": 0.0018667938852129182, "补充知识点3204": 0.0018667938852129182, "整数的认识": 0.0797535832071519, "因数与倍数": 0.052249486631681566, "公倍数与最小公倍数": 0.007902760780734687, "倍数、公倍数、最小公倍数-": 0.00031113231420215305, "按要求写倍数、最小公倍数-": 0.00031113231420215305, "补充知识点1562": 0.00031113231420215305, "不知次品轻重找次品": 0.00024890585136172244, "求最少称量次数": 0.00024890585136172244, "补充知识点15179": 0.00024890585136172244, "补充知识点15180": 0.00024890585136172244, "异分母分数连减在实际生活中的应用-": 0.001514177262450478, "求剩余部分占单位1\"几分之几的问题-\"": 0.001514177262450478, "补充知识点7965": 0.001514177262450478, "统计和概率": 0.03181846466574018, "统计": 0.029702764929165543, "统计图": 0.013752048287735165, "复式折线统计图": 0.003878782850386841, "根据复式折线统计图分析、预测": 0.002903901599220095, "根据复式统计图中的数据进行分析": 0.002903901599220095, "补充知识点22430": 0.002903901599220095, "补充知识点22431": 0.002903901599220095, "用最小公倍数解决实际问题": 0.004023977930347846, "用公倍数、最小公倍数解决复杂的实际问题": 0.0013067557196490428, "每隔几天去一次问题": 0.0013067557196490428, "补充知识点1574": 0.0013067557196490428, "单式折线统计图": 0.0038995250046669847, "根据单式折线统计图分析、预测": 0.0022401526622555018, "从单式折线统计图中获取信息": 0.0022401526622555018, "补充知识点22400": 0.0022401526622555018, "补充知识点22401": 0.0022401526622555018, "平移和旋转的综合": 0.00545518657567775, "利用平移和旋转探究图形变化的方法": 0.002157184045134928, "判断平移和旋转现象": 0.002157184045134928, "补充知识点20788": 0.002157184045134928, "补充知识点20789": 0.002157184045134928, "补充知识点20790": 0.002157184045134928, "平面图形": 0.07788678932193897, "三角形": 0.03308373607682894, "三角形三边关系": 0.005413702267117463, "三角形的三边关系": 0.005413702267117463, "给出任意两边长度，判断第三边(不)可能是多少(有选项)": 0.005413702267117463, "补充知识点16791": 0.005413702267117463, "补充知识点16792": 0.005413702267117463, "等腰三角形和等边三角形的认识及特征": 0.002634253593578229, "等腰三角形判断求周长": 0.0013689821824894733, "根据等腰三角形的两条边判断第三条边并求周长": 0.0013689821824894733, "补充知识点16883": 0.0013689821824894733, "补充知识点16884": 0.0013689821824894733, "小数的认识": 0.07894463919022629, "小数的近似数和改写": 0.014312086453299039, "小数的近似数": 0.013399431664972724, "把较大数改写成用万\"或\"亿\"作单位的数(小数)\"": 0.0014519507996100475, "改写与比大小": 0.0014519507996100475, "补充知识点2480": 0.0014519507996100475, "用四舍五入\"法求小数的近似数\"": 0.003920267158947128, "求小数近似数的方法": 0.003920267158947128, "补充知识点2462": 0.003920267158947128, "根据近似数求原数问题(小数)": 0.0042728837817095685, "已知近似数，求方框里可填的最大、最小数": 0.0042728837817095685, "补充知识点2446": 0.0042728837817095685, "整数的四则运算": 0.16587500777830785, "表内除法": 0.02217336292547344, "除法的初步认识": 0.004189915164588994, "初步认识除法": 0.000850428325485885, "写除法算式各部分名称+两种平均分的过程和结果": 0.000850428325485885, "补充知识点5547": 0.000850428325485885, "推理问题": 0.0007259753998050237, "排除法解决推理问题": 0.0003941009313227272, "已知两人的条件，推理其中两个人的结论": 0.0003941009313227272, "补充知识点14851": 0.0003941009313227272, "补充知识点14852": 0.0003941009313227272, "复式条形统计图": 0.0032980025305428223, "复式条形统计图综合应用": 0.002945385907780382, "通过横向复式条形统计图综合分析并解决问题": 0.002945385907780382, "补充知识点22376": 0.002945385907780382, "补充知识点22377": 0.002945385907780382, "鸡兔同笼": 0.004189915164588994, "鸡兔同笼问题": 0.003484681919064114, "鸡兔同笼变型题": 0.0023438634336562197, "鸡兔同笼问题(其他情景题)": 0.0023438634336562197, "补充知识点15052": 0.0023438634336562197, "三个量的鸡兔同笼问题": 0.00018667938852129183, "三物中两物某属性相同(如腿数)": 0.00018667938852129183, "补充知识点15073": 0.00018667938852129183, "盈亏问题": 6.222646284043061e-05, "列方程解决简单的盈亏问题\"\"": 6.222646284043061e-05, "解决盈亏问题(盈亏)": 6.222646284043061e-05, "补充知识点15077": 6.222646284043061e-05, "补充知识点15078": 6.222646284043061e-05, "倒扣型鸡兔同笼": 0.0009333969426064591, "补充知识点15050": 0.0009333969426064591, "补充知识点15051": 0.0009333969426064591, "数学竞赛": 0.031237684345896164, "算式谜，数阵，进位制": 0.008504283254858849, "横式数字谜": 0.004915890564394018, "乘除法横式谜(1-9)": 0.0006637489369645932, "用同一句乘法口诀": 0.0006637489369645932, "补充知识点24571": 0.0006637489369645932, "补充知识点24572": 0.0006637489369645932, "补充知识点24573": 0.0006637489369645932, "包含分求份数（列除法算式）": 0.0006637489369645932, "有隐藏条件的求每份数(如：份数没有直接告知)": 0.0006637489369645932, "补充知识点5552": 0.0006637489369645932, "用2-6的乘法口诀求商": 0.0032357760677023915, "连减与除法(2-6)": 0.00037335877704258366, "连减一个数，结果为0": 0.0006222646284043061, "补充知识点5616": 0.00037335877704258366, "有余数的除法": 0.012217128871004542, "有余数除法的试商": 0.0005185538570035884, "除法竖式的计算-试商法": 0.0005185538570035884, "列竖式计算带余除法": 0.0005185538570035884, "补充知识点5919": 0.0005185538570035884, "常见的量": 0.03115471572877559, "质量单位及换算": 0.0032357760677023915, "质量单位的选择": 0.0007882018626454544, "填质量单位(克和千克)": 0.0006015224741241625, "填合适的质量单位(克和千克)": 0.0006015224741241625, "补充知识点13609": 0.0006015224741241625, "补充知识点13610": 0.0006015224741241625, "轴对称的认识及辨认": 0.0029661280620605257, "轴对称图形的初步应用": 0.0004355852398830143, "添加一个正方形，使成为轴对称图形": 0.0004355852398830143, "补充知识点20311": 0.0004355852398830143, "补充知识点20312": 0.0004355852398830143, "补充知识点20313": 0.0004355852398830143, "乘除法解决问题(表内)": 0.007342722615170811, "列多个算式的应用题（2-6含除法）-": 0.0012860135653688992, "除法与加/减/乘法解决问题对比（移后删）": 0.0012860135653688992, "补充知识点5792": 0.0012860135653688992, "有隐藏条件的除法应用题(2-6)": 0.0007467175540851673, "借助画图理解题意解决较复杂除法问题": 0.0007467175540851673, "补充知识点5742": 0.0007467175540851673, "整数四则混合运算": 0.012528261185206696, "根据分步式列综合式": 0.0021364418908547842, "将分步算式改写成带小括号或中括号的综合算式": 0.0015971458795710522, "用倒推的方法改写综合算式": 0.0015971458795710522, "补充知识点6925": 0.0015971458795710522, "小数的四则运算": 0.0383522432639854, "小数的加法和减法": 0.0274418701126299, "利用小数的加、减法混合运算解决实际问题": 0.009437680197465308, "小数加减法应用题(涉及小数简算)": 0.002468316359337081, "小数加减法应用题": 0.005600381655638755, "补充知识点7331": 0.002468316359337081, "运算定律与简便运算": 0.056874987036153575, "整数除法的性质": 0.004355852398830143, "加、减、乘、除运算定律/性质的综合运用": 0.0013067557196490428, "判断简算后的等式对错/判断两个算式是否相等": 0.0013067557196490428, "补充知识点9671": 0.0013067557196490428, "补充知识点9672": 0.0013067557196490428, "小数加、减法简便运算": 0.0038580406961066975, "与小数减法相关的简便运算": 0.002841675136379664, "小数加减法简算综合": 0.0020742154280143535, "判断添括号凑整是否正确": 0.0020742154280143535, "补充知识点9711": 0.0020742154280143535, "三角形的内角和": 0.008047955860695693, "三角形内角和的认识": 0.0019290203480533488, "三角形内角和的辨析": 0.0019290203480533488, "补充知识点16967": 0.0019290203480533488, "补充知识点16968": 0.0019290203480533488, "比和比例": 0.05706166642467487, "比": 0.006409325672564353, "比的化简": 0.000891912634046172, "化连比": 0.00012445292568086122, "化简连比(分数)": 0.00012445292568086122, "补充知识点11809": 0.00012445292568086122, "补充知识点11810": 0.00012445292568086122, "利用整数四则混合运算解决问题": 0.004397336707390429, "用含有括号的四则混合运算解决实际问题": 0.0015971458795710522, "根据算式选问题/条件": 0.0015971458795710522, "补充知识点6938": 0.0015971458795710522, "多位小数的大小比较": 0.006948621683848084, "方框里填最大最小数(小数比大小)": 0.0008296861712057415, "在空缺处填最大或最小数": 0.0008296861712057415, "补充知识点2350": 0.0008296861712057415, "补充知识点2351": 0.0008296861712057415, "三角形的內角和的应用": 0.0023023791250959325, "利用三角形内角和求角(已知两角求第三角)": 0.0023023791250959325, "补充知识点16943": 0.0023023791250959325, "补充知识点16944": 0.0023023791250959325, "直线、射线、线段": 0.0016178880338511958, "两点间线段最短与两点间的距离": 0.0005600381655638754, "两点间的距离(三角形)": 0.0005600381655638754, "判断最短路线": 0.0005600381655638754, "补充知识点15573": 0.0005600381655638754, "补充知识点15574": 0.0005600381655638754, "三角形的高及画法": 0.002281636970815789, "三角形高的画法": 0.0009956234054468898, "画锐角/直角/钝角三角形的三条高": 0.0009956234054468898, "补充知识点16755": 0.0009956234054468898, "补充知识点16756": 0.0009956234054468898, "多边形的内角和": 0.003629876999025119, "多边形内角和的认识": 0.0008296861712057415, "求多边形的内角和": 0.0008296861712057415, "补充知识点17011": 0.0008296861712057415, "补充知识点17012": 0.0008296861712057415, "整数、小数、分数、百分数的简便运算": 0.007280496152330381, "运用乘法分配律简算": 0.003484681919064114, "补充知识点9925": 0.001742340959532057, "补充知识点9926": 0.001742340959532057, "作平移后的图形": 0.0008711704797660285, "在方格纸上画平移图形": 0.0008711704797660285, "已知平移的方向和距离，画平移后的图形": 0.0008711704797660285, "补充知识点20737": 0.0008711704797660285, "补充知识点20738": 0.0008711704797660285, "补充知识点20739": 0.0008711704797660285, "平均数、中位数、众数": 0.0027587065192590902, "平均数的意义及求法": 0.0027587065192590902, "平均数的实际问题": 0.0005185538570035884, "求平均价格平均用电等实际问题": 0.0005185538570035884, "补充知识点22530": 0.0005185538570035884, "补充知识点22531": 0.0005185538570035884, "小数的数位和计数单位的认识": 0.0073842069237310985, "小数的计数单位": 0.0028001908278193774, "小数相邻两个计数单位之间的进率": 0.0028001908278193774, "补充知识点2080": 0.0028001908278193774, "补充知识点2081": 0.0028001908278193774, "小数的意义": 0.003484681919064114, "用分数和小数表示阴影部分": 0.002281636970815789, "补充知识点1760": 0.0011408184854078945, "补充知识点1761": 0.0011408184854078945, "小数的读法和写法": 0.007633112775092821, "小数的读法(两位及以上)": 0.0026964800564186595, "小数的读法": 0.0026964800564186595, "补充知识点1904": 0.0026964800564186595, "补充知识点1905": 0.0026964800564186595, "根据小数的意义进行单位换算": 0.00856650971769928, "根据小数的意义进行长度单位的换算": 0.0007467175540851673, "数线上的单位换算": 0.0007467175540851673, "补充知识点1824": 0.0007467175540851673, "补充知识点1825": 0.0007467175540851673, "三角形的分类": 0.004915890564394018, "按角分类三角形": 0.0014312086453299039, "能根据已给某角度数对三角形进行分类": 0.0014312086453299039, "补充知识点16857": 0.0014312086453299039, "补充知识点16858": 0.0014312086453299039, "按边分类三角形": 0.00018667938852129183, "辨析等边三角形": 0.00018667938852129183, "补充知识点16841": 0.00018667938852129183, "补充知识点16842": 0.00018667938852129183, "小数与单位换算(综合)": 0.0022401526622555018, "填单位名称": 0.0022401526622555018, "补充知识点1846": 0.0022401526622555018, "补充知识点1847": 0.0022401526622555018, "小数点位置的移动": 0.013876501213416026, "小数点移动规律的正用": 0.005247765032876314, "去掉小数点求数值的变化": 0.005247765032876314, "补充知识点2206": 0.005247765032876314, "补充知识点2207": 0.005247765032876314, "小数的写法(两位及以上)": 0.0009333969426064591, "写出计数器上的小数": 0.0009333969426064591, "补充知识点1926": 0.0009333969426064591, "补充知识点1927": 0.0009333969426064591, "小数点移动规律的逆用": 0.0014519507996100475, "经过两次变化后，求原数": 0.0014519507996100475, "补充知识点2200": 0.0014519507996100475, "补充知识点2201": 0.0014519507996100475, "特殊三角形内角和的应用(等腰/等边/等腰直角/直角三角形)": 0.0038165563875464104, "已知等腰三角形的顶角与一个底角和，求顶角": 0.0038165563875464104, "补充知识点16985": 0.0038165563875464104, "补充知识点16986": 0.0038165563875464104, "特殊四边形内角和的应用(长方形/正方形)": 0.0006844910912447367, "长/正方形剪掉一个角，判断剩余图形": 0.0006844910912447367, "补充知识点17059": 0.0006844910912447367, "补充知识点17060": 0.0006844910912447367, "利用小数点移动引起小数大小的变化解决实际问题": 0.0034224554562236834, "解决纸的厚度问题": 0.0034224554562236834, "补充知识点2174": 0.0034224554562236834, "补充知识点2175": 0.0034224554562236834, "多位小数的进位加法、退位减法": 0.007259753998050238, "小数加减法": 0.005766318889879903, "找规律填数": 0.005766318889879903, "补充知识点7218": 0.005766318889879903, "小数加减混合运算的应用题": 0.003132065296301674, "补充知识点7300": 0.003132065296301674, "整数加法运算律推广到小数": 0.0010163655597270331, "小数连加简算": 0.0010163655597270331, "小数连加简算(结合律)": 0.0010163655597270331, "补充知识点9705": 0.0010163655597270331, "按角分类的应用": 0.0015764037252909088, "根据三角形里角的数量关系辨析直角三角形": 0.0015764037252909088, "补充知识点16849": 0.0015764037252909088, "补充知识点16850": 0.0015764037252909088, "角": 0.003526166227624401, "线段与角的综合": 0.0007467175540851673, "角度直接运算": 0.0007467175540851673, "运用直角和平角求解未知角的度数": 0.0007467175540851673, "补充知识点15937": 0.0007467175540851673, "补充知识点15938": 0.0007467175540851673, "等腰三角形": 0.0006844910912447367, "等腰三角形的特征": 0.0006844910912447367, "补充知识点16915": 0.0006844910912447367, "补充知识点16916": 0.0006844910912447367, "等边三角形": 0.000580780319844019, "认识等边三角形": 0.000580780319844019, "补充知识点16903": 0.000580780319844019, "补充知识点16904": 0.000580780319844019, "三角形的概念及表示方法": 0.0018253095766526312, "认识三角形": 0.0018253095766526312, "三角形的特征": 0.0018253095766526312, "补充知识点16739": 0.0018253095766526312, "补充知识点16740": 0.0018253095766526312, "两、三位数与两位数的乘法": 0.020431021965941383, "两位数乘两位数的进位乘法": 0.00587002966128062, "两位数乘两位数的笔算(有进位)的实际问题": 0.0021779261994150714, "两步（及以上）计算的实际问题": 0.0021779261994150714, "补充知识点6554": 0.0021779261994150714, "计算": 0.008110182323536122, "乘除法的速算与巧算": 0.0006637489369645932, "巧求头同尾合十\"的两位数乘两位数\"": 0.00010371077140071768, "头同尾合十巧算(不需要0占位)": 0.00010371077140071768, "补充知识点23518": 0.00010371077140071768, "补充知识点23519": 0.00010371077140071768, "补充知识点23520": 0.00010371077140071768, "位置与方向": 0.01953910933189521, "方向": 0.015577357864387796, "东南、西南、东北、西北方向": 0.008732446951940429, "方向与路程的综合问题（八个方向）": 0.0006844910912447367, "两地之间的路程问题": 0.0006844910912447367, "补充知识点21330": 0.0006844910912447367, "补充知识点21331": 0.0006844910912447367, "两位数乘两位数的笔算(有进位)的应用": 0.0012237871025284686, "算式表示的含义": 0.0018045674223724877, "补充知识点6577": 0.0012237871025284686, "用两步连乘解决实际问题": 0.0046877268673124394, "用连乘解决实际问题": 0.0032980025305428223, "根据算式编应用题并解答": 0.0032980025305428223, "补充知识点6601": 0.0032980025305428223, "东、南、西、北方向": 0.006844910912447367, "在平面图上辨认东、南、西、北": 0.0015971458795710522, "根据位置判断方向": 0.0015971458795710522, "补充知识点21236": 0.0015971458795710522, "补充知识点21237": 0.0015971458795710522, "长方形和正方形": 0.020161373960299517, "长方形的面积": 0.006471552135404783, "运用长方形面积公式解决实际问题": 0.0015556615710107652, "洒水车、收割机问题": 0.0016178880338511958, "补充知识点16281": 0.0015556615710107652, "补充知识点16282": 0.0015556615710107652, "两位数乘两位数的不进位乘法": 0.003941009313227272, "两位数乘两位数的笔算(不进位)": 0.0019497625023334924, "两位数乘两位数(不进位)的竖式算理": 0.0019497625023334924, "补充知识点6536": 0.0019497625023334924, "两、三位数与一位数的除法": 0.024434257742009084, "三位数除以一位数，商末尾有0": 0.002530542822177511, "商末尾有0的一位数除法(被除数末尾有0)": 0.0003318744684822966, "竖式改错": 0.004086204393188277, "补充知识点6422": 0.0003318744684822966, "两位数乘整十数的口算乘法": 0.004148430856028707, "运用两位数乘整十（百）数的口算解决实际问题": 0.0018460517309327746, "一步计算的应用": 0.0018460517309327746, "补充知识点6512": 0.0018460517309327746, "两、三位数的加法和减法": 0.005558897347078468, "两位数与两位数的加减法口算": 0.001161560639688038, "两位数加两位数的口算的应用题": 0.00024890585136172244, "解决问题": 0.0016801144966916263, "补充知识点4976": 0.00024890585136172244, "整数的简单估算": 0.013503142436373442, "除数是一位数的估算": 0.006056709049801912, "灵活选择估算策略解决问题": 0.0012237871025284686, "用不同的估算策略解决问题": 0.0012237871025284686, "补充知识点7121": 0.0012237871025284686, "小数的除法": 0.004542531787351435, "用“去尾法”解决问题": 0.0013274978739291864, "进一、去尾解决问题": 0.001161560639688038, "由算式判断解决的问题": 0.001161560639688038, "补充知识点7625": 0.001161560639688038, "运用除数是一位数的除法估算解决问题": 0.0019290203480533488, "总价、数量和单价之间的关系": 0.0019290203480533488, "补充知识点7133": 0.0019290203480533488, "三位数除以一位数，首位不够除": 0.002903901599220095, "运用三位数除以一位数的笔算解决实际问题(商是两位数)": 0.0014312086453299039, "两步（及以上）解决实际问题": 0.0014312086453299039, "补充知识点6392": 0.0014312086453299039, "两、三位数与两位数的除法": 0.007819792163614113, "多位数与两位数的乘除混合运算": 0.0019912468108937795, "用乘除混合解决实际问题": 0.0013689821824894733, "用乘除混合运算解决实际问题": 0.0013689821824894733, "补充知识点6844": 0.0013689821824894733, "三位数除以一位数，被除数中间无0（商中间有0）": 0.0031735496048619608, "商中间有0的一位数除法的应用": 0.0015764037252909088, "根据商中间是否是0选算式": 0.0015764037252909088, "补充知识点6464": 0.0015764037252909088, "商末尾有0的一位数除法的应用(被除数末尾有0)": 0.0006637489369645932, "判断商末尾0的个数": 0.0006637489369645932, "补充知识点6415": 0.0006637489369645932, "根据商末尾0的情况填数": 0.0009541390968866026, "根据商末尾0的个数填数": 0.0009541390968866026, "补充知识点6428": 0.0009541390968866026, "两位数乘两位数的笔算(不进位)的实际问题": 0.0012445292568086122, "够不够、能不能": 0.0012445292568086122, "补充知识点6543": 0.0012445292568086122, "两、三位数与一位数的乘法": 0.006284872746883491, "整十、整百、整千数与一位数的乘法": 0.000580780319844019, "运用口算乘法解决实际问题": 0.0002903901599220095, "整十、整百、整千数乘一位数的应用题": 0.0002903901599220095, "补充知识点6009": 0.0002903901599220095, "商末尾有0的一位数除法(有余数)": 0.0001659372342411483, "商末尾有0(有余数)的竖式计算": 0.0001659372342411483, "补充知识点6433": 0.0001659372342411483, "判断商是几位数（除数是一位数）": 0.0014934351081703347, "不计算判断商是几位数（除数是一位数）": 0.0014934351081703347, "根据商的位数选算式": 0.0014934351081703347, "补充知识点6477": 0.0014934351081703347, "商中间有0的一位数除法(被除数中间没有0)": 0.0006430067826844496, "补充知识点6456": 0.0006430067826844496, "余数与除数的关系": 0.004791437638713157, "除余关系的应用（两、三位数除以一位数）": 0.0011823027939681814, "由除余关系求被除数": 0.0011823027939681814, "补充知识点5890": 0.0011823027939681814, "一位小数的大小比较": 0.004044720084627989, "两数之间的小数": 0.0004770695484433013, "写出两个整数之间的一位小数": 0.0004770695484433013, "补充知识点2296": 0.0004770695484433013, "补充知识点2297": 0.0004770695484433013, "小数的初步认识": 0.004418078861670573, "小数的辨析": 0.0006015224741241625, "补充知识点1676": 0.0006015224741241625, "补充知识点1677": 0.0006015224741241625, "小数的含义": 0.002592769285017942, "借助直观图，理解小数的含义": 0.002592769285017942, "补充知识点1704": 0.002592769285017942, "补充知识点1705": 0.002592769285017942, "利用小数加减法解决实际问题": 0.006616747215365788, "运用一位小数减法解决问题": 0.0013274978739291864, "运用小数减法(不退位)的计算解决问题": 0.0013274978739291864, "补充知识点7255": 0.0013274978739291864, "小数的读法(一位小数)": 0.0008711704797660285, "读出小数(小数部分末尾为0)": 0.0008711704797660285, "补充知识点1890": 0.0008711704797660285, "补充知识点1891": 0.0008711704797660285, "测量": 0.020327311194540666, "常见的长度单位及换算": 0.0029661280620605257, "长度及长度的常用单位": 0.0009333969426064591, "米的认识": 0.0002281636970815789, "长度与比较(米和厘米)": 4.1484308560287074e-05, "求比一个数多或少几的数是多少(一)": 4.1484308560287074e-05, "补充知识点21713": 4.1484308560287074e-05, "时间单位及换算": 0.023023791250959325, "经过时间的计算": 0.010122171288710045, "24时计时法时间的计算": 0.00580780319844019, "已知经过时间，求开始（结束）时刻": 0.002157184045134928, "已知经过时间，求结束时刻": 0.002157184045134928, "补充知识点13230": 0.002157184045134928, "24时计时法与普通计时法的互化": 0.002281636970815789, "普通计时法与24时计时法的转化": 0.0018253095766526312, "12时计时法转化为24时计时法": 0.0018253095766526312, "补充知识点13129": 0.0018253095766526312, "补充知识点13130": 0.0018253095766526312, "年、月、日的认识及换算": 0.003484681919064114, "认识大月与小月": 0.001472692953890191, "指定月份的天数": 0.001472692953890191, "补充知识点13093": 0.001472692953890191, "补充知识点13094": 0.001472692953890191, "日期和时间的推算": 0.0015764037252909088, "年、月、日时间的推算": 0.001203044948248325, "与星期几有关的推算（没有日历）": 0.0006844910912447367, "简单计算星期几(跨月)": 0.0006844910912447367, "补充知识点13282": 0.0006844910912447367, "24时计算经过的时间": 0.002281636970815789, "中途有停顿的经过时间": 0.002281636970815789, "补充知识点13216": 0.002281636970815789, "三位数除以一位数，首位除不尽": 0.0013482400282093297, "运用三位数除以一位数的笔算(最高位不能整除)解决实际问题": 0.00041484308560287074, "一步计算解决实际问题": 0.00041484308560287074, "补充知识点6371": 0.00041484308560287074, "长方形和正方形组合的面积": 0.002945385907780382, "长方形与正方形综合应用解决实际问题": 0.00010371077140071768, "一边靠墙的面积问题": 0.00010371077140071768, "补充知识点16427": 0.00010371077140071768, "补充知识点16428": 0.00010371077140071768, "常见的面积单位及换算": 0.0096243595859866, "面积单位间的进率及单位换算": 0.006409325672564353, "面积单位间的进率及换算": 0.005351475804277033, "铺砖问题（涉及单位换算）": 0.0015971458795710522, "铺砖方案的选择": 0.0015971458795710522, "补充知识点21870": 0.0015971458795710522, "长方形面积正求": 0.002281636970815789, "长方形面积公式推导的简单应用": 0.002281636970815789, "补充知识点16333": 0.002281636970815789, "补充知识点16334": 0.002281636970815789, "面积认识及大小的比较": 0.00445956317023086, "面积单位间的大小比较": 0.0007882018626454544, "多个面积单位的大小排序": 0.0007882018626454544, "补充知识点16217": 0.0007882018626454544, "补充知识点16218": 0.0007882018626454544, "图形剪切后的面积与周长": 8.296861712057415e-05, "剩余图形的面积和周长的变化": 8.296861712057415e-05, "补充知识点16195": 8.296861712057415e-05, "补充知识点16196": 8.296861712057415e-05, "长方形的周长": 0.0013274978739291864, "篱笆围墙(长方形)": 0.0002281636970815789, "一面靠墙，围长方形": 0.0002281636970815789, "补充知识点16071": 0.0002281636970815789, "补充知识点16072": 0.0002281636970815789, "长方形周长的运用": 0.000580780319844019, "直接利用长方形公式计算周长": 0.000580780319844019, "补充知识点16061": 0.000580780319844019, "补充知识点16062": 0.000580780319844019, "面积单位换算应用": 0.0011823027939681814, "计算并比较解决问题": 0.001783825268092344, "补充知识点21847": 0.0011823027939681814, "图形的拼组": 0.01026736636867105, "平面图形的拼接": 0.0026135114392980855, "用长（正）方形进行拼组解决面积问题": 0.001161560639688038, "已知基础图形的长宽（边长、周长），求拼后图形的面积（周长）": 0.001161560639688038, "补充知识点19831": 0.001161560639688038, "补充知识点19832": 0.001161560639688038, "补充知识点19833": 0.001161560639688038, "长、正方形面积综合应用": 0.0013689821824894733, "长方形纸中剪小正方形的个数": 0.0013689821824894733, "补充知识点16433": 0.0013689821824894733, "补充知识点16434": 0.0013689821824894733, "改变长方形的长或宽求面积变化量": 0.0009956234054468898, "根据长（宽）的变化和面积的变化求宽（长）": 0.0009956234054468898, "补充知识点16261": 0.0009956234054468898, "补充知识点16262": 0.0009956234054468898, "长方形与正方形互化后的面积问题": 0.00037335877704258366, "长宽一定的长方形化成正方形后的面积": 0.00037335877704258366, "补充知识点16419": 0.00037335877704258366, "补充知识点16420": 0.00037335877704258366, "解决有关小数大小比较的实际问题（一位小数）": 0.0014519507996100475, "名次问题": 0.0014519507996100475, "补充知识点2280": 0.0014519507996100475, "补充知识点2281": 0.0014519507996100475, "面积单位间的换算": 0.002219410507975358, "找出相等的量-": 0.002219410507975358, "补充知识点21856": 0.002219410507975358, "挖空（粉刷）问题": 0.00031113231420215305, "长方形中挖正方形": 0.00031113231420215305, "补充知识点16451": 0.00031113231420215305, "补充知识点16452": 0.00031113231420215305, "长方形面积与周长的综合应用": 0.0009126547883263156, "长方形的周长一定，长方形的面积的可能情况": 0.0009126547883263156, "补充知识点16311": 0.0009126547883263156, "补充知识点16312": 0.0009126547883263156, "解决有关小数大小比较的实际问题": 0.001742340959532057, "比较价格的高低": 0.001742340959532057, "补充知识点2362": 0.001742340959532057, "补充知识点2363": 0.001742340959532057, "24时计时法简单应用": 0.0006015224741241625, "24时计时法表示时刻": 0.0006015224741241625, "补充知识点13208": 0.0006015224741241625, "认识24时计时法、12时计时法": 0.0004563273941631578, "认识24时计时法和12时计时法": 0.0004563273941631578, "补充知识点13135": 0.0004563273941631578, "补充知识点13136": 0.0004563273941631578, "平年、闰年的认识及判定方法": 0.002841675136379664, "认识平年和闰年": 0.0018253095766526312, "认识平年、闰年": 0.0018253095766526312, "补充知识点13306": 0.0018253095766526312, "补充知识点13307": 0.0018253095766526312, "认识月份和季度": 0.0006844910912447367, "季度与月份": 0.0006844910912447367, "补充知识点13105": 0.0006844910912447367, "补充知识点13106": 0.0006844910912447367, "年、月、日有关的计算": 0.003090580987741387, "计算经过的天数": 0.001742340959532057, "解决与保质期有关的问题": 0.001742340959532057, "补充知识点13198": 0.001742340959532057, "计算经过的年数": 0.000539296011283732, "计算年龄": 0.000539296011283732, "补充知识点13184": 0.000539296011283732, "长度单位的选择": 0.0007882018626454544, "选择合适的长度单位(米和厘米)": 0.0004978117027234449, "补充知识点21761": 0.00024890585136172244, "补充知识点21762": 0.00024890585136172244, "正方形的面积": 0.002530542822177511, "正方形面积与周长的综合应用": 0.0009748812511667462, "周长与面积不能比较": 0.0009748812511667462, "补充知识点16373": 0.0009748812511667462, "补充知识点16374": 0.0009748812511667462, "除数是整数的小数除法的应用": 0.00037335877704258366, "除数是整数的小数除法实际问题9": 0.00037335877704258366, "解决有关比较的实际问题": 0.0010578498682873203, "补充知识点7537": 0.00037335877704258366, "与平年和闰年有关的实际问题": 0.0010163655597270331, "与2月29日过生日有关的实际问题": 0.0010163655597270331, "补充知识点13318": 0.0010163655597270331, "补充知识点13319": 0.0010163655597270331, "常见节日认识": 0.0004563273941631578, "补充知识点13069": 0.0002281636970815789, "补充知识点13070": 0.0002281636970815789, "推理第几天是几月几日": 8.296861712057415e-05, "一年的倒数第几天是几月几日": 8.296861712057415e-05, "补充知识点13301": 8.296861712057415e-05, "1平方厘米小正方形的拼接问题（无图）": 0.0009956234054468898, "补几个图形可拼成长（正）方形": 0.0009956234054468898, "补充知识点16199": 0.0009956234054468898, "补充知识点16200": 0.0009956234054468898, "探究周长（面积）相等的图形面积（周长）变化的情况": 0.0004563273941631578, "周长一定，长方形、正方形的面积变化情况": 0.0004563273941631578, "补充知识点16405": 0.0004563273941631578, "补充知识点16406": 0.0004563273941631578, "长方形面积反求": 0.0004770695484433013, "已知长方形的面积，长和宽的可能情况": 0.0004770695484433013, "补充知识点16301": 0.0004770695484433013, "补充知识点16302": 0.0004770695484433013, "已知面积，求长（宽）或边长（涉及单位换算）": 0.00014519507996100476, "已知正方形的面积，求边长（乘法换算）": 0.00014519507996100476, "补充知识点21846": 0.00014519507996100476, "三位数除以一位数的应用(商是两位数)": 0.0008089440169255979, "填合适的数": 0.0015556615710107652, "补充知识点6378": 0.0008089440169255979, "两位数除以一位数，首位除不尽": 0.0015349194167306216, "两位数除以一位数(被除数首位不能整除)的应用": 0.000269648005641866, "计算并比较大小": 0.0013067557196490428, "补充知识点6312": 0.000269648005641866, "三位数除以一位数，首位能除尽": 0.001389724336769617, "三位数除以一位数(每一位都能整除)的应用": 0.00010371077140071768, "图形找规律": 0.00010371077140071768, "补充知识点6338": 0.00010371077140071768, "除数是一位数的估算的应用": 0.0014519507996100475, "估算并比较大小": 0.0014519507996100475, "补充知识点7143": 0.0014519507996100475, "商中间有0的一位数除法的实际问题": 0.0009541390968866026, "计算并比较的实际问题": 0.0009541390968866026, "补充知识点6448": 0.0009541390968866026, "商末尾有0的一位数除法的实际问题": 0.00041484308560287074, "方案选择/更划算": 0.00041484308560287074, "补充知识点6440": 0.00041484308560287074, "植树问题": 0.001742340959532057, "封闭图形上的植树问题": 0.0003318744684822966, "封闭路线植树问题": 0.0003318744684822966, "封闭路线求长度": 0.0003318744684822966, "补充知识点15110": 0.0003318744684822966, "竞赛应用题": 0.0067619422953267925, "和倍问题": 0.0010163655597270331, "隐藏的和倍问题（两位数除以一位数）": 0.00018667938852129183, "解决实际问题": 0.0002281636970815789, "补充知识点25782": 0.00018667938852129183, "补充知识点25783": 0.00018667938852129183, "补充知识点25784": 0.00018667938852129183, "除数是一位数的填数问题": 0.001161560639688038, "已知没有余数填被除数中的某位数": 0.001161560639688038, "补充知识点24631": 0.001161560639688038, "补充知识点24632": 0.001161560639688038, "补充知识点24633": 0.001161560639688038, "错中求解": 0.005600381655638755, "除数是一位数除法的错中求解问题": 0.000539296011283732, "需结合余数（已知）的还原问题": 0.000539296011283732, "补充知识点23817": 0.000539296011283732, "补充知识点23818": 0.000539296011283732, "补充知识点23819": 0.000539296011283732, "整百整十数除以一位数的除法": 0.002530542822177511, "几百几十（几千几百）数除以一位数的口算": 0.001078592022567464, "末尾带0的除法规律": 0.002219410507975358, "补充知识点6233": 0.001078592022567464, "整十、整百、整千数除以一位数的除法": 0.0024475742050569372, "整十、整百、整千数除以一位数的口算": 0.0011408184854078945, "补充知识点6207": 0.0011408184854078945, "整十、整百、整千数乘一位数的口算": 0.0002903901599220095, "直接写出结果": 0.0024475742050569372, "补充知识点6018": 0.0002903901599220095, "两位数除以一位数，首位能除尽": 0.0015764037252909088, "两位数除以一位数的笔算(被除数首位能被整除)": 0.000891912634046172, "竖式中某部分的含义": 0.000891912634046172, "补充知识点6286": 0.000891912634046172, "直接估算出结果": 0.0007259753998050237, "补充知识点7129": 0.0007259753998050237, "三角形的分类综合应用": 0.0013689821824894733, "与三角形的分类相关说法的对错判断": 0.0013689821824894733, "补充知识点16871": 0.0013689821824894733, "补充知识点16872": 0.0013689821824894733, "比例": 0.05065234075211052, "图形的放大与缩小": 0.0031528074505818176, "图形放大与缩小的理解": 0.0009333969426064591, "根据放大与缩小的变化选择图形": 0.0009333969426064591, "补充知识点12854": 0.0009333969426064591, "补充知识点12855": 0.0009333969426064591, "比例的意义": 0.007280496152330381, "比例的意义（一）": 0.0016801144966916263, "补充知识点12145": 0.0016801144966916263, "补充知识点12146": 0.0016801144966916263, "比例尺应用": 0.0015971458795710522, "比例尺有关的综合应用题一（结合行程）-": 0.0004978117027234449, "求相遇时间（知比例尺、图上距离、两车速度）-": 0.0004978117027234449, "补充知识点12928": 0.0004978117027234449, "补充知识点12929": 0.0004978117027234449, "百分数的四则运算": 0.013793532596295451, "求一个数比另一个数多/少百分之几": 0.0019082781937732052, "复杂的求两数百分率关系问题": 0.0009541390968866026, "补充知识点8508": 0.0009541390968866026, "补充知识点8509": 0.0009541390968866026, "分数的四则混合运算及应用题": 0.0023853477422165065, "分数四则混合运算": 0.000580780319844019, "判断运算顺序": 0.000580780319844019, "补充知识点8396": 0.000580780319844019, "补充知识点8397": 0.000580780319844019, "巧用分数乘法运算律进行简便计算": 0.00024890585136172244, "利用乘法交换、结合律进行简便运算": 0.00024890585136172244, "补充知识点8388": 0.00024890585136172244, "补充知识点8389": 0.00024890585136172244, "小数的连除运算": 0.00014519507996100476, "小数连除": 0.00014519507996100476, "同级运算带符号搬家": 0.00014519507996100476, "补充知识点7640": 0.00014519507996100476, "小数的四则运算及法则": 0.0004563273941631578, "小数四则混合运算顺序": 0.00024890585136172244, "小数四则混合运算的运算法则": 0.00024890585136172244, "补充知识点7722": 0.00024890585136172244, "补充知识点7723": 0.00024890585136172244, "用最大公因数解决实际问题": 0.003484681919064114, "用求公因数的方法解决实际问题": 0.003484681919064114, "用公因数解决分物体问题": 0.003484681919064114, "补充知识点1499": 0.003484681919064114, "鸽巢问题": 0.00891912634046172, "鸽巢问题进阶": 0.0007674597083653108, "利用鸽巢原理\"解决实际问题-\"": 0.000580780319844019, "钥匙开锁问题": 0.000580780319844019, "补充知识点15307": 0.000580780319844019, "正比例的应用": 0.0032357760677023915, "正比例的应用（一）": 0.0022608948165356454, "正比例应用题（直接计算）": 0.0022608948165356454, "补充知识点12546": 0.0022608948165356454, "补充知识点12547": 0.0022608948165356454, "正比例的意义及辨识": 0.002841675136379664, "正比例的意义": 0.0008296861712057415, "补全表格-": 0.0008296861712057415, "补充知识点12428": 0.0008296861712057415, "补充知识点12429": 0.0008296861712057415, "求实际周长、面积问题-": 0.0008089440169255979, "求实际面积（知比例尺、图上长度）-": 0.0008089440169255979, "补充知识点12914": 0.0008089440169255979, "补充知识点12915": 0.0008089440169255979, "比例尺的意义": 0.005683350272759328, "由实际数据计算比例尺": 0.0029868702163406693, "求缩小比例尺（知图上距离、实际距离）": 0.0029868702163406693, "补充知识点12764": 0.0029868702163406693, "补充知识点12765": 0.0029868702163406693, "线段比例尺与数值比例尺的互化": 0.0008711704797660285, "线段比例尺化成数值比例尺": 0.0008711704797660285, "补充知识点12756": 0.0008711704797660285, "补充知识点12757": 0.0008711704797660285, "应用比例尺画图": 0.0008711704797660285, "由比例尺画平面图/找物体位置": 0.0008711704797660285, "计算实际距离，在平面图中画物体位置（知比例尺+方向+运动情况）": 0.0008711704797660285, "补充知识点12794": 0.0008711704797660285, "补充知识点12795": 0.0008711704797660285, "正比例图象的认识": 0.0018045674223724877, "正比例图象的特点": 0.0007259753998050237, "用所给数据绘制正比例图象": 0.0007259753998050237, "补充知识点12464": 0.0007259753998050237, "补充知识点12465": 0.0007259753998050237, "正比例的判断": 0.0012445292568086122, "找出不成比例关系的两量-": 0.0012445292568086122, "补充知识点12414": 0.0012445292568086122, "补充知识点12415": 0.0012445292568086122, "反比例的意义及辨识": 0.008006471552135404, "比例判断综合-": 0.004832921947273444, "判断两量间的比例关系（文字）-": 0.004832921947273444, "补充知识点12520": 0.004832921947273444, "补充知识点12521": 0.004832921947273444, "影子问题": 0.0005185538570035884, "靠近/远离路灯，判断影子变化-": 0.0005185538570035884, "补充知识点12538": 0.0005185538570035884, "补充知识点12539": 0.0005185538570035884, "比例的基本性质": 0.006243388438323204, "判定能否组成比例（用比例的基本性质）": 0.0004770695484433013, "多个比中选两个组比例": 0.0004770695484433013, "补充知识点12247": 0.0004770695484433013, "补充知识点12248": 0.0004770695484433013, "立体图形": 0.0781356951733007, "体积的等积变形": 0.003256518221982535, "体积的等积变形（圆柱、圆锥）": 0.0017630831138122005, "等积变形问题（含圆柱）-": 0.0003318744684822966, "连通器问题（求水面高度）": 0.0003318744684822966, "补充知识点19700": 0.0003318744684822966, "补充知识点19701": 0.0003318744684822966, "判定能否组成比例（用比例的意义）": 0.0023023791250959325, "计算出比值后直接组成比例": 0.0023023791250959325, "补充知识点12159": 0.0023023791250959325, "补充知识点12160": 0.0023023791250959325, "比的意义": 0.000539296011283732, "数学文化中的比": 0.000269648005641866, "补充知识点11699": 0.000269648005641866, "补充知识点11700": 0.000269648005641866, "分数加、减简便运算": 0.002945385907780382, "分子是1的异分母分数加法的简便算法": 0.0004978117027234449, "分母是互质数的两分数的加法计算": 0.0004978117027234449, "补充知识点9759": 0.0004978117027234449, "补充知识点9760": 0.0004978117027234449, "含百分数的运算": 0.00031113231420215305, "百分数四则混合运算": 0.00031113231420215305, "含百分数加减的四则混合运算(不带括号)": 0.00031113231420215305, "补充知识点8450": 0.00031113231420215305, "补充知识点8451": 0.00031113231420215305, "分数除法": 0.0032357760677023915, "被除数与商的大小关系（分数除法）": 0.0005185538570035884, "商与被除数的大小关系（分数除法）": 0.0005185538570035884, "被除数与商的大小关系": 0.0005185538570035884, "补充知识点8286": 0.0005185538570035884, "小数的乘法": 0.0045010474787911476, "小数与整数的乘法": 0.0014104664910497605, "小数乘整数计算": 0.00037335877704258366, "估算-": 0.00037335877704258366, "补充知识点7350": 0.00037335877704258366, "因数和积的大小关系（小数乘法）": 0.0003941009313227272, "积与因数的大小关系（小数乘法）": 0.0003941009313227272, "判断积与因数的大小关系（算式与数）-": 0.0003941009313227272, "补充知识点7449": 0.0003941009313227272, "正负数的认识": 0.006284872746883491, "正负数的大小比较": 0.0004978117027234449, "正、负数的大小比较": 0.0004978117027234449, "排序": 0.0011200763311277509, "补充知识点3605": 0.0004978117027234449, "补充知识点3606": 0.0004978117027234449, "百分数的认识": 0.00205347327373421, "百分数、小数和分数的互化": 0.000891912634046172, "百分数和分数、小数的互化": 0.000580780319844019, "百分数、比、分数、小数的互化": 0.000580780319844019, "补充知识点3345": 0.000580780319844019, "补充知识点3346": 0.000580780319844019, "用四舍五入\"法取循环小数的近似值\"": 0.00031113231420215305, "商用循环小数表示并求商的近似值": 0.00031113231420215305, "补充知识点2458": 0.00031113231420215305, "分数的意义、读写及分类": 0.015639584327228225, "分数单位的认识与确定": 0.0018045674223724877, "添加分数单位后，变成某数": 0.000269648005641866, "添加分数单位，使其变成整数 -": 0.000269648005641866, "补充知识点2556": 0.000269648005641866, "真分数、假分数、带分数的运用": 0.005766318889879903, "真分数的运用": 0.001203044948248325, "求分母确定的所有真分数的和-": 0.001203044948248325, "补充知识点2809": 0.001203044948248325, "补充知识点2810": 0.001203044948248325, "真分数、假分数、带分数的认识": 0.003982493621787559, "认识带分数": 0.0007467175540851673, "带分数的读法写法": 0.0007467175540851673, "补充知识点2689": 0.0007467175540851673, "补充知识点2690": 0.0007467175540851673, "比与分数、除法的关系": 0.0001659372342411483, "比和分数、除法的关系": 0.00024890585136172244, "补充知识点11835": 0.00012445292568086122, "补充知识点11836": 0.00012445292568086122, "求图形的放大比、缩小比-": 0.0004563273941631578, "求图形放大或缩小的比（无图）": 0.0004563273941631578, "补充知识点12838": 0.0004563273941631578, "补充知识点12839": 0.0004563273941631578, "解比例的实际问题-": 0.0013689821824894733, "解比例的实际应用（一）": 0.0012445292568086122, "知增减量+变化前后分量比，求两量": 0.0012445292568086122, "补充知识点12335": 0.0012445292568086122, "补充知识点12336": 0.0012445292568086122, "假分数的运用": 0.0005185538570035884, "由分数是最简假分数求未知数的可能值": 0.0005185538570035884, "补充知识点2831": 0.0005185538570035884, "补充知识点2832": 0.0005185538570035884, "解比例的计算": 0.003795814233266267, "解比例（概念、计算）": 0.0026964800564186595, "判断解比例用到的方法-": 0.0026964800564186595, "补充知识点12297": 0.0026964800564186595, "补充知识点12298": 0.0026964800564186595, "根据等式写比例": 0.002323121279376076, "乘法等式写成比例-": 0.002323121279376076, "补充知识点12241": 0.002323121279376076, "补充知识点12242": 0.002323121279376076, "反比例的意义": 0.0007467175540851673, "由已给数据，理解反比例关系": 0.0007467175540851673, "补充知识点12488": 0.0007467175540851673, "补充知识点12489": 0.0007467175540851673, "圆柱和圆锥": 0.01912426624629234, "圆柱的展开图": 0.0019082781937732052, "由圆柱表面展开图求体积": 0.00012445292568086122, "所围圆柱容积最大，求底面半/直径（知长方形长宽）": 0.00012445292568086122, "补充知识点18888": 0.00012445292568086122, "补充知识点18889": 0.00012445292568086122, "圆柱的表面积": 0.0027587065192590902, "圆柱的表面积的较复杂应用": 0.0004355852398830143, "糊灯笼所需的彩纸面积-": 0.0004355852398830143, "补充知识点19030": 0.0004355852398830143, "补充知识点19031": 0.0004355852398830143, "图上距离、实际距离的换算": 0.004231399473149281, "由比例尺求实际距离": 0.0023438634336562197, "求实际距离（知缩小数值比例尺+图上距离）": 0.0023438634336562197, "补充知识点12806": 0.0023438634336562197, "补充知识点12807": 0.0023438634336562197, "除数是小数的小数除法": 0.0006844910912447367, "除数是小数的除法计算9": 0.0003318744684822966, "估算": 0.0003318744684822966, "补充知识点7572": 0.0003318744684822966, "含有括号的小数混合运算": 0.00010371077140071768, "脱式计算": 0.001161560639688038, "补充知识点7698": 0.00010371077140071768, "补充知识点7699": 0.00010371077140071768, "鸽巢问题初步": 0.00587002966128062, "求一个笼里的鸽子数-": 0.003982493621787559, "一个笼里至少鸽子数（知鸽子、笼子）-": 0.003982493621787559, "补充知识点15288": 0.003982493621787559, "圆柱的认识和特征": 0.0014519507996100475, "单个圆柱捆扎问题": 0.00024890585136172244, "求包装绳长(单个圆柱绕圈捆扎)": 0.00024890585136172244, "补充知识点18860": 0.00024890585136172244, "补充知识点18861": 0.00024890585136172244, "立体图形的切拼": 0.006430067826844496, "立体图形的切拼（圆柱）": 0.002281636970815789, "平行底面切/拼圆柱的表面积": 0.0003318744684822966, "求截面面积（知段数+拼后减少的面积）": 0.0003318744684822966, "补充知识点20171": 0.0003318744684822966, "补充知识点20172": 0.0003318744684822966, "反比例的判断": 0.0006637489369645932, "找成反比例关系的两量-": 0.0006637489369645932, "补充知识点12478": 0.0006637489369645932, "补充知识点12479": 0.0006637489369645932, "组成比例（一）": 0.0012445292568086122, "补充数字组成比例-": 0.0012445292568086122, "补充知识点12169": 0.0012445292568086122, "补充知识点12170": 0.0012445292568086122, "常见的体积单位及换算": 0.007736823546493539, "体积、容积单位的选择": 0.0008296861712057415, "填合适的单位(容积）": 0.0008296861712057415, "填合适的单位（面积、体积、容积）-": 0.0008296861712057415, "补充知识点22024": 0.0008296861712057415, "补充知识点22025": 0.0008296861712057415, "体积、容积单位进率及换算": 0.0027379643649789467, "容积单位间的进率与换算（升和毫升）": 0.0015556615710107652, "容积单位的换算": 0.001389724336769617, "单位换算综合（长度、面积、体积...）-": 0.001389724336769617, "补充知识点22012": 0.001389724336769617, "按比分配问题": 0.001742340959532057, "已知总量和分量比，求分量": 0.001078592022567464, "已知分量比，先求两个分量的和，再求分量(算式关系)": 0.001078592022567464, "补充知识点11895": 0.001078592022567464, "补充知识点11896": 0.001078592022567464, "分数乘法": 0.01126298977411794, "求一个数的几分之几的问题": 0.002219410507975358, "求一个数的几分之几是多少(分数乘法)": 0.0007259753998050237, "数形结合求一个数的几分之几": 0.0007259753998050237, "补充知识点8176": 0.0007259753998050237, "比的应用": 0.002219410507975358, "求量之比（简单实际问题）-": 8.296861712057415e-05, "求与总价、单价、数量有关的比（直接求）": 8.296861712057415e-05, "补充知识点12123": 8.296861712057415e-05, "补充知识点12124": 8.296861712057415e-05, "化简比(带单位)": 0.00018667938852129183, "含小数的化简比(带单位)": 0.00018667938852129183, "补充知识点11767": 0.00018667938852129183, "补充知识点11768": 0.00018667938852129183, "求比值": 0.0009956234054468898, "求比值并比较判断（应用题）": 0.0004978117027234449, "补充知识点11727": 0.0004978117027234449, "补充知识点11728": 0.0004978117027234449, "化简比(分数)": 0.00018667938852129183, "化简分数比并求比值(真分数)": 0.00018667938852129183, "补充知识点11777": 0.00018667938852129183, "补充知识点11778": 0.00018667938852129183, "长方体和正方体": 0.031486590197257885, "长方体的体积": 0.006596005061085645, "长方体棱长变化的运用-": 0.0007052332455248803, "长不变，根据宽和高变化的倍数，求体积的变化-": 0.0007052332455248803, "补充知识点18550": 0.0007052332455248803, "补充知识点18551": 0.0007052332455248803, "利用等积变形\"的数学思想解决问题\"": 0.00037335877704258366, "等积变形的理解、辨析-": 0.00037335877704258366, "补充知识点19716": 0.00037335877704258366, "补充知识点19717": 0.00037335877704258366, "与常见图形有关的比和化简比": 0.0006015224741241625, "根据所给图形的数量关系求比": 0.0006015224741241625, "补充知识点12051": 0.0006015224741241625, "补充知识点12052": 0.0006015224741241625, "式与方程": 0.014457281533260044, "方程的解与解方程": 0.0035469083819045445, "解分数方程": 0.00205347327373421, "分数四则混合运算(无括号)": 0.0004770695484433013, "补充知识点11197": 0.0004770695484433013, "补充知识点11198": 0.0004770695484433013, "用字母表示数": 0.005144054261475597, "用字母表示数、数量关系": 0.004169173010308851, "用字母表示自然数9": 0.0004978117027234449, "用含字母的算式表示连续的奇/偶/自然数-": 0.0004978117027234449, "补充知识点10758": 0.0004978117027234449, "补充知识点10759": 0.0004978117027234449, "圆锥的体积（容积）": 0.0023438634336562197, "三角形旋转的体积问题（含圆锥）-": 0.00031113231420215305, "找旋转后体积相等的平面图": 0.00031113231420215305, "补充知识点19372": 0.00031113231420215305, "补充知识点19373": 0.00031113231420215305, "统计图表的综合应用": 0.00645080998112464, "扇形统计图中获取信息填空-（考法需整合）": 0.0006430067826844496, "已知单位1\"和分率，求部分量\"": 0.0006430067826844496, "补充知识点22698": 0.0006430067826844496, "补充知识点22699": 0.0006430067826844496, "补充知识点22700": 0.0006430067826844496, "百分数的意义": 0.0013482400282093297, "百分数的意义描述": 0.00020742154280143537, "补充知识点3275": 0.00020742154280143537, "补充知识点3276": 0.00020742154280143537, "求一个数是另一个数的百分之几（百分率问题）": 0.001078592022567464, "求一个数是另一个数的百分之几": 0.000539296011283732, "求一个数是另一个数的百分之几（文字叙述）": 0.000539296011283732, "补充知识点8490": 0.000539296011283732, "补充知识点8491": 0.000539296011283732, "扇形统计图的特点及绘制": 0.0004355852398830143, "根据数据绘制扇形统计图": 0.0002281636970815789, "判断扇形统计图绘制的正误": 0.0002281636970815789, "补充知识点22458": 0.0002281636970815789, "补充知识点22459": 0.0002281636970815789, "根据方向、角度和距离确定物体的位置": 0.0011823027939681814, "用方向和距离确定某个点的位置": 0.00035261662276244013, "用方向和距离确定一个点的位置(已知参照点)": 0.00035261662276244013, "补充知识点21552": 0.00035261662276244013, "补充知识点21553": 0.00035261662276244013, "补充知识点21554": 0.00035261662276244013, "圆": 0.006575262906805501, "圆的面积及应用": 0.0015764037252909088, "圆的面积": 0.0010371077140071767, "圆的面积公式正用": 0.0004978117027234449, "圆的面积公式正向应用(需辨析圆的半径)": 0.0004978117027234449, "补充知识点17316": 0.0004978117027234449, "圆的周长及应用": 0.0016178880338511958, "半圆的周长": 0.0002903901599220095, "求半圆的周长": 0.000580780319844019, "补充知识点17238": 0.0002903901599220095, "20以内数的加、减法": 0.007425691232291386, "实际问题（十几减几的退位减法）": 0.001078592022567464, "解决含有多余条件的实际问题": 0.0010578498682873203, "含多余条件的减法应用题": 0.0010578498682873203, "补充知识点4212": 0.0010578498682873203, "图形识别与匹配": 0.00024890585136172244, "小图形是大图形的某一部分": 0.00024890585136172244, "补充知识点19795": 0.00024890585136172244, "补充知识点19796": 0.00024890585136172244, "补充知识点19797": 0.00024890585136172244, "用同样的平面图形进行拼组": 0.000539296011283732, "用几个同样的长（正）方形": 0.000539296011283732, "补充知识点19810": 0.000539296011283732, "补充知识点19811": 0.000539296011283732, "补充知识点19812": 0.000539296011283732, "计数": 0.005247765032876314, "平面图形计数": 0.002509800667897368, "补砖块问题": 0.0003318744684822966, "长（正）方形砖": 0.0003318744684822966, "补充知识点25078": 0.0003318744684822966, "补充知识点25079": 0.0003318744684822966, "补充知识点25080": 0.0003318744684822966, "规则图形折、剪、拼": 0.0003941009313227272, "拼成规则图形": 0.0003941009313227272, "补充知识点19882": 0.0003941009313227272, "补充知识点19883": 0.0003941009313227272, "补充知识点19884": 0.0003941009313227272, "简单平面图形计数": 0.0004770695484433013, "图形零散摆放": 0.0004770695484433013, "补充知识点25126": 0.0004770695484433013, "补充知识点25127": 0.0004770695484433013, "补充知识点25128": 0.0004770695484433013, "平面图形的认识及分类": 0.001472692953890191, "长方形、正方形、三角形和圆的初步认识": 0.0011408184854078945, "认识各种平面图形": 0.0003941009313227272, "图形的特点": 0.0003941009313227272, "补充知识点15419": 0.0003941009313227272, "补充知识点15420": 0.0003941009313227272, "100以内数的加法、减法": 0.019290203480533487, "100以内数的加减混合运算": 0.004957374872954305, "分步解决问题的策略": 0.0008296861712057415, "问题1+条件解决问题2": 0.0008296861712057415, "补充知识点4931": 0.0008296861712057415, "数量关系的比较": 0.0003941009313227272, "由图意填合适的条件/问题": 0.0003941009313227272, "补充知识点4924": 0.0003941009313227272, "求比一个数多（少）几的数": 0.0008296861712057415, "根据题意画一画": 0.0009126547883263156, "补充知识点4828": 0.0008296861712057415, "利用数量关系的比较解决问题": 0.00010371077140071768, "再多少就一样": 0.00010371077140071768, "补充知识点4907": 0.00010371077140071768, "线段图法解决问题": 0.00020742154280143537, "根据线段图列式计算": 0.00020742154280143537, "补充知识点4835": 0.00020742154280143537, "人民币单位及换算": 0.004293625935989712, "元、角、分的认识及换算": 0.003941009313227272, "加减法应用(人民币)": 0.0006637489369645932, "计算价格差": 0.0006637489369645932, "补充知识点13450": 0.0006637489369645932, "补充知识点13451": 0.0006637489369645932, "求一个数比另一个数多（少）几": 0.000539296011283732, "由题意只列式不计算": 0.000539296011283732, "补充知识点4821": 0.000539296011283732, "提出问题并解决问题（100以内数）": 0.000539296011283732, "根据已知信息提出问题并解答": 0.000539296011283732, "补充知识点4846": 0.000539296011283732, "循环小数的认识与简写": 0.0005600381655638754, "循环小数的简写": 0.0003318744684822966, "判断循环节": 0.0003318744684822966, "补充知识点1966": 0.0003318744684822966, "补充知识点1967": 0.0003318744684822966, "循环小数的意义": 0.00037335877704258366, "补充知识点1978": 0.00018667938852129183, "补充知识点1979": 0.00018667938852129183, "循环小数比大小": 0.0008089440169255979, "补充知识点1996": 0.000269648005641866, "补充知识点1997": 0.000269648005641866, "纯循环小数和混循环小数": 4.1484308560287074e-05, "混循环小数": 4.1484308560287074e-05, "补充知识点1956": 4.1484308560287074e-05, "补充知识点1957": 4.1484308560287074e-05, "用“四舍五入”法求商的近似数": 0.0001659372342411483, "用四舍五入\"法取商的近似值（小数除法）\"": 0.00012445292568086122, "算式的近似值": 0.00012445292568086122, "补充知识点7609": 0.00012445292568086122, "除数是小数的小数除法的应用": 0.00035261662276244013, "除数是小数的小数除法实际问题": 0.00035261662276244013, "已知总量求平均分问题": 0.00035261662276244013, "补充知识点7590": 0.00035261662276244013, "除数是小数的基本算理及算法": 6.222646284043061e-05, "除数是小数的算法9": 6.222646284043061e-05, "补充知识点7555": 6.222646284043061e-05, "被除数和商的大小关系（小数除法）": 0.00024890585136172244, "商与被除数的大小关系（除数是小数）": 0.00024890585136172244, "因数和积的大小关系与商和被除数的大小关系综合应用": 0.00024890585136172244, "补充知识点7632": 0.00024890585136172244, "循环小数和周期性规律综合问题": 6.222646284043061e-05, "循环小数与周期问题": 6.222646284043061e-05, "先计算，再判断某一位是几": 6.222646284043061e-05, "补充知识点2004": 6.222646284043061e-05, "补充知识点2005": 6.222646284043061e-05, "两位数与两位数的不进位加法": 0.0009541390968866026, "口算两位数加两位数(不进位)": 0.00012445292568086122, "相同数位上的数才能相加": 0.00012445292568086122, "补充知识点4511": 0.00012445292568086122, "100以内数的认识": 0.01254900333948684, "百数表": 0.001161560639688038, "根据百数表的规律填数": 0.000580780319844019, "补充知识点582": 0.000580780319844019, "两位数与两位数的进位加法": 0.0013482400282093297, "笔算两位数加两位数(进位加)": 0.00024890585136172244, "列竖式计算两位数+两位数(进位)": 0.00024890585136172244, "补充知识点4647": 0.00024890585136172244, "两位数与一位数的退位减法": 0.0012652714110887556, "口算两位数减一位数(退位)": 0.0002903901599220095, "补充知识点4616": 0.0002903901599220095, "两位数与一位数的不进位加法": 0.0007259753998050237, "口算两位数加一位数(不进位)": 0.00035261662276244013, "分步骤口算两位数加一位数": 0.00035261662276244013, "补充知识点4419": 0.00035261662276244013, "两位数与一位数的进位加法": 0.001514177262450478, "口算两位数加一位数(进位)": 0.00037335877704258366, "直接得出结果": 0.0004355852398830143, "补充知识点4585": 0.00037335877704258366, "100以内数的连减运算": 0.001659372342411483, "连减解决实际问题（100以内）": 0.001078592022567464, "连减解决实际问题": 0.001078592022567464, "补充知识点4796": 0.001078592022567464, "几何图形相关问题": 0.0019912468108937795, "平面几何": 0.0019912468108937795, "七巧板": 0.0007259753998050237, "七巧板拼图问题": 0.00041484308560287074, "运用七巧板的特征解决有趣的拼图问题": 0.00041484308560287074, "补充知识点25577": 0.00041484308560287074, "补充知识点25578": 0.00041484308560287074, "认识七巧板": 0.0002903901599220095, "七巧板中相同的图形": 0.0002903901599220095, "补充知识点25589": 0.0002903901599220095, "补充知识点25590": 0.0002903901599220095, "异分母分数加法的计算方法": 0.0012652714110887556, "理解异分母分数加法的算理": 0.0012652714110887556, "补充知识点7905": 0.0012652714110887556, "异分母分数减法的计算方法": 0.0004770695484433013, "理解异分母分数减法的算理": 0.0004770695484433013, "补充知识点7917": 0.0004770695484433013, "简单分数方程（分数加减）-": 0.000850428325485885, "等式性质2解分数方程-": 0.000850428325485885, "补充知识点11233": 0.000850428325485885, "补充知识点11234": 0.000850428325485885, "作旋转后的图形": 0.0021986683536952146, "判断/绘制图形旋转90°的图形": 0.0021986683536952146, "逆时针旋转90°后得到的图形": 0.0021986683536952146, "补充知识点20773": 0.0021986683536952146, "补充知识点20774": 0.0021986683536952146, "补充知识点20775": 0.0021986683536952146, "在方格纸上按一定的比将图形放大": 0.0006637489369645932, "方格纸中找图形放大后的图形": 0.0006637489369645932, "补充知识点12874": 0.0006637489369645932, "补充知识点12875": 0.0006637489369645932, "求一个数占另一个数几分之几": 0.00580780319844019, "求一个数是另一个数的几分之几的实际应用": 0.0008089440169255979, "一个量减少几分之几后与另一量相等，求两量关系9": 0.0008089440169255979, "补充知识点8079": 0.0008089440169255979, "最简分数": 0.004584016095911721, "最简分数的意义": 0.0021156997365746407, "已知分母/分子写最简真分数": 0.0021156997365746407, "补充知识点3095": 0.0021156997365746407, "补充知识点3096": 0.0021156997365746407, "长方体有关棱长的应用": 0.003484681919064114, "长方体棱长和的实际问题": 0.0007467175540851673, "求部分棱长和(实际问题)": 0.0007467175540851673, "补充知识点18369": 0.0007467175540851673, "补充知识点18370": 0.0007467175540851673, "不规则物体的体积算法": 0.004998859181514592, "不规则物体的体积算法（长方体、正方体）（考点考法需整合）": 0.0038165563875464104, "排水法求物体体积（方法/步骤）（考法需整合）": 0.0022608948165356454, "有水溢出求物体体积-": 0.0022608948165356454, "补充知识点19582": 0.0022608948165356454, "补充知识点19583": 0.0022608948165356454, "长方体体积公式实际应用": 0.0016178880338511958, "大长方体里最多放小长/正方体个数（棱长已知）-": 0.0016178880338511958, "补充知识点18606": 0.0016178880338511958, "补充知识点18607": 0.0016178880338511958, "长方体体积的计算": 0.004231399473149281, "根据棱长和及长、宽、高关系，求体积-": 0.004231399473149281, "补充知识点18566": 0.004231399473149281, "补充知识点18567": 0.004231399473149281, "长方体棱长和": 0.00170085665097177, "由横截面面积，求棱长和": 0.00170085665097177, "补充知识点18345": 0.00170085665097177, "补充知识点18346": 0.00170085665097177, "长方体的表面积": 0.006243388438323204, "长方体表面积的计算": 0.0030490966791810997, "长方体表面积（考法需整合）": 0.002509800667897368, "由侧面展开图信息，求表面积": 0.002509800667897368, "补充知识点18492": 0.002509800667897368, "长方体的展开图": 0.0018667938852129182, "根据正方体相对面的特征找展开图9": 0.0009333969426064591, "补充知识点18467": 0.0009333969426064591, "补充知识点18468": 0.0009333969426064591, "长方体的认识及特征": 0.0017630831138122005, "长方体的认识": 0.0017630831138122005, "四个面相同的特殊长方体": 0.0017630831138122005, "补充知识点18282": 0.0017630831138122005, "补充知识点18283": 0.0017630831138122005, "正方体有关棱长的应用": 0.0011200763311277509, "正方体棱长和的实际问题": 0.00018667938852129183, "小虫沿棱爬行问题9": 0.00018667938852129183, "补充知识点18429": 0.00018667938852129183, "补充知识点18430": 0.00018667938852129183, "奇数与偶数的认识与运用": 0.004729211175872726, "因、倍、质、合、奇、偶数综合运用": 0.0008296861712057415, "说法辨析-": 0.0008296861712057415, "补充知识点1269": 0.0008296861712057415, "3的倍数特征": 0.0023438634336562197, "3的倍数特征（考法需整合）": 0.0021779261994150714, "3的倍数特征-": 0.0021779261994150714, "补充知识点1151": 0.0021779261994150714, "根据因数的特征解决问题": 0.0006637489369645932, "运用因数解决实际问题": 0.0013274978739291864, "补充知识点1039": 0.0006637489369645932, "几百几十数、整百数的加减法口算": 0.0015971458795710522, "几百几十数的加法解决问题": 0.0002281636970815789, "解决直接求和的问题": 0.0002281636970815789, "补充知识点4942": 0.0002281636970815789, "几百几十数的减法解决问题": 0.00012445292568086122, "解决求部分的问题": 0.00012445292568086122, "补充知识点4955": 0.00012445292568086122, "选择合适的长度单位(分米、毫米)": 0.0003941009313227272, "填合适的长度单位": 0.0003941009313227272, "补充知识点21767": 0.0003941009313227272, "补充知识点21768": 0.0003941009313227272, "正方形的周长": 0.0006844910912447367, "正方形周长的应用": 0.00012445292568086122, "由正方形周长公式解决实际问题": 0.00012445292568086122, "补充知识点16121": 0.00012445292568086122, "补充知识点16122": 0.00012445292568086122, "质量单位的换算": 0.001078592022567464, "吨、千克之间的换算与比较": 0.0004978117027234449, "复名数质量单位比较大小": 2.0742154280143537e-05, "复名数质量单位比大小": 2.0742154280143537e-05, "补充知识点13603": 2.0742154280143537e-05, "单名数质量单位比较大小": 0.000269648005641866, "不同质量单位比大小": 0.000269648005641866, "补充知识点13595": 0.000269648005641866, "计量单位间的比大小": 0.0004978117027234449, "千米和米的计算": 0.00018667938852129183, "求其中一个数": 0.00018667938852129183, "补充知识点13785": 0.00018667938852129183, "补充知识点13786": 0.00018667938852129183, "补充知识点13787": 0.00018667938852129183, "长度的估测": 2.0742154280143537e-05, "千米和米的长度估计": 2.0742154280143537e-05, "米的长度估计": 2.0742154280143537e-05, "补充知识点21789": 2.0742154280143537e-05, "补充知识点21790": 2.0742154280143537e-05, "优化问题": 0.0076123706208126776, "租船问题": 0.0019497625023334924, "租船问题与省钱方案": 0.0001659372342411483, "只计算人数的租船方案": 0.0001659372342411483, "补充知识点15004": 0.0001659372342411483, "用列表法解决问题": 0.0003941009313227272, "用列表法解决搭配问题": 0.0003941009313227272, "列表法解决问题": 0.00018667938852129183, "取东西问题、选路线问题": 0.00018667938852129183, "补充知识点14628": 0.00018667938852129183, "行程问题": 0.0021779261994150714, "普通行程问题": 0.001659372342411483, "解决千米相关的实际问题": 0.00024890585136172244, "看图解决计算问题": 0.00024890585136172244, "补充知识点14332": 0.00024890585136172244, "长度单位的换算": 0.0007052332455248803, "毫米、厘米、分米、米之间的进率与换算": 0.00041484308560287074, "解决有关分米、厘米、毫米的问题": 0.0001659372342411483, "计算解决问题": 0.0001659372342411483, "补充知识点21750": 0.0001659372342411483, "毫米和分米的认识": 0.00041484308560287074, "认识分米": 0.0001659372342411483, "用分米作单位测量": 0.0001659372342411483, "补充知识点21719": 0.0001659372342411483, "吨的计算": 0.00012445292568086122, "相同单位简单计算": 0.00012445292568086122, "补充知识点13599": 0.00012445292568086122, "时、分、秒的认识及换算": 0.0015971458795710522, "时、分、秒之间的换算与比较": 0.0007052332455248803, "时、分、秒时间单位换算(单名数)": 0.00037335877704258366, "时和分之间的换算": 0.00037335877704258366, "补充知识点13058": 0.00037335877704258366, "时间快慢的比较": 0.0002903901599220095, "比较时间的长短": 0.00020742154280143537, "时间长短比较": 0.00020742154280143537, "补充知识点13048": 0.00020742154280143537, "分米、厘米和毫米之间的单位计算": 4.1484308560287074e-05, "相同单位的计算": 4.1484308560287074e-05, "补充知识点21746": 4.1484308560287074e-05, "认识毫米": 0.00024890585136172244, "1毫米有多长": 0.00024890585136172244, "补充知识点21725": 0.00024890585136172244, "万以内数加减法的估算": 0.002157184045134928, "用整百数或几百几十数的估算解决问题": 0.000580780319844019, "简单估算解决问题": 0.000580780319844019, "补充知识点7038": 0.000580780319844019, "时、分、秒有关的计算": 0.0012237871025284686, "计算开始或结束时刻": 0.0004770695484433013, "计算开始时刻": 0.0004770695484433013, "补充知识点13154": 0.0004770695484433013, "计算经过的时间": 0.0004770695484433013, "求经过的时间(跨时)": 0.0004770695484433013, "补充知识点13151": 0.0004770695484433013, "秒的认识": 0.0003318744684822966, "体验生活中的时分": 0.00012445292568086122, "判断不同的时间内能做什么": 0.00012445292568086122, "补充知识点13043": 0.00012445292568086122, "认识一个整体的几分之几": 0.0008711704797660285, "认识几分之几": 0.0004978117027234449, "理解几分之几的含义": 0.0004978117027234449, "补充知识点2533": 0.0004978117027234449, "根据正方形的周长公式求正方形的边长": 0.00014519507996100476, "利用正方形周长公式求边长": 0.00014519507996100476, "补充知识点16129": 0.00014519507996100476, "补充知识点16130": 0.00014519507996100476, "根据长方形的周长公式求长方形的长和宽": 0.00012445292568086122, "长方形已知周长计算长或宽": 0.00012445292568086122, "补充知识点16055": 0.00012445292568086122, "补充知识点16056": 0.00012445292568086122, "倍的运算": 0.0013274978739291864, "求一个数的几倍是多少": 0.0010163655597270331, "补充知识点9995": 0.0003318744684822966, "补充知识点9996": 0.0003318744684822966, "两、三位数与一位数的一次进位乘法": 0.0004770695484433013, "解决两位数乘一位数(不连续进位)的实际问题": 8.296861712057415e-05, "箭头所指部分表示的含义": 8.296861712057415e-05, "补充知识点6067": 8.296861712057415e-05, "求一个数是另一个数的几倍": 0.001514177262450478, "看图求倍数(表内)": 8.296861712057415e-05, "看实物图求倍数": 8.296861712057415e-05, "补充知识点9969": 8.296861712057415e-05, "补充知识点9970": 8.296861712057415e-05, "错中求解(加减法各部分间的关系)（万以内）": 0.00018667938852129183, "加数看错": 0.00018667938852129183, "补充知识点23715": 0.00018667938852129183, "补充知识点23716": 0.00018667938852129183, "补充知识点23717": 0.00018667938852129183, "两、三位数的加减混合运算": 0.000539296011283732, "几百几十数的加减混合运算解决问题": 0.00012445292568086122, "补充知识点5117": 0.00012445292568086122, "几百几十数加、减几百几十数的估算计算": 0.0007259753998050237, "估成几百几十数计算": 0.0007259753998050237, "补充知识点7026": 0.0007259753998050237, "用7~9的乘法口诀求商": 0.002468316359337081, "用乘法口诀求商(7、8、9)（移后删）": 0.00170085665097177, "口算": 0.005268507187156458, "补充知识点5688": 0.00170085665097177, "两、三位数的不退位减法": 0.00012445292568086122, "三位数减两、三位数(不退位)的计算": 0.00010371077140071768, "根据文字叙述列式计算": 0.00012445292568086122, "补充知识点5040": 0.00010371077140071768, "两、三位数的一次退位减法": 0.00037335877704258366, "三位数减两、三位数(一次退位)的计算": 0.00024890585136172244, "笔算计算方法": 0.0012860135653688992, "补充知识点5048": 0.00024890585136172244, "两、三位数的一次进位加法": 0.0004978117027234449, "三位数加两、三位数(一次进位)的计算": 0.0003941009313227272, "补充知识点5018": 0.0003941009313227272, "两、三位数的不进位加法": 0.0003318744684822966, "三位数加两、三位数(不进位)的计算": 0.0002281636970815789, "补充知识点5009": 0.0002281636970815789, "万以内数的认识": 0.006430067826844496, "1000以内数的组成": 0.0006637489369645932, "数的组成(1000以内)": 0.0006637489369645932, "已知数，写组成(1000以内)": 0.0006637489369645932, "补充知识点627": 0.0006637489369645932, "几百几十数的减法(笔算)": 0.00012445292568086122, "补充知识点4947": 0.00012445292568086122, "两位数加两位数的口算(进位)": 0.000269648005641866, "口算(进位加)": 0.0003318744684822966, "补充知识点4972": 0.000269648005641866, "两、三位数的连续进位加法": 0.000269648005641866, "三位数加两、三位数(连续进位)的计算": 0.00018667938852129183, "笔算": 0.00018667938852129183, "补充知识点5029": 0.00018667938852129183, "加、减法验算": 0.00020742154280143537, "三位数加两、三位数的验算": 6.222646284043061e-05, "三位数加两、三位数(一次进位)": 6.222646284043061e-05, "补充知识点5091": 6.222646284043061e-05, "三位数加两、三位数(一次进位)的应用": 0.00010371077140071768, "看图解决问题(二)": 0.00010371077140071768, "补充知识点5024": 0.00010371077140071768, "两、三位数的连加运算": 0.00020742154280143537, "三位数连加运算": 6.222646284043061e-05, "简单连加计算": 6.222646284043061e-05, "补充知识点5100": 6.222646284043061e-05, "三位数连加的简单应用": 0.00014519507996100476, "求一共": 0.00024890585136172244, "补充知识点5104": 0.00014519507996100476, "质量及质量的常用单位": 0.00035261662276244013, "吨的认识": 0.00014519507996100476, "认识质量单位吨\"\"": 0.00014519507996100476, "克、千克、吨的辨析": 0.00014519507996100476, "补充知识点13585": 0.00014519507996100476, "千克与吨之间的进率及换算": 8.296861712057415e-05, "单名数换算": 8.296861712057415e-05, "补充知识点13606": 8.296861712057415e-05, "分米、厘米和毫米之间的单位换算": 0.00020742154280143537, "多个含单位的比大小（单名）": 0.00020742154280143537, "补充知识点21740": 0.00020742154280143537, "质量间的计算和应用": 0.0010163655597270331, "解决吨相关的实际问题": 0.0002903901599220095, "有关净含量\"问题\"": 0.0002903901599220095, "补充知识点13631": 0.0002903901599220095, "补充知识点13632": 0.0002903901599220095, "已知一个数的百分之几是多少，求这个数": 0.0009126547883263156, "已知一个数的百分之几是多少，求这个数(数形结合)": 0.0004563273941631578, "补充知识点8600": 0.0004563273941631578, "补充知识点8601": 0.0004563273941631578, "选择合适的质量单位(吨)": 0.00018667938852129183, "填合适的单位": 0.00018667938852129183, "补充知识点13623": 0.00018667938852129183, "补充知识点13624": 0.00018667938852129183, "厘米和米之间的进率与换算": 0.0001659372342411483, "单位换算(米和厘米)": 0.0001659372342411483, "单位换算（单名数）": 0.0001659372342411483, "补充知识点21737": 0.0001659372342411483, "千米和米之间的进率与换算": 0.00012445292568086122, "千米与米的换算": 6.222646284043061e-05, "单名数单位换算": 6.222646284043061e-05, "补充知识点21758": 6.222646284043061e-05, "时、分的认识及换算": 0.000269648005641866, "时与分的换算": 0.00024890585136172244, "时与分之间的换算": 0.00024890585136172244, "补充知识点13031": 0.00024890585136172244, "千米的认识": 0.0002903901599220095, "千米和米的关系": 0.00014519507996100476, "补充知识点21731": 0.00014519507996100476, "千米和米的大小比较": 6.222646284043061e-05, "复名数单位比大小": 6.222646284043061e-05, "补充知识点21755": 6.222646284043061e-05, "生活中的计量单位": 0.00010371077140071768, "认识交通标志": 8.296861712057415e-05, "交通标志表示的含义": 8.296861712057415e-05, "补充知识点13683": 8.296861712057415e-05, "补充知识点13684": 8.296861712057415e-05, "补充知识点13685": 8.296861712057415e-05, "选择合适的长度单位(千米、米、厘米、毫米)": 0.00014519507996100476, "千米单位(生活常识)": 0.00014519507996100476, "补充知识点21773": 0.00014519507996100476, "补充知识点21774": 0.00014519507996100476, "几百几十数的减法(口算)": 2.0742154280143537e-05, "口算计算方法": 2.0742154280143537e-05, "补充知识点4950": 2.0742154280143537e-05, "几百几十数的加法(口算)": 0.00014519507996100476, "补充知识点4938": 0.00014519507996100476, "几百几十数的加法(笔算)": 0.0002281636970815789, "补充知识点4935": 0.0002281636970815789, "两位数减两位数的口算(退位)": 0.00010371077140071768, "探究口算方法，理解算理(退位减)": 0.00010371077140071768, "补充知识点4985": 0.00010371077140071768, "错中求解(两位数、几百几十数)": 0.00014519507996100476, "运算符号看错": 0.0002281636970815789, "补充知识点23760": 0.00014519507996100476, "补充知识点23761": 0.00014519507996100476, "补充知识点23762": 0.00014519507996100476, "解决求等车时间的问题": 4.1484308560287074e-05, "已知两辆车发车的时间间隔和到站时间，求等待时间": 4.1484308560287074e-05, "补充知识点13168": 4.1484308560287074e-05, "植树问题拓展应用": 0.0008296861712057415, "锯木头问题": 0.0001659372342411483, "求锯木头的段数": 0.0001659372342411483, "补充知识点15137": 0.0001659372342411483, "多位数与一位数的乘除混合运算": 0.0006844910912447367, "用乘法和除法两步计算解决问题": 0.0005185538570035884, "选择合适的算式": 0.0005185538570035884, "补充知识点6487": 0.0005185538570035884, "集合问题": 0.0009333969426064591, "运用集合的知识解决简单问题（无圈外部分）": 0.0005600381655638754, "给出韦恩图解决问题": 0.0005600381655638754, "补充知识点14947": 0.0005600381655638754, "补充知识点14948": 0.0005600381655638754, "铁丝围正方形": 6.222646284043061e-05, "已知一根铁丝刚好围成一个正方形，求铁丝长": 6.222646284043061e-05, "补充知识点16153": 6.222646284043061e-05, "补充知识点16154": 6.222646284043061e-05, "补充知识点9943": 0.0004770695484433013, "补充知识点9944": 0.0004770695484433013, "计算长方形的周长": 0.0002281636970815789, "长方形周长辦析": 0.0002281636970815789, "补充知识点16101": 0.0002281636970815789, "补充知识点16102": 0.0002281636970815789, "计算正方形的周长": 0.0002281636970815789, "正方形去掉一角周长变化": 0.0002281636970815789, "补充知识点16133": 0.0002281636970815789, "补充知识点16134": 0.0002281636970815789, "两、三位数的连续退位减法": 0.0002281636970815789, "三位数减两、三位数(连续退位)的计算": 6.222646284043061e-05, "补充知识点5059": 6.222646284043061e-05, "三位数减两、三位数(一次退位)的应用": 0.00012445292568086122, "看图解决问题": 0.0005185538570035884, "补充知识点5055": 0.00012445292568086122, "三位数加两、三位数(不进位)的应用": 0.00010371077140071768, "补充知识点5016": 0.00010371077140071768, "分米、厘米和毫米之间的比较大小": 8.296861712057415e-05, "单名数单位比大小": 8.296861712057415e-05, "补充知识点13755": 8.296861712057415e-05, "补充知识点13756": 8.296861712057415e-05, "补充知识点13757": 8.296861712057415e-05, "时、分、秒时间单位换算(单名数与复名数互化)": 0.00020742154280143537, "时间单位换算(单名数化复名数)": 0.00020742154280143537, "补充知识点13067": 0.00020742154280143537, "时针、分针、秒针的基本走法之间的关系": 0.00012445292568086122, "1分等于60秒": 0.00012445292568086122, "补充知识点13062": 0.00012445292568086122, "认识钟面上的指针": 0.00018667938852129183, "钟面上指针的特点": 0.00018667938852129183, "补充知识点13038": 0.00018667938852129183, "100以内数的组成": 0.006616747215365788, "由数位上数的关系写数(100以內)": 0.0014519507996100475, "已知十位和个位上的数写数": 0.0014519507996100475, "补充知识点514": 0.0014519507996100475, "数数的方法": 0.001389724336769617, "连续数(100以内)": 0.0010371077140071767, "写出一个数前面几个数": 0.0010371077140071767, "补充知识点418": 0.0010371077140071767, "用连除解决实际问题": 0.004998859181514592, "连除的计算与应用": 0.001389724336769617, "连除的计算": 0.001389724336769617, "补充知识点6821": 0.001389724336769617, "两、三位数与一位数连续进位的乘法": 0.0005600381655638754, "有关三位数乘一位数(连续进位)的实际应用": 0.0002281636970815789, "根据文字列式计算": 0.00240608989649665, "补充知识点6110": 0.0002281636970815789, "时间的计算(时分秒)": 8.296861712057415e-05, "借助钟表计算时间": 8.296861712057415e-05, "补充知识点13161": 8.296861712057415e-05, "利用平移巧算周长与面积": 0.0031528074505818176, "巧求面积（割补法、平移法、整体减空白法）": 0.0008296861712057415, "整体减空白法求面积": 0.0008296861712057415, "补充知识点17698": 0.0008296861712057415, "补充知识点17699": 0.0008296861712057415, "补充知识点17700": 0.0008296861712057415, "小数的大小比较（一位小数）": 0.0015349194167306216, "直接比大小": 0.002011988965173923, "补充知识点2266": 0.0015349194167306216, "补充知识点2267": 0.0015349194167306216, "面积单位的选择": 0.003090580987741387, "认识面积单位": 0.002945385907780382, "常用的面积单位及其字母表示": 0.002945385907780382, "补充知识点21883": 0.002945385907780382, "补充知识点21884": 0.002945385907780382, "面积的大小比较": 0.0006222646284043061, "观察法比较面积的大小": 0.0006222646284043061, "补充知识点16225": 0.0006222646284043061, "补充知识点16226": 0.0006222646284043061, "买赠问题": 0.0018045674223724877, "补充知识点6831": 0.0018045674223724877, "两位数乘整十数的应用": 0.0010371077140071767, "找计算结果相同的算式": 0.0010371077140071767, "补充知识点6501": 0.0010371077140071767, "两位数与一位数的乘法口算": 0.001472692953890191, "两位数乘一位数的口算（不进位）": 0.0001659372342411483, "两位数乘一位数(不进位)口算的算理": 0.0001659372342411483, "补充知识点6034": 0.0001659372342411483, "两位数除以一位数(被除数首位能被整除)解决实际问题": 0.0004978117027234449, "对折问题": 0.0004978117027234449, "补充知识点6294": 0.0004978117027234449, "铁丝围长方形": 8.296861712057415e-05, "周长已知，求围成的最大/最小面积（长、宽数的性质被限定）9": 8.296861712057415e-05, "补充知识点16091": 8.296861712057415e-05, "补充知识点16092": 8.296861712057415e-05, "两位数除以一位数的笔算(被除数首位不能被整除)": 0.00041484308560287074, "商的个位是几": 0.00041484308560287074, "补充知识点6317": 0.00041484308560287074, "八个方向描述简单的行走路线": 0.002468316359337081, "正确识别线路与站点顺序": 0.002468316359337081, "补充知识点21318": 0.002468316359337081, "补充知识点21319": 0.002468316359337081, "在地图或平面示意图上辨认方向": 0.0016178880338511958, "根据方向找位置": 0.0016178880338511958, "补充知识点21310": 0.0016178880338511958, "补充知识点21311": 0.0016178880338511958, "认识东北、东南、西北、西南四个方向": 0.0007467175540851673, "指南针与方向": 0.0007467175540851673, "补充知识点21272": 0.0007467175540851673, "补充知识点21273": 0.0007467175540851673, "根据描述确定位置（八个方向）": 0.0006844910912447367, "在方格图中标明具体位置": 0.0006844910912447367, "补充知识点21266": 0.0006844910912447367, "补充知识点21267": 0.0006844910912447367, "相对方向(八个方向)": 0.002157184045134928, "风向与方向": 0.002157184045134928, "补充知识点21280": 0.002157184045134928, "补充知识点21281": 0.002157184045134928, "被除数是0的除法运算": 0.0004563273941631578, "0除以一个数(不是0)": 0.0004563273941631578, "0除以任何不是0的数，都得0": 0.0004563273941631578, "补充知识点6404": 0.0004563273941631578, "三位数与一位数的乘法口算": 0.0009748812511667462, "几百几十数乘一位数的口算(有进位)": 0.0006844910912447367, "由拆分法理解口算算理": 0.0006844910912447367, "补充知识点6135": 0.0006844910912447367, "一位小数的进位加法、退位减法": 0.0016801144966916263, "一位小数加法的计算(进位)": 0.0010163655597270331, "一位小数加法(进位)的算理": 0.0010163655597270331, "补充知识点7168": 0.0010163655597270331, "一位小数的不进位加法、不退位减法": 0.0006430067826844496, "一位小数加法的计算(不进位)": 0.0006015224741241625, "一位小数加法(不进位)的竖式计算": 0.0006015224741241625, "补充知识点7160": 0.0006015224741241625, "一位小数减法的计算(退位)": 4.1484308560287074e-05, "一位小数减法(退位)的竖式计算": 4.1484308560287074e-05, "补充知识点7173": 4.1484308560287074e-05, "两位数乘两位数的估算": 0.005849287507000477, "根据积的范围找算式": 0.0013482400282093297, "补充知识点7071": 0.0013482400282093297, "三位数除以一位数(最高位不能整除)的笔算": 0.0006430067826844496, "补充知识点6365": 0.0006430067826844496, "两位数乘整十（百）数的口算": 0.0011200763311277509, "两位数乘整十数的口算算理": 0.0011200763311277509, "补充知识点6497": 0.0011200763311277509, "两位数乘一位数的口算(有进位)": 0.0010993341768476073, "补充知识点6038": 0.0010993341768476073, "竖式数字谜": 0.001742340959532057, "小数加法竖式谜": 0.0002903901599220095, "小数加法(不进位)竖式谜": 0.0002903901599220095, "补充知识点24469": 0.0002903901599220095, "补充知识点24470": 0.0002903901599220095, "补充知识点24471": 0.0002903901599220095, "小数减法竖式谜": 2.0742154280143537e-05, "小数减法(退位)竖式谜": 2.0742154280143537e-05, "补充知识点24484": 2.0742154280143537e-05, "补充知识点24485": 2.0742154280143537e-05, "补充知识点24486": 2.0742154280143537e-05, "运用一位小数加法解决问题": 0.0017630831138122005, "运用一位小数加法(进位)的计算解决问题": 0.0017630831138122005, "补充知识点7246": 0.0017630831138122005, "错中求解(100以内)": 0.0005185538570035884, "看错2个加数": 0.0005185538570035884, "补充知识点23793": 0.0005185538570035884, "补充知识点23794": 0.0005185538570035884, "补充知识点23795": 0.0005185538570035884, "理解每个数位上数字的含义": 0.0011823027939681814, "补充知识点526": 0.0011823027939681814, "100以内数的读、写法": 0.0007052332455248803, "根据计数器读数、写数(100以内)": 0.0004563273941631578, "根据数对应计数器": 0.0004563273941631578, "补充知识点446": 0.0004563273941631578, "分组数": 0.00035261662276244013, "直接数": 0.00035261662276244013, "补充知识点409": 0.00035261662276244013, "数的相对大小关系": 0.001389724336769617, "数的相对大小关系(100以内)": 0.001389724336769617, "根据数填合适大小关系": 0.001389724336769617, "补充知识点501": 0.001389724336769617, "整十数加、减一位数": 0.001742340959532057, "加减法(整十数加一位数及相应的减法)解决实际问题": 0.0009126547883263156, "列加法算式解决问题": 0.0009126547883263156, "补充知识点4377": 0.0009126547883263156, "整十数之间的加减法": 0.0004770695484433013, "整十数加、减整十数解决实际问题": 0.00020742154280143537, "整十数加整十数解决实际问题": 0.00020742154280143537, "补充知识点4401": 0.00020742154280143537, "十几减7、6的退位减法": 0.0007882018626454544, "十几减7、6的实际问题": 0.0004563273941631578, "对称分析中最多最少": 0.0004563273941631578, "补充知识点4201": 0.0004563273941631578, "十几减8的退位减法": 0.0010163655597270331, "十几减8的实际问题": 0.0004770695484433013, "根据所给算式选择条件/问题9": 0.0004770695484433013, "补充知识点4148": 0.0004770695484433013, "探索规律": 0.0017630831138122005, "数字排列的规律": 0.0009333969426064591, "整数排列的规律": 0.0006844910912447367, "找规律填数（20以内）": 4.1484308560287074e-05, "双重规律": 4.1484308560287074e-05, "补充知识点23289": 4.1484308560287074e-05, "补充知识点23290": 4.1484308560287074e-05, "补充知识点23291": 4.1484308560287074e-05, "“提问题”“填条件”问题": 0.000580780319844019, "根据条件提出问题并解答": 0.0003318744684822966, "看图提出问题并解答": 0.00014519507996100476, "补充知识点13837": 0.00014519507996100476, "补充知识点13838": 0.00014519507996100476, "口算(整十数加一位数及相应的减法)": 0.0002281636970815789, "补充知识点4387": 0.0002281636970815789, "加减法(整十数加一位数及相应的减法)的应用": 0.0006015224741241625, "看图列式": 0.001783825268092344, "补充知识点4391": 0.0006015224741241625, "100以内数的比大小": 0.001472692953890191, "数的大小比较的应用": 0.0004355852398830143, "根据数的大小关系填数阵图": 0.0004355852398830143, "补充知识点469": 0.0004355852398830143, "加减混合运算（20以内）": 0.00020742154280143537, "加减混合连加连减(20以内退位)": 0.00012445292568086122, "补充知识点4355": 0.00012445292568086122, "两位数与整十数的减法": 0.0009126547883263156, "口算两位数减整十数的应用": 0.0005600381655638754, "补充知识点4490": 0.0005600381655638754, "数的表征": 0.0003318744684822966, "根据数选符号表示": 0.0003318744684822966, "补充知识点541": 0.0003318744684822966, "用珠子摆数": 0.0006430067826844496, "用多颗珠子表示不同的两位数": 0.0006430067826844496, "补充知识点574": 0.0006430067826844496, "加、减法的意义和各部分间的关系": 0.005600381655638755, "理解加法与减法之间的互逆关系（十几减8、7、6）": 0.00018667938852129183, "根据不等式填最值": 0.00024890585136172244, "补充知识点8925": 0.00018667938852129183, "补充知识点8926": 0.00018667938852129183, "理解加法与减法之间的互逆关系（十几减5、4、3、2）": 6.222646284043061e-05, "补充知识点8933": 6.222646284043061e-05, "补充知识点8934": 6.222646284043061e-05, "理解加法与减法之间的互逆关系（十几减9）": 0.0001659372342411483, "根据算式填空": 0.0001659372342411483, "补充知识点8913": 0.0001659372342411483, "补充知识点8914": 0.0001659372342411483, "两位数与整十数的加法": 0.0006015224741241625, "口算两位数加整十数(不进位)的应用": 0.00018667938852129183, "补充知识点4455": 0.00018667938852129183, "求还差多少": 2.0742154280143537e-05, "解决含有“至少”的减法实际问题9": 2.0742154280143537e-05, "补充知识点4220": 2.0742154280143537e-05, "10以内数的加、减法": 0.004397336707390429, "9以内加减法实际应用": 2.0742154280143537e-05, "文字应用题(求部分)": 2.0742154280143537e-05, "求现在(已有运算符号)": 2.0742154280143537e-05, "补充知识点3882": 2.0742154280143537e-05, "立体图形中的平面图形": 0.0009956234054468898, "补充知识点15439": 0.0004978117027234449, "补充知识点15440": 0.0004978117027234449, "组合平面图形计数": 0.0001659372342411483, "组合图形中某图形的个数": 0.0001659372342411483, "补充知识点25090": 0.0001659372342411483, "补充知识点25091": 0.0001659372342411483, "补充知识点25092": 0.0001659372342411483, "十几减8的应用": 0.00014519507996100476, "选正确的减法计算的过程": 0.00014519507996100476, "补充知识点4165": 0.00014519507996100476, "不等化等": 0.0006222646284043061, "移多补少（20以内）": 6.222646284043061e-05, "移多补少求多的量": 6.222646284043061e-05, "补充知识点24043": 6.222646284043061e-05, "补充知识点24044": 6.222646284043061e-05, "补充知识点24045": 6.222646284043061e-05, "图文问题": 0.0010578498682873203, "图文算式（100以内的加减法）": 0.000269648005641866, "整体代换法": 0.000269648005641866, "补充知识点13907": 0.000269648005641866, "补充知识点13908": 0.000269648005641866, "数的大小比较(100以内)": 0.0004770695484433013, "补充知识点462": 0.0004770695484433013, "口算两位数加整十数(不进位)解决实际问题": 0.000269648005641866, "两位数加整十数的简单实际应用": 0.000269648005641866, "补充知识点4462": 0.000269648005641866, "100以内数的连加运算": 0.0008296861712057415, "连加解决实际问题": 0.0007882018626454544, "补充知识点4785": 0.0003941009313227272, "口算两位数加一位数(进位)的应用": 0.0004978117027234449, "由得数的范围填数": 0.0004978117027234449, "补充知识点4591": 0.0004978117027234449, "两位数加一位数(进位)解决实际问题": 0.0006015224741241625, "套圈游戏": 0.0006015224741241625, "补充知识点4600": 0.0006015224741241625, "两位数加一位数(不进位)的实际问题": 0.0001659372342411483, "提出问题并列式解答": 0.0001659372342411483, "补充知识点4424": 0.0001659372342411483, "移多补少(求给多少)(100以内)": 8.296861712057415e-05, "移多补少(求给多少)": 8.296861712057415e-05, "补充知识点24016": 8.296861712057415e-05, "补充知识点24017": 8.296861712057415e-05, "补充知识点24018": 8.296861712057415e-05, "移多补少(求多多少)(100以内)": 6.222646284043061e-05, "移多补少(求多多少)": 6.222646284043061e-05, "补充知识点24004": 6.222646284043061e-05, "补充知识点24005": 6.222646284043061e-05, "补充知识点24006": 6.222646284043061e-05, "利用小数加减法解决综合问题": 0.0033602289933832527, "应用小数计算解决简单的加法实际问题": 0.0033602289933832527, "补充知识点7234": 0.0033602289933832527, "四边形内角和的应用": 0.0012860135653688992, "根据四边形内角和计算角的度数": 0.0012860135653688992, "补充知识点17045": 0.0012860135653688992, "补充知识点17046": 0.0012860135653688992, "三角形的底和高": 0.0012860135653688992, "认识三角形的底和高": 0.0012860135653688992, "补充知识点16761": 0.0012860135653688992, "补充知识点16762": 0.0012860135653688992, "小数减法(小数部分位数相同)": 0.00010371077140071768, "位数相同且借位的小数减法的笔算方法(末尾无0)": 0.00010371077140071768, "补充知识点7205": 0.00010371077140071768, "小数加法(小数部分位数相同)": 0.0002281636970815789, "位数相同且进位的小数加法的笔算方法(末尾无0)": 0.0002281636970815789, "补充知识点7198": 0.0002281636970815789, "假设法解鸡兔同笼": 0.0004563273941631578, "假设法解决鸡兔同笼": 0.0004563273941631578, "假设法解决鸡兔同笼问题(动物类)": 0.0004563273941631578, "补充知识点15025": 0.0004563273941631578, "有余数除法的实际应用": 0.0036091348447449753, "有余数除法的应用题": 0.0017215988052519135, "根据信息选正确的算式": 0.0017215988052519135, "补充知识点5927": 0.0017215988052519135, "根据除余关系求被除数": 0.0008711704797660285, "根据关系式商×除数+余数=被除数\"求被除数\"": 0.0008711704797660285, "补充知识点5867": 0.0008711704797660285, "乘加、乘减混合运算": 0.0010371077140071767, "乘加乘减应用题(表内混合运算)": 0.0010371077140071767, "应用题-乘加": 0.0010371077140071767, "补充知识点6868": 0.0010371077140071767, "乘除法的应用题(表内)": 0.0009748812511667462, "乘除混合运算应用": 0.0009748812511667462, "补充知识点5753": 0.0009748812511667462, "乘、除法的意义和各部分间的关系": 0.00651303644396507, "乘、除法各部分间的关系的计算与应用": 0.0011823027939681814, "利用乘、除法各部分间的关系填数": 0.0011823027939681814, "补充知识点9215": 0.0011823027939681814, "补充知识点9216": 0.0011823027939681814, "有关0的运算": 0.00410694654746842, "与0或1运算相关说法的正误判断": 0.00205347327373421, "补充知识点9243": 0.00205347327373421, "补充知识点9244": 0.00205347327373421, "加、减法各部分间的关系的计算与运用": 0.0010578498682873203, "列竖式计算加、减法并验算": 0.0010578498682873203, "补充知识点8963": 0.0010578498682873203, "补充知识点8964": 0.0010578498682873203, "公顷、平方米和平方千米的进率与换算": 0.0010578498682873203, "认识公顷-解决实际问题(求面积)": 0.00035261662276244013, "计算面积后换算": 0.00035261662276244013, "补充知识点21829": 0.00035261662276244013, "面积和面积单位": 0.00012445292568086122, "公顷、平方千米的认识": 0.00012445292568086122, "认识公顷和平方千米": 0.00012445292568086122, "认识1公顷和1平方千米的大小": 0.00012445292568086122, "补充知识点21809": 0.00012445292568086122, "排列组合问题": 0.011117794694156935, "按要求组最大数或最小数(亿以上)": 0.00010371077140071768, "按要求组最大数或最小数(亿以上不含0)": 0.00010371077140071768, "补充知识点14483": 0.00010371077140071768, "补充知识点14484": 0.00010371077140071768, "根据读0个数的组数问题(亿以上)": 8.296861712057415e-05, "根据读0个数的组数问题(亿以上读0)": 8.296861712057415e-05, "补充知识点14493": 8.296861712057415e-05, "补充知识点14494": 8.296861712057415e-05, "大数的认识": 0.0034639397647839706, "整数的数级、数位和计数单位的认识": 0.0014312086453299039, "含万级数位顺序表的应用": 0.00010371077140071768, "识别一个数的位数与最高位": 0.00010371077140071768, "补充知识点821": 0.00010371077140071768, "根据长度画线段": 0.000269648005641866, "画线段、直线和射线": 0.00010371077140071768, "点与线": 0.00010371077140071768, "补充知识点15519": 0.00010371077140071768, "补充知识点15520": 0.00010371077140071768, "公顷与平方米的换算": 0.000269648005641866, "公顷和平方米的单位换算": 0.000269648005641866, "补充知识点21823": 0.000269648005641866, "整数的改写与近似数": 0.002094957582294497, "整数的改写": 0.00041484308560287074, "整万数的改写": 0.0006637489369645932, "补充知识点911": 0.0003318744684822966, "正方形面积正求": 0.0005185538570035884, "边长和面积的关系": 0.0005185538570035884, "补充知识点16387": 0.0005185538570035884, "补充知识点16388": 0.0005185538570035884, "面积单位比较大小(公顷、平方千米和平方米)": 0.00014519507996100476, "多个面积单位比大小": 0.00014519507996100476, "补充知识点13815": 0.00014519507996100476, "补充知识点13816": 0.00014519507996100476, "补充知识点13817": 0.00014519507996100476, "经济问题": 0.004335110244549999, "购物中的买几送几问题": 0.0001659372342411483, "求单价便宜的钱数": 0.0001659372342411483, "补充知识点14209": 0.0001659372342411483, "补充知识点14210": 0.0001659372342411483, "商的变化规律": 0.0018875360394930618, "商不变的规律": 0.00035261662276244013, "探索商不变的规律": 0.00035261662276244013, "补充知识点10290": 0.00035261662276244013, "补充知识点10291": 0.00035261662276244013, "补充知识点10292": 0.00035261662276244013, "一亿有多大": 0.00037335877704258366, "补充知识点909": 0.00012445292568086122, "认识含亿级的数位顺序表": 0.0002903901599220095, "认识含有亿级的数位顺序表": 0.0002903901599220095, "补充知识点809": 0.0002903901599220095, "整数的近似数": 0.0016801144966916263, "求亿以内数的近似数": 0.00020742154280143537, "省略其他数位后面的尾数求近似数": 0.00020742154280143537, "补充知识点926": 0.00020742154280143537, "数位、数级和数位顺序表的认识": 4.1484308560287074e-05, "认识数级(删)": 4.1484308560287074e-05, "补充知识点831": 4.1484308560287074e-05, "计算器与复杂的运算": 0.0004770695484433013, "计算器的初步认识与使用": 8.296861712057415e-05, "计算工具的认识": 8.296861712057415e-05, "补充知识点10539": 8.296861712057415e-05, "补充知识点10540": 8.296861712057415e-05, "补充知识点10541": 8.296861712057415e-05, "1格表示多个单位的单式条形统计图": 0.0009126547883263156, "以一当五的条形统计图": 0.00035261662276244013, "根据统计表绘制以一当五的条形统计图": 0.00035261662276244013, "补充知识点22364": 0.00035261662276244013, "补充知识点22365": 0.00035261662276244013, "以一当多的条形统计图": 0.0004563273941631578, "根据统计表绘制以一当多条形统计图": 0.0004563273941631578, "补充知识点22342": 0.0004563273941631578, "补充知识点22343": 0.0004563273941631578, "梯形的特征及分类": 0.0029246437535002385, "梯形的高及画法": 8.296861712057415e-05, "画梯形的高": 8.296861712057415e-05, "梯形高的特征": 8.296861712057415e-05, "补充知识点16637": 8.296861712057415e-05, "补充知识点16638": 8.296861712057415e-05, "直角梯形和等腰梯形的概念及特点": 6.222646284043061e-05, "认识等腰梯形": 4.1484308560287074e-05, "等腰梯形的认识": 4.1484308560287074e-05, "补充知识点16619": 4.1484308560287074e-05, "补充知识点16620": 4.1484308560287074e-05, "平行与垂直的特征、性质": 0.0013482400282093297, "过直线上或直线外一点作垂线": 0.0006637489369645932, "直线到直线的距离": 2.0742154280143537e-05, "平行线间的距离": 2.0742154280143537e-05, "补充知识点15691": 2.0742154280143537e-05, "补充知识点15692": 2.0742154280143537e-05, "线段、直线、射线的认识及特征": 0.000580780319844019, "认识射线": 0.0003318744684822966, "射线的定义及特征": 0.0003318744684822966, "补充知识点15535": 0.0003318744684822966, "补充知识点15536": 0.0003318744684822966, "亿以上数的读、写法": 0.0003318744684822966, "亿以上数的写法": 0.00018667938852129183, "写出亿以上数的方法": 0.00018667938852129183, "补充知识点776": 0.00018667938852129183, "亿以上数的读法": 8.296861712057415e-05, "判断读0\"个数\"": 8.296861712057415e-05, "补充知识点768": 8.296861712057415e-05, "亿以内数的读、写法": 0.0006430067826844496, "亿以内数的读法": 0.0002281636970815789, "读亿以内数的方法": 0.0002281636970815789, "补充知识点736": 0.0002281636970815789, "十进制计数法": 0.00020742154280143537, "认识十进制计数法": 0.00010371077140071768, "补充知识点851": 0.00010371077140071768, "读出、写出计数器上的数(亿以内)": 0.00018667938852129183, "根据计数器读数、写数": 0.00018667938852129183, "补充知识点718": 0.00018667938852129183, "亿以上数的组成": 4.1484308560287074e-05, "数的组成(亿以上)": 4.1484308560287074e-05, "理解某一数位上的数表示的意义(删)": 4.1484308560287074e-05, "补充知识点792": 4.1484308560287074e-05, "积的变化规律（整数乘法）": 0.000891912634046172, "运用积的变化规律解决问题": 6.222646284043061e-05, "根据积的变化规律解决问题(一个因数变化)": 6.222646284043061e-05, "补充知识点10281": 6.222646284043061e-05, "补充知识点10282": 6.222646284043061e-05, "补充知识点10283": 6.222646284043061e-05, "三位数与两位数的进位乘法": 0.0008089440169255979, "三位数乘两位数积的位数问题": 8.296861712057415e-05, "直接判断积的位数": 8.296861712057415e-05, "补充知识点6642": 8.296861712057415e-05, "自然数的认识": 0.0002903901599220095, "认识自然数": 0.0003318744684822966, "自然数的辨析": 0.00018667938852129183, "补充知识点901": 0.00018667938852129183, "三位数乘两位数，乘数末尾有0": 6.222646284043061e-05, "三位数乘两位数(末尾有0的乘法)": 6.222646284043061e-05, "三位数乘两位数的计算法则(因数末尾有0)": 6.222646284043061e-05, "补充知识点6650": 6.222646284043061e-05, "三位数乘两位数笔算": 0.0006844910912447367, "列竖式计算三位数乘两位数": 0.0006844910912447367, "补充知识点6637": 0.0006844910912447367, "三位数与整十数的口算乘法": 0.00041484308560287074, "三位数乘两位数的口算(几百几十数)": 0.000269648005641866, "整百数乘非整十数的口算": 0.000269648005641866, "补充知识点6620": 0.000269648005641866, "区分近似数与准确数": 4.1484308560287074e-05, "认识准确数": 4.1484308560287074e-05, "补充知识点948": 4.1484308560287074e-05, "根据条件写数(亿以内)": 8.296861712057415e-05, "已知各数位上数的和且数字不重复写最大数或最小数": 8.296861712057415e-05, "补充知识点728": 8.296861712057415e-05, "认识直线": 6.222646284043061e-05, "直线的表示方法": 6.222646284043061e-05, "补充知识点15543": 6.222646284043061e-05, "补充知识点15544": 6.222646284043061e-05, "线段的再认识": 8.296861712057415e-05, "线段的定义及特征": 8.296861712057415e-05, "补充知识点15551": 8.296861712057415e-05, "补充知识点15552": 8.296861712057415e-05, "画线段": 0.0001659372342411483, "画指定长度的线段": 0.0001659372342411483, "补充知识点15513": 0.0001659372342411483, "补充知识点15514": 0.0001659372342411483, "直线、射线、线段之间的关系": 0.00010371077140071768, "线段、射线和直线的区别": 0.00010371077140071768, "补充知识点15559": 0.00010371077140071768, "补充知识点15560": 0.00010371077140071768, "公顷、平方米和平方千米的单位换算": 0.00037335877704258366, "公顷、平方千米和平方米的单位换算": 0.00037335877704258366, "补充知识点21820": 0.00037335877704258366, "选择合适的面积单位(公顷、平方千米)": 0.0002903901599220095, "补充知识点21879": 0.00014519507996100476, "补充知识点21880": 0.00014519507996100476, "大数的比较": 0.0003318744684822966, "两个数比较大小(亿以内)": 0.00020742154280143537, "亿以内的数比大小的方法": 0.00020742154280143537, "补充知识点868": 0.00020742154280143537, "按要求组最大数或最小数(亿以内)": 0.0001659372342411483, "按要求组最大数或最小数(亿以内不含0)": 0.0001659372342411483, "补充知识点14457": 0.0001659372342411483, "补充知识点14458": 0.0001659372342411483, "比较大小的灵活运用(亿以内)": 8.296861712057415e-05, "比大小后找最大数或最小数的应用题": 8.296861712057415e-05, "补充知识点855": 8.296861712057415e-05, "亿以内数的写法": 0.00012445292568086122, "判断写0\"个数\"": 0.00012445292568086122, "补充知识点744": 0.00012445292568086122, "根据读0个数的组数问题(亿以内)": 0.0001659372342411483, "根据读0个数的组数问题(亿以内读0)": 0.0001659372342411483, "补充知识点14475": 0.0001659372342411483, "补充知识点14476": 0.0001659372342411483, "分段计费问题": 0.0012652714110887556, "分段计费问题（小数乘法）": 0.0009956234054468898, "用小数乘加、乘减解决分段计费问题": 0.0009956234054468898, "拍照中的加洗照片问题9": 0.0009956234054468898, "补充知识点15161": 0.0009956234054468898, "分段计费问题（小数除法）": 0.000269648005641866, "小数运算解决分段计费问题（小数除法）": 0.000269648005641866, "由费用，反求用量/里程/重量（时间/里程/水量/邮递等）-": 0.000269648005641866, "补充知识点15173": 0.000269648005641866, "用计算器探究规律": 0.0003318744684822966, "用计算器计算小数乘除法": 0.0001659372342411483, "根据已知算式结果探索规律，根据规律再计算": 0.0002281636970815789, "补充知识点10590": 0.0001659372342411483, "补充知识点10591": 0.0001659372342411483, "小数的估算及应用": 0.0007052332455248803, "估算解决实际问题": 0.000580780319844019, "实际问题中的估算方法": 0.000580780319844019, "补充知识点7666": 0.000580780319844019, "补充知识点7667": 0.000580780319844019, "小数乘整数的实际应用": 0.0006844910912447367, "补充知识点7339": 0.0006844910912447367, "积的近似数": 0.00035261662276244013, "用四舍五入\"法取积的近似值的实际应用（小数乘法）\"": 0.00018667938852129183, "探索生活问题中的近似值": 0.00018667938852129183, "补充知识点7477": 0.00018667938852129183, "小数与小数的乘法": 0.0011408184854078945, "小数乘小数的运用-": 0.00014519507996100476, "小数乘小数积的估算9": 0.00014519507996100476, "补充知识点7414": 0.00014519507996100476, "除数是小数的除法运用9": 0.00010371077140071768, "找出商在数线上的大概位置（除数是小数）9": 0.00010371077140071768, "补充知识点7579": 0.00010371077140071768, "列方程解决稍复杂的实际问题": 0.00024890585136172244, "线段图与方程": 0.0001659372342411483, "方程解决折信纸问题-": 0.0001659372342411483, "补充知识点11648": 0.0001659372342411483, "补充知识点11649": 0.0001659372342411483, "补充知识点11650": 0.0001659372342411483, "数对与位置": 0.002717222210698803, "用数对表示位置": 0.000850428325485885, "数对与棋盘中的位置": 8.296861712057415e-05, "根据象棋的走步规则写数对": 8.296861712057415e-05, "补充知识点21390": 8.296861712057415e-05, "补充知识点21391": 8.296861712057415e-05, "认识数对": 0.0004563273941631578, "补充知识点21366": 0.0004563273941631578, "补充知识点21367": 0.0004563273941631578, "可能性": 0.0021156997365746407, "可能性的大小": 0.0007467175540851673, "判断事件发生的可能性的大小": 0.0013067557196490428, "多种物品判断可能性大小": 0.0005600381655638754, "补充知识点23010": 0.0005600381655638754, "补充知识点23011": 0.0005600381655638754, "最不利原则": 0.002281636970815789, "最不利": 0.002281636970815789, "袜子、手套问题": 0.002281636970815789, "补充知识点15311": 0.002281636970815789, "根据可能性的大小设计方案": 0.00012445292568086122, "根据可能性的大小设计其他游戏": 0.00012445292568086122, "补充知识点22998": 0.00012445292568086122, "补充知识点22999": 0.00012445292568086122, "有余数的小数除法": 0.00012445292568086122, "计算求余数（除数是小数的除法）": 0.00012445292568086122, "补充知识点7565": 0.00012445292568086122, "判断商与1的大小关系（除数是小数）": 6.222646284043061e-05, "选出商大于1的算式9": 6.222646284043061e-05, "补充知识点7559": 6.222646284043061e-05, "用“进一法”解决问题": 0.0004563273941631578, "进一法（小数）": 0.0004563273941631578, "合理选择取近似值的方法": 0.0004563273941631578, "补充知识点7621": 0.0004563273941631578, "去尾法（小数）": 0.0001659372342411483, "选择合适的取商的方法": 0.0001659372342411483, "补充知识点7629": 0.0001659372342411483, "利用小数四则混合运算解决问题": 0.0007052332455248803, "小数运算解决其他实际问题": 0.000539296011283732, "小数四则混合运算解决实际问题": 0.000539296011283732, "补充知识点7792": 0.000539296011283732, "补充知识点7793": 0.000539296011283732, "梯形的面积": 0.0024268320507767937, "梯形面积的应用": 0.0023438634336562197, "梯形面积公式应用(逆用)": 0.00041484308560287074, "已知面积和底，计算高(含单位换算)": 0.00041484308560287074, "补充知识点16704": 0.00041484308560287074, "梯形面积公式应用(正用)": 0.0004978117027234449, "先求出底和高，再运用面积公式计算": 0.0004978117027234449, "补充知识点16711": 0.0004978117027234449, "错中求解（含字母/未知数）": 8.296861712057415e-05, "看错运算顺序，求运算结果的差-": 8.296861712057415e-05, "补充知识点23790": 8.296861712057415e-05, "补充知识点23791": 8.296861712057415e-05, "补充知识点23792": 8.296861712057415e-05, "三角形的面积": 0.002364605587936363, "三角形面积的应用": 0.0016178880338511958, "三角形与平行四边形的面积": 0.0007674597083653108, "底(或高)相等，知高（或底）间关系，求面积间关系": 0.0007674597083653108, "补充知识点17112": 0.0007674597083653108, "平行四边形": 0.002157184045134928, "平行四边形的面积": 0.001514177262450478, "平行四边形面积的应用": 0.0006222646284043061, "等底等高的平行四边形的面积": 6.222646284043061e-05, "等底等高的平行四边形面积计算": 6.222646284043061e-05, "补充知识点16555": 6.222646284043061e-05, "梯形的底变化或高变化": 0.0002903901599220095, "梯形底之和不变，高不变，求面积变化": 0.0002903901599220095, "补充知识点16694": 0.0002903901599220095, "平行四边形面积的计算": 0.000891912634046172, "平行四边形面积公式正用": 0.0005185538570035884, "已知底和对应高求平行四边形面积（数图结合）": 0.0005185538570035884, "补充知识点16546": 0.0005185538570035884, "平行四边形面积公式逆用": 0.00031113231420215305, "已知高和面积，求底": 0.00031113231420215305, "补充知识点16541": 0.00031113231420215305, "平行四边形、长方形的面积关系（长拉平/平拉长）": 0.00024890585136172244, "判断平行四边形的框架拉成长方形后面积和周长的变化": 0.00024890585136172244, "补充知识点16568": 0.00024890585136172244, "平行线间图形面积问题（含梯、三、平行四边形）": 0.000539296011283732, "已知三角形面积，求梯形面积": 0.000539296011283732, "补充知识点16684": 0.000539296011283732, "商不变的性质（小数除法）-": 0.0003318744684822966, "运用商不变的性质计算（除数是小数）9": 0.0003318744684822966, "补充知识点10410": 0.0003318744684822966, "补充知识点10411": 0.0003318744684822966, "补充知识点10412": 0.0003318744684822966, "积的变化规律（小数乘法）": 0.000539296011283732, "积的变化规律（小数乘法）9": 0.000539296011283732, "应用乘数和积的变化规律计算9": 0.000539296011283732, "补充知识点7465": 0.000539296011283732, "三角形面积的计算": 0.0007467175540851673, "三角形面积公式正用": 0.0003318744684822966, "已知底和对应高，求三角面积": 0.0003318744684822966, "补充知识点17084": 0.0003318744684822966, "事件的确定性与不确定性": 0.0008296861712057415, "摸球问题": 0.00035261662276244013, "判断摸球中奖的可能性的大小": 0.00035261662276244013, "补充知识点22920": 0.00035261662276244013, "补充知识点22921": 0.00035261662276244013, "补充知识点22922": 0.00035261662276244013, "根据数对找位置": 0.0014104664910497605, "数对与图形的顶点": 0.0002903901599220095, "用数对表示长方形的顶点": 0.0002903901599220095, "补充知识点21448": 0.0002903901599220095, "补充知识点21449": 0.0002903901599220095, "小数乘法运算律": 0.001161560639688038, "整数乘法运算定律推广到小数乘法": 0.001161560639688038, "巧用小数乘法运算律进行简算": 0.0004978117027234449, "巧用运算定律计算-": 0.0004978117027234449, "补充知识点9732": 0.0004978117027234449, "计算器的复杂运算": 6.222646284043061e-05, "计算器按键损坏问题": 6.222646284043061e-05, "利用乘法的意义解决按键损坏问题": 6.222646284043061e-05, "补充知识点10542": 6.222646284043061e-05, "补充知识点10543": 6.222646284043061e-05, "不规则图形的面积": 0.0007674597083653108, "分割法求不规则图形面积": 0.0006222646284043061, "方格中用分割法求不规则图形面积": 0.00035261662276244013, "补充知识点17830": 0.00035261662276244013, "补充知识点17831": 0.00035261662276244013, "补充知识点17832": 0.00035261662276244013, "解含括号的方程": 0.0001659372342411483, "解方程：a(x±b)=c这种类型(a≠0)": 0.0001659372342411483, "根据文字叙述列方程并解答": 0.0006844910912447367, "补充知识点11151": 0.0001659372342411483, "补充知识点11152": 0.0001659372342411483, "用字母表示单价、数量、总价间的关系": 0.00014519507996100476, "用字母表示求单价": 0.00014519507996100476, "补充知识点10626": 0.00014519507996100476, "补充知识点10627": 0.00014519507996100476, "列方程解含一个未知数的问题": 0.0018875360394930618, "列方程解决实际问题ax+ab=c或a(x＋b)=c": 0.00041484308560287074, "写等量关系并列方程": 0.0006015224741241625, "补充知识点11285": 0.00041484308560287074, "补充知识点11286": 0.00041484308560287074, "补充知识点11287": 0.00041484308560287074, "数对与行列": 0.00024890585136172244, "根据数对确定不同位置的关系": 0.00024890585136172244, "补充知识点21476": 0.00024890585136172244, "补充知识点21477": 0.00024890585136172244, "数对与位置变化的规律": 0.00012445292568086122, "第二个数加减涉及上下移动": 0.00012445292568086122, "补充知识点21466": 0.00012445292568086122, "补充知识点21467": 0.00012445292568086122, "还原小数近似数的问题": 0.00018667938852129183, "根据积的近似值反求原数": 0.00018667938852129183, "已知三位小数的近似值求原数的最值": 0.00018667938852129183, "补充知识点7460": 0.00018667938852129183, "小数四则混合运算（乘法运算律）": 0.0006637489369645932, "利用小数乘法结合律进行简算": 0.0006637489369645932, "补充知识点9743": 0.0006637489369645932, "除数是两位数的连除": 6.222646284043061e-05, "用连除的知识简算(除数是两位数)": 6.222646284043061e-05, "比较两组算式的结果，总结出连除的性质": 6.222646284043061e-05, "补充知识点6839": 6.222646284043061e-05, "解决差倍问题（小数除法）-": 0.0001659372342411483, "小数运算解决和差倍问题": 0.0001659372342411483, "补充知识点7663": 0.0001659372342411483, "补充知识点7664": 0.0001659372342411483, "除数是整数的小数除法": 0.00037335877704258366, "除数是整数的小数除法计算9": 0.00018667938852129183, "补充知识点7503": 0.00018667938852129183, "列方程解决实际问题ax±b=c(a≠0)": 0.0009333969426064591, "用方程解决有关几倍多几的问题": 0.0009333969426064591, "补充知识点11348": 0.0009333969426064591, "补充知识点11349": 0.0009333969426064591, "补充知识点11350": 0.0009333969426064591, "求一个数的百分之几是多少": 0.0008711704797660285, "已知总量、分量占总量百分率及另外两量比，求分量": 0.0004355852398830143, "补充知识点8538": 0.0004355852398830143, "补充知识点8539": 0.0004355852398830143, "小数混合运算解决问题(不含除法)": 0.0001659372342411483, "两步运算解决简单的实际问题": 0.0001659372342411483, "补充知识点7754": 0.0001659372342411483, "补充知识点7755": 0.0001659372342411483, "小数乘小数的计算9": 0.0005600381655638754, "列坚式计算（小数乘小数）9": 0.0005600381655638754, "补充知识点7397": 0.0005600381655638754, "归一归总问题": 0.0004563273941631578, "小数运算解决归一归总问题": 0.00010371077140071768, "先归一再归总": 0.00010371077140071768, "补充知识点25767": 0.00010371077140071768, "补充知识点25768": 0.00010371077140071768, "补充知识点25769": 0.00010371077140071768, "等式的意义及性质": 0.00024890585136172244, "等式的性质1": 0.0003318744684822966, "根据等式的性质1的补全式子": 0.0001659372342411483, "补充知识点11015": 0.0001659372342411483, "补充知识点11016": 0.0001659372342411483, "事件发生的不确定性和确定性": 0.0002903901599220095, "用可能描述确定不确定现象": 0.0002903901599220095, "补充知识点22980": 0.0002903901599220095, "补充知识点22981": 0.0002903901599220095, "补充知识点22982": 0.0002903901599220095, "小数乘法中倍的应用": 0.0002903901599220095, "小数连乘解决倍数问题": 0.0002903901599220095, "补充知识点7388": 0.0002903901599220095, "利用小数与小数的乘法解决实际问题": 0.0002903901599220095, "小数乘小数解决实际问题": 0.0002903901599220095, "已知份数和每份量，求总量": 0.0002903901599220095, "补充知识点7426": 0.0002903901599220095, "积的小数位数与乘数小数位数的关系": 0.00018667938852129183, "小数点的位置": 0.00018667938852129183, "判断积是几位小数9": 0.00018667938852129183, "补充知识点7431": 0.00018667938852129183, "小数乘小数的算理、算法": 0.00014519507996100476, "探索竖式的算理算法": 0.00014519507996100476, "补充知识点7405": 0.00014519507996100476, "判断商与1的大小关系（除数是整数的小数除法）9": 2.0742154280143537e-05, "由被除数与除数的关系，判断商与1的关系": 2.0742154280143537e-05, "补充知识点7509": 2.0742154280143537e-05, "小数乘整数的运用9": 0.00012445292568086122, "改写算式9": 0.00012445292568086122, "补充知识点7371": 0.00012445292568086122, "分数的乘、除法的混合运算": 0.0005185538570035884, "分数乘除混合运算的应用题": 0.0002903901599220095, "分数乘法或除法应用题（涉及分率）": 0.0002903901599220095, "补充知识点8346": 0.0002903901599220095, "补充知识点8347": 0.0002903901599220095, "求比一个数多/少几分之几的数是多少": 0.0007467175540851673, "求比一个数多(或少)几分之几的数是多少": 0.0006430067826844496, "根据条件列关系式-": 0.0006430067826844496, "补充知识点8216": 0.0006430067826844496, "分数乘小数": 0.00024890585136172244, "分数乘小数的计算": 0.00024890585136172244, "分数乘小数的计算题": 0.00024890585136172244, "补充知识点8134": 0.00024890585136172244, "工程问题": 0.0007467175540851673, "两人合作工程问题": 0.0005600381655638754, "两人独做加合作完成工程问题": 0.0005600381655638754, "补充知识点15347": 0.0005600381655638754, "补充知识点15348": 0.0005600381655638754, "已知一个数的几分之几是多少，求这个数": 0.0004355852398830143, "已知一个数的几分之几是多少，求这个数（考法需整合）": 0.0004355852398830143, "利用数形结合求这个数": 0.0004355852398830143, "补充知识点8293": 0.0004355852398830143, "分数加减乘混合运算的应用题": 0.0004770695484433013, "分数加减乘混合运算的应用题(行程问题)": 0.0004770695484433013, "补充知识点8370": 0.0004770695484433013, "补充知识点8371": 0.0004770695484433013, "分数与整数的除法": 0.0004978117027234449, "分数除以整数的计算": 0.00014519507996100476, "分数除以整数的计算法则": 0.00014519507996100476, "补充知识点8235": 0.00014519507996100476, "倒数的认识及应用": 0.0017215988052519135, "倒数的认识": 0.0009956234054468898, "倒数的概念": 0.00020742154280143537, "根据定义判断是否互为倒数": 0.00020742154280143537, "补充知识点3247": 0.00020742154280143537, "倒数的比大小9": 0.0005185538570035884, "运用倒数的意义比大小": 0.0005185538570035884, "补充知识点3260": 0.0005185538570035884, "分数乘分数": 0.000850428325485885, "分数乘分数的比较大小": 0.00035261662276244013, "分数乘分数的计算比大小(两步计算)": 0.00035261662276244013, "补充知识点8120": 0.00035261662276244013, "用方向和距离描述某个点的位置": 0.0007467175540851673, "用方向和距离描述一个点的位置（填写）": 0.0007467175540851673, "补充知识点21534": 0.0007467175540851673, "补充知识点21535": 0.0007467175540851673, "补充知识点21536": 0.0007467175540851673, "连续求一个数的几分之几是多少的问题": 0.0003941009313227272, "连续求一个数的几分之几是多少": 0.0003941009313227272, "数形结合连续求一个数的几分之几": 0.0003941009313227272, "补充知识点8198": 0.0003941009313227272, "分数乘法运算律": 0.0003941009313227272, "整数乘法运算定律推广到分数乘法": 0.0003941009313227272, "应用分数乘法分配律进行简便计算": 0.0001659372342411483, "分数乘法分配律简便计算(括号减)": 0.0001659372342411483, "补充知识点9807": 0.0001659372342411483, "分数与整数的乘法": 0.000539296011283732, "分数乘整数": 0.0004770695484433013, "分数乘整数的计算": 0.0002281636970815789, "分数乘整数的计算方法": 0.0002281636970815789, "求一个数比另一个数多(或少)几分之几": 0.00024890585136172244, "两数未知（根据两数关系画图）": 0.00024890585136172244, "补充知识点8054": 0.00024890585136172244, "分数乘分数的计算": 0.00020742154280143537, "分数乘分数的计算过程正误的判断和修改": 0.00020742154280143537, "补充知识点8096": 0.00020742154280143537, "分数与分数的除法": 0.0008711704797660285, "一个数除以分数的计算": 0.00035261662276244013, "分数除以分数的计算": 0.00035261662276244013, "补充知识点8252": 0.00035261662276244013, "分数乘分数的运用": 8.296861712057415e-05, "分数乘分数进行单位换算": 8.296861712057415e-05, "补充知识点8111": 8.296861712057415e-05, "同分母分数加、减法": 0.004086204393188277, "同分母分数加法": 0.000269648005641866, "同分母分数加法的算理": 0.000269648005641866, "补充知识点7844": 0.000269648005641866, "分数乘除解归一归总问题": 0.00014519507996100476, "分数乘除法，解决先归一再归总问题": 0.00014519507996100476, "补充知识点25728": 0.00014519507996100476, "补充知识点25729": 0.00014519507996100476, "补充知识点25730": 0.00014519507996100476, "分数乘整数的意义": 0.00014519507996100476, "分数乘整数的意义理解": 0.00014519507996100476, "圆的周长、圆周率": 0.0006637489369645932, "圆的周长公式": 0.0005185538570035884, "已知圆的半径求周长": 0.0005185538570035884, "补充知识点17219": 0.0005185538570035884, "圆的概念及特点": 0.0006430067826844496, "长(正)方形中的圆": 0.00020742154280143537, "在长方形中剪最大的圆确定半径或直径": 0.00020742154280143537, "补充知识点17172": 0.00020742154280143537, "补充知识点17173": 0.00020742154280143537, "与比有关的行程问题-": 0.0007259753998050237, "已知工时和工作量，求工效之比（移后删）": 0.0007259753998050237, "补充知识点12023": 0.0007259753998050237, "补充知识点12024": 0.0007259753998050237, "高级单位换算低级单位": 0.0006222646284043061, "高级单位换算成低级单位的方法": 0.0006222646284043061, "补充知识点1806": 0.0006222646284043061, "补充知识点1807": 0.0006222646284043061, "已知比一个数多/少几分之几是多少，求这个数": 0.0006222646284043061, "已知比一个数多(少)几分之几的数是多少，求这个数": 0.0006222646284043061, "列数量关系式": 0.0006222646284043061, "补充知识点8317": 0.0006222646284043061, "圆的周长与面积的综合运用": 0.0001659372342411483, "已知周长求圆的面积(简单)": 0.0001659372342411483, "补充知识点17293": 0.0001659372342411483, "圆的周长的应用": 0.0006637489369645932, "圆的周长公式逆向应用": 0.00018667938852129183, "已知圆周长求半径": 0.00018667938852129183, "补充知识点17268": 0.00018667938852129183, "综合应用分数乘法交换、结合律进行简便计算": 8.296861712057415e-05, "应用分数乘法交换律简便计算": 8.296861712057415e-05, "补充知识点9813": 8.296861712057415e-05, "分数的平均分": 0.00018667938852129183, "分数除以整数的意义": 0.00018667938852129183, "数形结合表示分数除以整数的算理": 0.00018667938852129183, "补充知识点8230": 0.00018667938852129183, "求一个数的倒数": 0.000269648005641866, "写特殊数的倒数": 0.000269648005641866, "补充知识点3253": 0.000269648005641866, "分数乘分数的意义": 0.00020742154280143537, "分数乘分数的意义描述": 0.00020742154280143537, "补充知识点8102": 0.00020742154280143537, "因数和积的大小关系（分数乘法）": 0.00018667938852129183, "分数乘法中因数与积的大小关系": 0.00018667938852129183, "乘等于1的数": 0.00018667938852129183, "补充知识点8152": 0.00018667938852129183, "稍复杂的求一个数的几分之几是多少": 0.0003318744684822966, "两物总量相等用法不同，比较剩余的多少9": 0.0003318744684822966, "补充知识点8182": 0.0003318744684822966, "化简比(整数)": 0.00018667938852129183, "直接化简整数比": 0.00018667938852129183, "补充知识点11799": 0.00018667938852129183, "补充知识点11800": 0.00018667938852129183, "求一个数是另一个数的几分之几": 0.0046877268673124394, "同一个量，转化为不同单位1\"的几分之几\"": 0.0046877268673124394, "补充知识点8066": 0.0046877268673124394, "比的基本性质": 0.0006430067826844496, "运用比的基本性质判断比值变化情况-": 0.0001659372342411483, "补充知识点11745": 0.0001659372342411483, "补充知识点11746": 0.0001659372342411483, "一个数除以分数的应用": 0.0004355852398830143, "整数除以分数的应用(两步计算)": 0.0004355852398830143, "补充知识点8269": 0.0004355852398830143, "整数乘分数": 6.222646284043061e-05, "整数乘分数的实际应用": 6.222646284043061e-05, "根据整数乘分数的意义解决问题": 6.222646284043061e-05, "分数乘除混合运算": 0.0002281636970815789, "特殊技巧计算的分数乘除混合运算(拆分/凑整/约分)": 0.0002281636970815789, "补充知识点8338": 0.0002281636970815789, "补充知识点8339": 0.0002281636970815789, "分数方程解决实际应用": 0.0001659372342411483, "已知一个量和两个量的关系，求个某量": 0.0001659372342411483, "补充知识点11177": 0.0001659372342411483, "补充知识点11178": 0.0001659372342411483, "分数的连乘运算": 0.000269648005641866, "分数连乘的计算": 0.00014519507996100476, "分数连乘(稍复杂的含整数)": 0.00014519507996100476, "补充知识点8145": 0.00014519507996100476, "分数提取公因数": 0.00014519507996100476, "改写后提取": 0.00014519507996100476, "补充知识点9803": 0.00014519507996100476, "小数连减简算": 0.0006637489369645932, "小数连减简算(减法的性质)": 0.0006637489369645932, "补充知识点9722": 0.0006637489369645932, "分数混合运算(不含分数除法)": 0.0001659372342411483, "脱式计算（含括号）": 0.0001659372342411483, "补充知识点8358": 0.0001659372342411483, "补充知识点8359": 0.0001659372342411483, "路线图": 0.0003318744684822966, "根据方向、角度和距离画线路图": 0.0001659372342411483, "绘制路线图": 0.00014519507996100476, "根据描述的方向和距离绘制路线图": 0.00014519507996100476, "补充知识点21524": 0.00014519507996100476, "补充知识点21525": 0.00014519507996100476, "判断可能的分量比": 0.0001659372342411483, "根据两个分量的差判断可能的分量比": 0.0001659372342411483, "补充知识点11985": 0.0001659372342411483, "补充知识点11986": 0.0001659372342411483, "用工程问题的思想解进出水管问题": 4.1484308560287074e-05, "用两根水管先后打开的进出水问题": 4.1484308560287074e-05, "补充知识点15367": 4.1484308560287074e-05, "补充知识点15368": 4.1484308560287074e-05, "分数的连除运算": 6.222646284043061e-05, "分数连除": 6.222646284043061e-05, "数形结合的分数的连除": 6.222646284043061e-05, "补充知识点8282": 6.222646284043061e-05, "和差倍问题": 0.0003318744684822966, "分数除法之和倍、差倍问题": 0.00014519507996100476, "分数除法之和倍问题": 0.00014519507996100476, "补充知识点14105": 0.00014519507996100476, "补充知识点14106": 0.00014519507996100476, "一个数除以分数的意义": 4.1484308560287074e-05, "数形结合表示一个数除以分数的算理": 4.1484308560287074e-05, "补充知识点8265": 4.1484308560287074e-05, "分数除以整数的应用": 0.0002903901599220095, "分数除以整数的应用(辦析数量关系型)": 0.0002903901599220095, "补充知识点8242": 0.0002903901599220095, "通过转换单位1\"求一个数是另一个数的几分之几\"": 6.222646284043061e-05, "补充知识点8091": 6.222646284043061e-05, "与倒数有关的综合计算": 0.0007259753998050237, "倒数的运用9": 0.0007259753998050237, "已知一个数和其倒数的分数单位求这个数9": 0.0007259753998050237, "补充知识点3267": 0.0007259753998050237, "根据方向、角度和距离描述路线图": 0.0001659372342411483, "描述路线图": 0.0001659372342411483, "根据路线图判断描述路线是否正确": 0.0001659372342411483, "补充知识点21514": 0.0001659372342411483, "补充知识点21515": 0.0001659372342411483, "解决商品提价、降价问题": 8.296861712057415e-05, "商品先升后降相同分率后价格变化": 8.296861712057415e-05, "补充知识点8207": 8.296861712057415e-05, "小数除加、除减混合运算": 4.1484308560287074e-05, "补充知识点7734": 4.1484308560287074e-05, "补充知识点7735": 4.1484308560287074e-05, "列方程解追击问题": 0.0003941009313227272, "用工程问题的思想解行程相遇问题": 0.00020742154280143537, "求相遇时的路程": 0.00020742154280143537, "补充知识点11576": 0.00020742154280143537, "补充知识点11577": 0.00020742154280143537, "补充知识点11578": 0.00020742154280143537, "错中求解问题（小数除法）": 0.00010371077140071768, "小数运算解决错中求解问题（除数是小数）": 6.222646284043061e-05, "看错乘除法": 0.00010371077140071768, "补充知识点7655": 6.222646284043061e-05, "商的变化规律（小数除法）": 0.00020742154280143537, "在方框里填合适的数9": 0.00020742154280143537, "补充知识点10395": 0.00020742154280143537, "补充知识点10396": 0.00020742154280143537, "补充知识点10397": 0.00020742154280143537, "小数乘小数的估算": 0.00010371077140071768, "合理估算找出恰当的结果": 0.00010371077140071768, "补充知识点7678": 0.00010371077140071768, "补充知识点7679": 0.00010371077140071768, "用四舍五入\"法取积的近似值（小数乘法）\"": 0.0001659372342411483, "算出小数乘小数的积，并按要求写出不同的近似值-": 0.0001659372342411483, "补充知识点7475": 0.0001659372342411483, "三角形底、高变化引起的面积变化": 0.00024890585136172244, "底和高均改变，计算三角形的面积": 0.00024890585136172244, "补充知识点17093": 0.00024890585136172244, "判定被除数的最大值和最小值": 8.296861712057415e-05, "被除数的最大值与最小值（小数除法）": 8.296861712057415e-05, "由商的近似值反求被除数最大值与最小值": 8.296861712057415e-05, "补充知识点7616": 8.296861712057415e-05, "用四舍五入\"法取商的近似值实际应用（小数除法）\"": 4.1484308560287074e-05, "补充知识点7613": 4.1484308560287074e-05, "除数是整数的小数运算比大小9": 2.0742154280143537e-05, "选出结果最大/最小的算式": 2.0742154280143537e-05, "补充知识点7516": 2.0742154280143537e-05, "平均数问题": 0.0009748812511667462, "小数运算解决平均数问题": 2.0742154280143537e-05, "求平均速度": 2.0742154280143537e-05, "补充知识点25863": 2.0742154280143537e-05, "补充知识点25864": 2.0742154280143537e-05, "补充知识点25865": 2.0742154280143537e-05, "除数是整数的小数除法运用9": 8.296861712057415e-05, "知小数点移动后数的变化量，求原数": 8.296861712057415e-05, "补充知识点7519": 8.296861712057415e-05, "小数乘法竖式谜": 2.0742154280143537e-05, "小数乘整数的竖式谜": 2.0742154280143537e-05, "补充知识点24454": 2.0742154280143537e-05, "补充知识点24455": 2.0742154280143537e-05, "补充知识点24456": 2.0742154280143537e-05, "根据数对确定位置": 0.000269648005641866, "数对与物体的位置": 0.000269648005641866, "补充知识点21418": 0.000269648005641866, "补充知识点21419": 0.000269648005641866, "示意图中物体的位置": 8.296861712057415e-05, "根据示意图直接写数对": 8.296861712057415e-05, "补充知识点21376": 8.296861712057415e-05, "补充知识点21377": 8.296861712057415e-05, "路线图中的数对问题": 0.00014519507996100476, "以所给数对为参考点，用方向和距离描述其它点的位置": 0.00014519507996100476, "补充知识点21354": 0.00014519507996100476, "补充知识点21355": 0.00014519507996100476, "数对与平移": 0.00020742154280143537, "根据数对确定图形的位置，并确定平移后图形的位置": 0.00020742154280143537, "补充知识点21442": 0.00020742154280143537, "补充知识点21443": 0.00020742154280143537, "具体情境中物体的位置": 0.00018667938852129183, "数对与教室中的位置": 0.00018667938852129183, "补充知识点21430": 0.00018667938852129183, "补充知识点21431": 0.00018667938852129183, "认识轴对称图形和对称轴(平面图形)": 0.0007467175540851673, "轴对称图形、对称轴的定义及辨析": 0.0007467175540851673, "补充知识点20344": 0.0007467175540851673, "补充知识点20345": 0.0007467175540851673, "补充知识点20346": 0.0007467175540851673, "小数乘整数的算理、算法9": 4.1484308560287074e-05, "探索竖式的算理算法9": 4.1484308560287074e-05, "补充知识点7366": 4.1484308560287074e-05, "根据条件写数(亿以上)": 6.222646284043061e-05, "已知部分数位上的数及任意相邻三个数的和写数(删)": 6.222646284043061e-05, "补充知识点781": 6.222646284043061e-05, "运用积的变化规律解决图形问题": 0.00018667938852129183, "已知长或宽的变化和原面积，求现面积": 0.00018667938852129183, "补充知识点10269": 0.00018667938852129183, "补充知识点10270": 0.00018667938852129183, "补充知识点10271": 0.00018667938852129183, "根据公式求时间": 0.0001659372342411483, "先求时间，再求到达时间的": 0.0001659372342411483, "补充知识点14322": 0.0001659372342411483, "根据公式求路程": 0.0004978117027234449, "解决路程问题": 0.0004978117027234449, "补充知识点14317": 0.0004978117027234449, "角的初步认识及辨认": 0.00037335877704258366, "认识角": 0.00037335877704258366, "数平面图形中的角": 0.00037335877704258366, "补充知识点15715": 0.00037335877704258366, "补充知识点15716": 0.00037335877704258366, "角的概念及表示方法": 4.1484308560287074e-05, "角的定义": 4.1484308560287074e-05, "角的概念": 4.1484308560287074e-05, "补充知识点15775": 4.1484308560287074e-05, "补充知识点15776": 4.1484308560287074e-05, "面积的计算(公顷、平方千米和平方米)": 6.222646284043061e-05, "两个不同单位的计算": 6.222646284043061e-05, "补充知识点21825": 6.222646284043061e-05, "边长增加问题": 0.00018667938852129183, "已知长宽变化引起的面积变化，求原面积": 0.00018667938852129183, "补充知识点16253": 0.00018667938852129183, "补充知识点16254": 0.00018667938852129183, "亿以内数的组成": 0.00014519507996100476, "数的组成(亿以内)": 0.00012445292568086122, "数的组成与数级分解": 0.00012445292568086122, "补充知识点754": 0.00012445292568086122, "用计算器探索规律": 6.222646284043061e-05, "补充知识点10604": 6.222646284043061e-05, "补充知识点10605": 6.222646284043061e-05, "整亿数的改写": 0.0001659372342411483, "补充知识点919": 8.296861712057415e-05, "1格表示一个单位的单式条形统计图": 0.000580780319844019, "以一当一的条形统计图": 0.000580780319844019, "探究表示数据的方法": 0.000580780319844019, "补充知识点22334": 0.000580780319844019, "补充知识点22335": 0.000580780319844019, "解决归一归总问题": 0.00018667938852129183, "简单双归一问题": 0.00018667938852129183, "补充知识点25740": 0.00018667938852129183, "补充知识点25741": 0.00018667938852129183, "补充知识点25742": 0.00018667938852129183, "简单求一个数的几分之几是多少": 0.0007467175540851673, "直接计算一个数的几分之一是多少": 0.0007467175540851673, "补充知识点8164": 0.0007467175540851673, "三位数减三位数的验算": 0.00014519507996100476, "三位数减两、三位数(不退位)": 0.00014519507996100476, "补充知识点5095": 0.00014519507996100476, "三位数加减混合运算": 0.0007052332455248803, "补充知识点5126": 0.00035261662276244013, "测量中的间隔问题": 2.0742154280143537e-05, "已知棵树和间隔，求路长": 2.0742154280143537e-05, "补充知识点15134": 2.0742154280143537e-05, "数学趣题": 0.0008296861712057415, "重叠问题": 0.00012445292568086122, "测量中的重叠问题": 0.00012445292568086122, "直线重叠求总长": 0.00012445292568086122, "补充知识点26363": 0.00012445292568086122, "补充知识点26364": 0.00012445292568086122, "补充知识点26365": 0.00012445292568086122, "几百几十数加、减几百几十数竖式谜": 6.222646284043061e-05, "加法竖式谜": 6.222646284043061e-05, "补充知识点24400": 6.222646284043061e-05, "补充知识点24401": 6.222646284043061e-05, "补充知识点24402": 6.222646284043061e-05, "万以内数的估计": 4.1484308560287074e-05, "求一个数接近哪个整百数或几百几十数": 4.1484308560287074e-05, "估成几百几十数": 4.1484308560287074e-05, "补充知识点701": 4.1484308560287074e-05, "两位数减两位数的口算的应用题": 0.000269648005641866, "补充知识点4989": 0.000269648005641866, "时间单位的选择": 0.0005185538570035884, "选择合适的时间单位(时、分、秒)": 0.00024890585136172244, "补充知识点13336": 0.00024890585136172244, "补充知识点13337": 0.00024890585136172244, "两、三位数与一位数的不进位乘法": 0.00041484308560287074, "解决三位数乘一位数(不进位)的实际问题": 0.0001659372342411483, "三位数乘一位数应用中的算理": 0.0001659372342411483, "补充知识点6056": 0.0001659372342411483, "时钟问题": 0.0003318744684822966, "根据时间规律解决问题": 2.0742154280143537e-05, "钟表整时的规律解决问题": 2.0742154280143537e-05, "补充知识点25976": 2.0742154280143537e-05, "补充知识点25977": 2.0742154280143537e-05, "补充知识点25978": 2.0742154280143537e-05, "分段计时解决问题": 8.296861712057415e-05, "补充知识点13149": 4.1484308560287074e-05, "因数中间或末尾是0的乘法": 0.00018667938852129183, "三位数中间有0(进位)的乘法笔算": 0.00012445292568086122, "三位数中间有0(进位)的算理考察": 0.00012445292568086122, "补充知识点6176": 0.00012445292568086122, "三位数乘一位数的估算": 0.0007674597083653108, "运用估算判断积的可能性": 0.00014519507996100476, "一位数不知的情况估算结果": 0.00014519507996100476, "补充知识点7061": 0.00014519507996100476, "有关时间推算的爬楼梯问题": 2.0742154280143537e-05, "已知楼层求台阶层数": 2.0742154280143537e-05, "补充知识点15151": 2.0742154280143537e-05, "三位数乘一位数(不连续进位)的笔算": 0.00018667938852129183, "三位数乘一位数(不连续进位)的算理": 0.00018667938852129183, "补充知识点6081": 0.00018667938852129183, "两位数乘一位数(不连续进位)的笔算": 0.00012445292568086122, "两位数乘一位数(不连续进位)的计算": 0.00012445292568086122, "补充知识点6077": 0.00012445292568086122, "用9的乘法口诀求商（移后删）": 2.0742154280143537e-05, "9的乘法口诀简单应用": 2.0742154280143537e-05, "补充知识点5683": 2.0742154280143537e-05, "已知一个数的几倍是多少，求这个数": 0.00037335877704258366, "补充知识点10033": 0.00012445292568086122, "补充知识点10034": 0.00012445292568086122, "求一个量增加/减少多少能成为另一个量的几倍": 0.0002903901599220095, "有关增加或减少的倍数问题": 0.0002903901599220095, "补充知识点10021": 0.0002903901599220095, "补充知识点10022": 0.0002903901599220095, "用三角尺画角": 0.0006430067826844496, "画线添角": 0.00012445292568086122, "判断画线增加的角的类型": 0.00012445292568086122, "补充知识点15885": 0.00012445292568086122, "补充知识点15886": 0.00012445292568086122, "直角、钝角、锐角的认识及特征": 0.0007259753998050237, "认识直角": 0.00018667938852129183, "直角的辨析": 0.00018667938852129183, "补充知识点15737": 0.00018667938852129183, "补充知识点15738": 0.00018667938852129183, "与直角有关的应用": 6.222646284043061e-05, "钟面上的直角": 6.222646284043061e-05, "补充知识点15763": 6.222646284043061e-05, "补充知识点15764": 6.222646284043061e-05, "认识锐角、钝角": 0.000580780319844019, "补充知识点15727": 0.0002903901599220095, "补充知识点15728": 0.0002903901599220095, "角的大小比较": 0.000269648005641866, "角的大小比较(初步认识角)": 8.296861712057415e-05, "补充知识点15827": 8.296861712057415e-05, "补充知识点15828": 8.296861712057415e-05, "数角(角的初步认识)": 0.00012445292568086122, "数射线中的角": 0.0002281636970815789, "补充知识点25093": 0.00012445292568086122, "补充知识点25094": 0.00012445292568086122, "补充知识点25095": 0.00012445292568086122, "线段的初步认识": 0.00020742154280143537, "认识线段": 0.00020742154280143537, "线段的认识": 0.00020742154280143537, "补充知识点15505": 0.00020742154280143537, "补充知识点15506": 0.00020742154280143537, "三角尺拼角": 0.00024890585136172244, "用不同的三角板拼": 0.00024890585136172244, "补充知识点15911": 0.00024890585136172244, "补充知识点15912": 0.00024890585136172244, "画角": 0.00012445292568086122, "补充知识点15901": 6.222646284043061e-05, "补充知识点15902": 6.222646284043061e-05, "角的初步认识的应用(直角、锐角、钝角)": 8.296861712057415e-05, "遮住角的一部分，猜角": 8.296861712057415e-05, "补充知识点15769": 8.296861712057415e-05, "补充知识点15770": 8.296861712057415e-05, "加法应用题(100以内)": 0.0003941009313227272, "求原来": 0.0007052332455248803, "补充知识点4652": 0.0003941009313227272, "两位数与两位数的退位减法": 0.0012860135653688992, "笔算两位数减两位数(退位减)": 0.00031113231420215305, "列竖式计算两位数减两位(退位减)": 0.00031113231420215305, "补充知识点4705": 0.00031113231420215305, "连减应用题": 0.00014519507996100476, "连减求还剩": 0.00014519507996100476, "补充知识点4802": 0.00014519507996100476, "连加应用题": 0.00010371077140071768, "已知三部分量，求总数": 0.00010371077140071768, "补充知识点4779": 0.00010371077140071768, "比多少应用题(一步)(100以内)": 0.00014519507996100476, "根据多多少/少多少求数的应用题": 0.00014519507996100476, "补充知识点4697": 0.00014519507996100476, "减法应用题(100以内)": 0.00010371077140071768, "减法应用题": 0.00010371077140071768, "补充知识点4712": 0.00010371077140071768, "笔算两位数加两位数(进位加)解决实际问题": 0.0003941009313227272, "购物问题（从多种物品中选2种）": 0.0003941009313227272, "补充知识点4679": 0.0003941009313227272, "加法竖式谜（100以内）": 0.0003318744684822966, "两位数加两位数竖式谜": 0.0003318744684822966, "补充知识点24526": 0.0003318744684822966, "补充知识点24527": 0.0003318744684822966, "补充知识点24528": 0.0003318744684822966, "长度的测量方法": 0.0005185538570035884, "测量方式": 0.00012445292568086122, "选择合适的身体尺测量": 0.00014519507996100476, "补充知识点21646": 0.00012445292568086122, "补充知识点21647": 0.00012445292568086122, "厘米的认识": 0.00014519507996100476, "认识厘米": 0.0002903901599220095, "补充知识点21686": 0.00014519507996100476, "米、厘米的应用": 0.00014519507996100476, "厘米的计算应用": 0.00014519507996100476, "补充知识点21701": 0.00014519507996100476, "残余刻度量长度（厘米）": 6.222646284043061e-05, "量物品": 6.222646284043061e-05, "补充知识点21612": 6.222646284043061e-05, "补充知识点21613": 6.222646284043061e-05, "比大小(米和厘米)": 6.222646284043061e-05, "补充知识点13725": 6.222646284043061e-05, "补充知识点13726": 6.222646284043061e-05, "补充知识点13727": 6.222646284043061e-05, "搭配问题": 0.004086204393188277, "握手问题": 0.00014519507996100476, "3人握手问题": 0.00014519507996100476, "补充知识点14691": 0.00014519507996100476, "补充知识点14692": 0.00014519507996100476, "表内乘法": 0.009811038974507892, "表内乘加、乘减": 0.002323121279376076, "9的乘加、乘减计算": 0.00012445292568086122, "直接写得数": 0.0003318744684822966, "补充知识点5446": 0.00012445292568086122, "9的乘法口诀及应用": 0.0010578498682873203, "9的乘法口诀（移后删）": 0.0004355852398830143, "根据9的乘法口诀计算": 0.0004355852398830143, "补充知识点5349": 0.0004355852398830143, "7的乘法口诀及应用": 0.0009956234054468898, "7的乘法应用题（移后删）": 0.000580780319844019, "7的乘法的意义应用问题": 0.000580780319844019, "补充知识点5298": 0.000580780319844019, "笔算(加减混合)": 0.00014519507996100476, "列竖式计算(加减混合)(带括号)": 0.00014519507996100476, "补充知识点4863": 0.00014519507996100476, "画直角": 0.00012445292568086122, "补充知识点15895": 6.222646284043061e-05, "补充知识点15896": 6.222646284043061e-05, "角的分类及换算": 0.000269648005641866, "角的分类": 4.1484308560287074e-05, "补充知识点15861": 2.0742154280143537e-05, "补充知识点15862": 2.0742154280143537e-05, "口算(加减混合)": 0.00024890585136172244, "运算顺序(加减混合)": 0.00024890585136172244, "补充知识点4885": 0.00024890585136172244, "两位数与两位数的不退位减法": 0.0003941009313227272, "口算两位数减两位数(不退位)": 6.222646284043061e-05, "看图选算式": 6.222646284043061e-05, "补充知识点4548": 6.222646284043061e-05, "口算两位数减整十数(不退位)": 0.00012445292568086122, "补充知识点4497": 0.00012445292568086122, "连加口算的计算与应用": 0.0002281636970815789, "补充知识点4771": 0.0002281636970815789, "比多少应用题(两步)": 4.1484308560287074e-05, "已知两数关系求多多少/少多少": 4.1484308560287074e-05, "补充知识点4857": 4.1484308560287074e-05, "加减混合应用题": 0.0002903901599220095, "求原来/现在": 0.0002903901599220095, "补充知识点4871": 0.0002903901599220095, "笔算(连加)": 2.0742154280143537e-05, "列竖式计算（连加）": 2.0742154280143537e-05, "补充知识点4768": 2.0742154280143537e-05, "找规律(100以内)": 6.222646284043061e-05, "数串找规律(连加同一个数)": 6.222646284043061e-05, "补充知识点23250": 6.222646284043061e-05, "补充知识点23251": 6.222646284043061e-05, "补充知识点23252": 6.222646284043061e-05, "100以内数加减法的估算": 0.000269648005641866, "估算(100以内)": 0.0001659372342411483, "估得数选算式": 0.0001659372342411483, "补充知识点7008": 0.0001659372342411483, "7的乘加乘减应用题": 0.00037335877704258366, "根据条件选择正确列式": 0.0010371077140071767, "补充知识点5435": 0.00037335877704258366, "8的乘加乘减应用题": 0.0004770695484433013, "补充知识点5441": 0.0004770695484433013, "两位数减一位数（退位）的应用": 0.00041484308560287074, "找得数相同的算式": 0.00041484308560287074, "补充知识点4622": 0.00041484308560287074, "估算解决问题(100以内)": 0.00010371077140071768, "够不够问题": 0.00020742154280143537, "补充知识点7016": 0.00010371077140071768, "笔算两位数减两位数(不退位减)": 6.222646284043061e-05, "多种方法计算两位数减两位数": 6.222646284043061e-05, "补充知识点4543": 6.222646284043061e-05, "笔算两位数加两位数(不进位加)": 0.00037335877704258366, "列竖式计算两位数+两位数(不进位)": 0.00037335877704258366, "补充知识点4507": 0.00037335877704258366, "口算两位数加两位数(进位加)": 6.222646284043061e-05, "补充知识点4661": 6.222646284043061e-05, "认识米": 2.0742154280143537e-05, "生活中的1米": 2.0742154280143537e-05, "补充知识点21696": 2.0742154280143537e-05, "测量线段（厘米）": 2.0742154280143537e-05, "测量图形中的线段长度": 2.0742154280143537e-05, "补充知识点21620": 2.0742154280143537e-05, "补充知识点21621": 2.0742154280143537e-05, "长度的简单计算问题(米、厘米)-": 2.0742154280143537e-05, "补充知识点21705": 2.0742154280143537e-05, "直尺测量（厘米）": 0.0001659372342411483, "直尺测量(从0刻度开始)": 0.0001659372342411483, "补充知识点21656": 0.0001659372342411483, "补充知识点21657": 0.0001659372342411483, "量一量，比一比": 6.222646284043061e-05, "比一比，估一估": 6.222646284043061e-05, "补充知识点21670": 6.222646284043061e-05, "补充知识点21671": 6.222646284043061e-05, "连加、连减（10以内）": 0.00041484308560287074, "连加计算(10以内)": 0.0001659372342411483, "连加计算": 0.0001659372342411483, "补充知识点3969": 0.0001659372342411483, "立体图形的搭建": 0.0007882018626454544, "相同正方体拼搭": 0.00041484308560287074, "数小正方体的个数": 0.00041484308560287074, "补充知识点20051": 0.00041484308560287074, "补充知识点20052": 0.00041484308560287074, "不进位加法（20以内）": 0.000269648005641866, "十几加几的不进位加法的应用": 0.000269648005641866, "补充知识点4072": 0.000269648005641866, "8加几的进位加法": 0.0004563273941631578, "8、7、6加几": 0.0004563273941631578, "按步骤凑十(圈十)": 0.000539296011283732, "补充知识点4284": 0.0004563273941631578, "9加几的进位加法": 0.00035261662276244013, "9加几": 0.00035261662276244013, "一个加数不变(9加几)": 0.00035261662276244013, "补充知识点4270": 0.00035261662276244013, "加减混合运算（10以内）": 0.0004355852398830143, "加减混合计算(10以内)": 0.00018667938852129183, "加减混合计算的计算方法": 0.00018667938852129183, "补充知识点4004": 0.00018667938852129183, "不退位减法（20以内）": 0.0004355852398830143, "十几减几的不退位减法": 0.0003941009313227272, "十几减几不退位的计算方法": 0.0003941009313227272, "补充知识点4087": 0.0003941009313227272, "页码问题": 0.0002281636970815789, "求页数（20以内）": 0.0001659372342411483, "求读了几页": 0.0001659372342411483, "补充知识点25961": 0.0001659372342411483, "补充知识点25962": 0.0001659372342411483, "补充知识点25963": 0.0001659372342411483, "立体图形的认识及分类": 0.0004770695484433013, "初步认识立体图形": 0.00041484308560287074, "认识长方体、正方体、圆柱和球": 0.00041484308560287074, "补充知识点18179": 0.00041484308560287074, "补充知识点18180": 0.00041484308560287074, "补充知识点18181": 0.00041484308560287074, "位置": 0.00018667938852129183, "左、右位置": 0.00012445292568086122, "判断物体所在的位置(左右)": 0.00010371077140071768, "一幅图中人的左右": 0.00010371077140071768, "补充知识点21070": 0.00010371077140071768, "补充知识点21071": 0.00010371077140071768, "连减的应用（10以内）": 0.0002281636970815789, "看图列式(连加)": 0.0002281636970815789, "补充知识点3980": 0.0002281636970815789, "不进位、不退位加减混合运算（20以内）": 0.00020742154280143537, "与10相关的计算": 6.222646284043061e-05, "看图列算式": 6.222646284043061e-05, "补充知识点4101": 6.222646284043061e-05, "20以内数的认识": 0.0027379643649789467, "20以内数的排序": 0.00031113231420215305, "区分基数与序数(11-20)": 4.1484308560287074e-05, "不同方向确定第几": 4.1484308560287074e-05, "补充知识点386": 4.1484308560287074e-05, "不进位不退位的加减法(11~19)": 0.00014519507996100476, "补充知识点4104": 0.00014519507996100476, "11-20各数的顺序": 0.000269648005641866, "找相邻数、中间数": 0.000269648005641866, "补充知识点378": 0.000269648005641866, "20以内数的比大小": 0.00037335877704258366, "按大小顺序排序": 0.00018667938852129183, "补充知识点372": 0.00018667938852129183, "20以内数的组成": 0.00041484308560287074, "数的组成（20以内）": 0.00037335877704258366, "根据数位上是几写数": 0.00037335877704258366, "补充知识点358": 0.00037335877704258366, "11~20的读、写法": 0.00018667938852129183, "计数器认数(20以内)": 4.1484308560287074e-05, "根据计数器写数": 4.1484308560287074e-05, "补充知识点328": 4.1484308560287074e-05, "多种立体图形的拼搭": 0.0001659372342411483, "稳、高": 0.0001659372342411483, "补充知识点20043": 0.0001659372342411483, "补充知识点20044": 0.0001659372342411483, "立体图形的稳定性（单个图形）": 4.1484308560287074e-05, "立体图形稳定性的比较": 4.1484308560287074e-05, "补充知识点18197": 4.1484308560287074e-05, "补充知识点18198": 4.1484308560287074e-05, "补充知识点18199": 4.1484308560287074e-05, "推理几天前或几天后是星期几": 0.00018667938852129183, "推理几天后是星期几": 0.00018667938852129183, "补充知识点13292": 0.00018667938852129183, "数阵问题": 0.00037335877704258366, "封闭型数阵图(20以内)": 8.296861712057415e-05, "封闭型数阵图(已知一条边上两数)": 8.296861712057415e-05, "补充知识点24880": 8.296861712057415e-05, "补充知识点24881": 8.296861712057415e-05, "补充知识点24882": 8.296861712057415e-05, "算式比较大小": 0.0004563273941631578, "算式比大小(20以内进位加)": 8.296861712057415e-05, "根据算式填合适的数": 0.00014519507996100476, "补充知识点24352": 8.296861712057415e-05, "补充知识点24353": 8.296861712057415e-05, "补充知识点24354": 8.296861712057415e-05, "10以内数的比大小": 0.0006222646284043061, "看图比大小": 0.0001659372342411483, "补充知识点290": 0.0001659372342411483, "11-20各数的读写": 0.00014519507996100476, "看图读数、写数": 0.00014519507996100476, "补充知识点319": 0.00014519507996100476, "9以内数的比大小": 0.00014519507996100476, "6-9的比大小": 0.00014519507996100476, "先写数，再比大小": 0.00014519507996100476, "补充知识点243": 0.00014519507996100476, "6~9的认识和读写": 0.00024890585136172244, "6-9的认识": 0.00024890585136172244, "看图数一数": 0.00024890585136172244, "补充知识点389": 0.00024890585136172244, "数一数": 0.00010371077140071768, "数与量的对应(1-5)": 8.296861712057415e-05, "看图写数(1-5)": 8.296861712057415e-05, "补充知识点44": 8.296861712057415e-05, "补充知识点45": 8.296861712057415e-05, "比一比": 0.00012445292568086122, "比较物体多少": 8.296861712057415e-05, "感受数量的比较": 6.222646284043061e-05, "多者比较多少": 6.222646284043061e-05, "补充知识点91": 6.222646284043061e-05, "感知数与量": 2.0742154280143537e-05, "自我介绍中的数量": 2.0742154280143537e-05, "补充知识点20": 2.0742154280143537e-05, "补充知识点21": 2.0742154280143537e-05, "8、9的加减法": 0.0006015224741241625, "8、9的加减法的应用": 0.00041484308560287074, "多和少的问题": 0.00041484308560287074, "补充知识点3847": 0.00041484308560287074, "8、9的分与合": 0.00041484308560287074, "8、9的分合的应用": 0.00041484308560287074, "看图分与合": 0.00041484308560287074, "补充知识点3807": 0.00041484308560287074, "6、7的加减法": 0.0006430067826844496, "6 、7的加减法的应用": 0.0004978117027234449, "看图列加、减法算式": 0.0004978117027234449, "补充知识点3790": 0.0004978117027234449, "6、7的减法的实际应用": 0.00014519507996100476, "减法计算的应用": 0.00014519507996100476, "补充知识点3774": 0.00014519507996100476, "图形的变化规律": 0.000539296011283732, "找规律(图形)": 8.296861712057415e-05, "接着摆（相同重复出现）": 8.296861712057415e-05, "补充知识点23387": 8.296861712057415e-05, "补充知识点23388": 8.296861712057415e-05, "补充知识点23389": 8.296861712057415e-05, "5以内数的排序": 0.0002281636970815789, "区分第几\"和\"有几\"\"": 0.0001659372342411483, "一列队列区分第几和有几": 0.0001659372342411483, "补充知识点218": 0.0001659372342411483, "5以内数的加法": 0.0004770695484433013, "加法应用(1-5)": 0.00024890585136172244, "补充知识点3678": 0.00024890585136172244, "不等式谜(1-10)": 4.1484308560287074e-05, "选出不能填的数": 4.1484308560287074e-05, "补充知识点277": 4.1484308560287074e-05, "10以内数的排序": 0.000269648005641866, "9以内的基数与序数": 0.000269648005641866, "按顺序判断第几": 0.000269648005641866, "补充知识点305": 0.000269648005641866, "图文算式(10以内)": 0.00010371077140071768, "多步代入的图文算式": 0.00010371077140071768, "补充知识点13879": 0.00010371077140071768, "补充知识点13880": 0.00010371077140071768, "减法表（10以内）": 2.0742154280143537e-05, "10以内减法表": 2.0742154280143537e-05, "减法表数的规律": 2.0742154280143537e-05, "补充知识点4062": 2.0742154280143537e-05, "5以内数的减法": 0.0002281636970815789, "1-5减法的含义": 6.222646284043061e-05, "怎样做可以使一样多": 6.222646284043061e-05, "补充知识点3699": 6.222646284043061e-05, "加减混合运用(10以内加减混合)": 0.00024890585136172244, "理解图意并填空": 0.00024890585136172244, "补充知识点4017": 0.00024890585136172244, "10的加减法": 0.0004978117027234449, "10的加减法的应用": 0.0002903901599220095, "看图列式计算(10的加减法)": 0.0002903901599220095, "补充知识点3906": 0.0002903901599220095, "10的计算": 0.00020742154280143537, "找结果相同的算式": 0.00020742154280143537, "补充知识点3898": 0.00020742154280143537, "8、9的加减法的计算": 0.00018667938852129183, "减法算式表示的含义9": 0.00018667938852129183, "补充知识点3834": 0.00018667938852129183, "10的分与合": 0.0003318744684822966, "10的分与合应用": 0.0001659372342411483, "补充知识点3890": 0.0001659372342411483, "10以内数的顺序": 0.00024890585136172244, "数物对应": 0.00024890585136172244, "补充知识点262": 0.00024890585136172244, "加法计算(1-5)": 0.00018667938852129183, "根据算式填数": 0.00024890585136172244, "补充知识点3666": 0.00018667938852129183, "前、后位置": 6.222646284043061e-05, "判断物体所在的位置(前后)": 6.222646284043061e-05, "判断前面/后面是谁": 6.222646284043061e-05, "补充知识点21004": 6.222646284043061e-05, "补充知识点21005": 6.222646284043061e-05, "生活中的左右": 2.0742154280143537e-05, "由图示判断左右": 2.0742154280143537e-05, "补充知识点21082": 2.0742154280143537e-05, "补充知识点21083": 2.0742154280143537e-05, "5以内数的比大小": 0.00018667938852129183, "比较大小(1-5)": 0.00018667938852129183, "按要求画图": 0.00018667938852129183, "补充知识点158": 0.00018667938852129183, "1~5的读、写法": 4.1484308560287074e-05, "认读写数(1-5)": 4.1484308560287074e-05, "根据物对应数": 4.1484308560287074e-05, "补充知识点151": 4.1484308560287074e-05, "一一对应感受多少": 2.0742154280143537e-05, "根据一一对应判断数量的多少": 2.0742154280143537e-05, "补充知识点97": 2.0742154280143537e-05, "间隔发车问题": 0.000269648005641866, "解决发车问题": 0.000269648005641866, "等待时间": 0.000269648005641866, "补充知识点14356": 0.000269648005641866, "年、月、日换算": 0.0008296861712057415, "年、月、日单位换算的简单应用": 0.0008296861712057415, "补充知识点13079": 0.0008296861712057415, "补充知识点13080": 0.0008296861712057415, "小数的组数和猜数问题": 0.001659372342411483, "按要求写小数与组数问题": 0.0003318744684822966, "用所给数字组小数(含0)": 0.0003318744684822966, "补充知识点2140": 0.0003318744684822966, "补充知识点2141": 0.0003318744684822966, "提出问题并解答（一位小数的加减运算）": 0.00018667938852129183, "补充知识点13865": 0.00018667938852129183, "补充知识点13866": 0.00018667938852129183, "与星期有关的换算": 0.00020742154280143537, "天数与星期的换算": 0.00020742154280143537, "补充知识点13181": 0.00020742154280143537, "日历中的规律": 0.0004355852398830143, "相邻7个日期的规律": 0.0004355852398830143, "补充知识点13172": 0.0004355852398830143, "搭配路线": 0.000539296011283732, "搭配路线（3段路 ）": 0.000539296011283732, "补充知识点14737": 0.000539296011283732, "补充知识点14738": 0.000539296011283732, "巧求长、正方形修路问题": 0.0005185538570035884, "四周铺路问题": 0.0005185538570035884, "补充知识点17734": 0.0005185538570035884, "补充知识点17735": 0.0005185538570035884, "补充知识点17736": 0.0005185538570035884, "长（正）方形的剪拼（涉及单位换算）": 0.00020742154280143537, "剪拼后图形的面积": 0.00020742154280143537, "补充知识点21839": 0.00020742154280143537, "运用正方形面积公式解决实际问题": 0.00037335877704258366, "由公式求正方形面积的简单应用": 0.00037335877704258366, "补充知识点16355": 0.00037335877704258366, "补充知识点16356": 0.00037335877704258366, "运用推理法解决有关两位数乘两位数的填数问题": 0.0006637489369645932, "积某一位上的数字": 0.0006637489369645932, "补充知识点7080": 0.0006637489369645932, "两位数乘两位数的笔算(有进位)": 0.002468316359337081, "补充知识点6568": 0.002468316359337081, "购票问题": 0.0018460517309327746, "买票问题（两位数乘两位数）": 0.00035261662276244013, "门票总费用（涉及团体票）": 0.00035261662276244013, "补充知识点15020": 0.00035261662276244013, "两位数除以一位数(被除数首位不能被整除)解决实际问题": 0.000850428325485885, "由要解决的问题补充信息": 0.000850428325485885, "补充知识点6302": 0.000850428325485885, "买票问题(多位数乘一位数)": 0.0001659372342411483, "怎样买票最合算": 0.0001659372342411483, "补充知识点15018": 0.0001659372342411483, "隐藏的和倍问题（三位数除以一位数）": 0.00041484308560287074, "实际问题中的和倍问题": 0.00041484308560287074, "补充知识点25788": 0.00041484308560287074, "补充知识点25789": 0.00041484308560287074, "补充知识点25790": 0.00041484308560287074, "除法竖式谜": 0.0003941009313227272, "被除数是两位数的除法竖式谜": 0.0003941009313227272, "补充知识点24391": 0.0003941009313227272, "补充知识点24392": 0.0003941009313227272, "补充知识点24393": 0.0003941009313227272, "分数的基本性质": 0.0021779261994150714, "分数基本性质": 0.0021779261994150714, "涂色表示分数基本性质": 0.0021779261994150714, "补充知识点2943": 0.0021779261994150714, "补充知识点2944": 0.0021779261994150714, "正方体的展开图": 0.0021779261994150714, "判断展开图能否还原为正方体": 0.0009541390968866026, "补充知识点18459": 0.0009541390968866026, "补充知识点18460": 0.0009541390968866026, "分数的加、减法混合运算": 0.002820932982099521, "同分母分数连加计算": 0.0004563273941631578, "补充知识点7979": 0.0004563273941631578, "分数加减混合运算": 0.0013689821824894733, "异分母分数加减混合运算的直接计算": 0.0013689821824894733, "补充知识点7992": 0.0013689821824894733, "同分母分数加法的含义及计算方法": 0.001161560639688038, "看图列式再计算": 0.001161560639688038, "补充知识点7849": 0.001161560639688038, "分数化成小数": 0.001514177262450478, "分数化为有限小数": 0.001514177262450478, "补充知识点3185": 0.001514177262450478, "补充知识点3186": 0.001514177262450478, "一位或多位小数化分数": 0.0006430067826844496, "一位或多位小数化分数（约分）": 0.0006430067826844496, "小数化成分数": 0.0006430067826844496, "纯循环小数化成分数": 0.0006430067826844496, "补充知识点2419": 0.0006430067826844496, "求两个数的最小公倍数的方法": 0.0004770695484433013, "用集合的方法找出最小公倍数": 0.0004770695484433013, "补充知识点1518": 0.0004770695484433013, "分数大小的比较": 0.008587251871979423, "异分母异分子分数比大小": 0.008027213706415548, "分数的大小比较": 0.003318744684822966, "异分母分数比大小": 0.003318744684822966, "补充知识点2619": 0.003318744684822966, "用公倍数、最小公倍数解决简单的实际问题": 0.0023438634336562197, "余数相同的最小公倍数实际问题": 0.0023438634336562197, "补充知识点1585": 0.0023438634336562197, "公因数与最大公因数": 0.007301238306610525, "公因数和最大公因数的意义": 0.0011200763311277509, "理解公因数的含义": 0.0011200763311277509, "补充知识点1452": 0.0011200763311277509, "找一个数的因数及因数的特征": 0.0013482400282093297, "找出一个数的因数": 0.0013482400282093297, "一个数的因数与最大最小": 0.0013482400282093297, "补充知识点1023": 0.0013482400282093297, "假分数与带分数或整数的互化": 0.0008089440169255979, "假分数、整数、带分数互化": 0.0004563273941631578, "利用分数与除法的关系把假分数化成整数或带分数": 0.0004563273941631578, "补充知识点2927": 0.0004563273941631578, "补充知识点2928": 0.0004563273941631578, "带分数与假分数互化": 0.00035261662276244013, "根据图写出带分数或假分数": 0.00035261662276244013, "补充知识点2913": 0.00035261662276244013, "补充知识点2914": 0.00035261662276244013, "运算性质（奇数和偶数）": 0.003920267158947128, "奇偶性在实际问题中的应用": 0.0007259753998050237, "平面图形周长和面积中的应用-": 0.0007259753998050237, "补充知识点1339": 0.0007259753998050237, "根据倍数的特征解决问题": 0.00024890585136172244, "运用倍数解决实际问题": 0.0002281636970815789, "运用倍数解决实际问题(至多至少)": 0.0002281636970815789, "补充知识点1060": 0.0002281636970815789, "体积（容积）大小的比较": 0.000269648005641866, "含体积单位的比大小": 0.000269648005641866, "两个带单位的比大小(单名数)": 0.000269648005641866, "补充知识点21915": 0.000269648005641866, "补充知识点21916": 0.000269648005641866, "9的倍数特征": 0.0006222646284043061, "根据9的倍数特征给□中填数-": 0.00031113231420215305, "补充知识点1238": 0.00031113231420215305, "小数加、减法解决较复杂购物问题": 0.0004770695484433013, "优惠后买两种物品便宜了多少钱": 0.0004770695484433013, "补充知识点7315": 0.0004770695484433013, "小数加、减法解决较复杂的实际问题（一位小数）": 0.0006844910912447367, "移多补少变相等求原来": 0.0006844910912447367, "补充知识点7289": 0.0006844910912447367, "同分母分数减法的计算、运用-": 0.00037335877704258366, "补充知识点7871": 0.00037335877704258366, "同分母分数加法的计算、运用-": 0.0010993341768476073, "补充知识点7864": 0.0010993341768476073, "用平移解决面积问题": 0.0013482400282093297, "用平移解决不规则图形面积问题": 0.0013482400282093297, "补充知识点17752": 0.0013482400282093297, "补充知识点17753": 0.0013482400282093297, "补充知识点17754": 0.0013482400282093297, "平移与平移现象": 0.0034639397647839706, "认识平移": 0.0005600381655638754, "平移的特点": 0.0005600381655638754, "补充知识点20506": 0.0005600381655638754, "补充知识点20507": 0.0005600381655638754, "补充知识点20508": 0.0005600381655638754, "确定平移的方向和距离": 0.0012237871025284686, "确定平移的方向和距离(实物)": 0.0012237871025284686, "补充知识点20488": 0.0012237871025284686, "补充知识点20489": 0.0012237871025284686, "补充知识点20490": 0.0012237871025284686, "补全轴对称图形": 0.0009748812511667462, "根据对称轴补全轴对称图形": 0.0009748812511667462, "补全轴对称图形的方法": 0.0009748812511667462, "补充知识点20725": 0.0009748812511667462, "补充知识点20726": 0.0009748812511667462, "补充知识点20727": 0.0009748812511667462, "图形的折叠问题": 0.0011200763311277509, "轴对称中对折问题": 0.0003318744684822966, "根据展开图找对折图": 0.0003318744684822966, "补充知识点20016": 0.0003318744684822966, "补充知识点20017": 0.0003318744684822966, "补充知识点20018": 0.0003318744684822966, "镜面对称": 0.00024890585136172244, "镜子问题": 0.0001659372342411483, "图形镜子问题": 0.0001659372342411483, "补充知识点20395": 0.0001659372342411483, "补充知识点20396": 0.0001659372342411483, "补充知识点20397": 0.0001659372342411483, "轴对称图形的性质及应用": 0.0003941009313227272, "感知对称点到对称轴的距离": 0.0003941009313227272, "补充知识点20380": 0.0003941009313227272, "补充知识点20381": 0.0003941009313227272, "补充知识点20382": 0.0003941009313227272, "除数不变，商随被除数变化的规律": 0.00035261662276244013, "补充知识点10314": 0.00035261662276244013, "补充知识点10315": 0.00035261662276244013, "补充知识点10316": 0.00035261662276244013, "除数是整数的基本算理及算法9": 4.1484308560287074e-05, "比较整数除法和小数除法，发现规律": 4.1484308560287074e-05, "补充知识点7498": 4.1484308560287074e-05, "列方程解决工程问题": 0.0001659372342411483, "列方程解含1个未知数的工程问题-": 0.0001659372342411483, "两人合修的工程问题（简单）-": 0.0001659372342411483, "补充知识点11668": 0.0001659372342411483, "补充知识点11669": 0.0001659372342411483, "补充知识点11670": 0.0001659372342411483, "利息、纳税": 2.0742154280143537e-05, "解决复杂应用题": 2.0742154280143537e-05, "补充知识点26145": 2.0742154280143537e-05, "补充知识点26146": 2.0742154280143537e-05, "补充知识点26147": 2.0742154280143537e-05, "补充知识点26148": 2.0742154280143537e-05, "已知分量差和分量比，求分量或总量": 0.0003318744684822966, "已知两个分量的差和比，求分量": 0.0003318744684822966, "补充知识点11939": 0.0003318744684822966, "补充知识点11940": 0.0003318744684822966, "列方程解年龄问题": 0.00010371077140071768, "列方程解决简单的年龄问题\"\"": 0.00010371077140071768, "几年前存在倍数关系": 0.00010371077140071768, "补充知识点11465": 0.00010371077140071768, "补充知识点11466": 0.00010371077140071768, "补充知识点11467": 0.00010371077140071768, "求组合图形中阴影部分的面积": 0.00018667938852129183, "运用差不变求组合图形的面积": 0.00018667938852129183, "利用差不变求组合图形各部分间的面积关系": 0.00018667938852129183, "补充知识点17812": 0.00018667938852129183, "补充知识点17813": 0.00018667938852129183, "补充知识点17814": 0.00018667938852129183, "看图找关系（实际问题与图线）": 0.0016178880338511958, "运行图中的行程问题（单式折线图）": 0.0012652714110887556, "从折线统计图中获取简单信息": 0.0012652714110887556, "补充知识点22866": 0.0012652714110887556, "补充知识点22867": 0.0012652714110887556, "补充知识点22868": 0.0012652714110887556, "1减几分之几的计算": 0.00024890585136172244, "1减几分之几的算理": 0.00024890585136172244, "补充知识点7840": 0.00024890585136172244, "整数加法运算定律推广到分数": 0.0023438634336562197, "填合适的运算符号": 0.0023438634336562197, "补充知识点9783": 0.0023438634336562197, "补充知识点9784": 0.0023438634336562197, "正负数的应用": 0.002717222210698803, "质量中的负数": 0.0007052332455248803, "根据一件物品上的净含量，求最多/最少的质量9": 0.0007052332455248803, "补充知识点3533": 0.0007052332455248803, "补充知识点3534": 0.0007052332455248803, "有括号的运算顺序": 0.0034224554562236834, "含有中括号的四则混合运算的运算顺序": 0.0004770695484433013, "根据运算顺序选算式": 0.0014934351081703347, "补充知识点9319": 0.0004770695484433013, "补充知识点9320": 0.0004770695484433013, "四则运算中的错中求解": 0.0003318744684822966, "漏掉括号运算的错中求解": 0.0003318744684822966, "补充知识点23643": 0.0003318744684822966, "补充知识点23644": 0.0003318744684822966, "补充知识点23645": 0.0003318744684822966, "运用加、减法各部分间的关系巧解算式": 0.0005600381655638754, "已知被减数、减数与差的和，求被减数": 0.0005600381655638754, "补充知识点9079": 0.0005600381655638754, "补充知识点9080": 0.0005600381655638754, "24点": 0.00035261662276244013, "24点游戏": 0.00035261662276244013, "从给出的数字中选出4个数字列出结果是24的算式": 0.00035261662276244013, "补充知识点6930": 0.00035261662276244013, "最省钱问题": 0.0010163655597270331, "运费最省钱问题": 0.0003941009313227272, "补充知识点14957": 0.0003941009313227272, "整数的四则混合运算": 0.0018045674223724877, "新定义运算": 0.0006430067826844496, "补充知识点7003": 0.0006430067826844496, "带有中括号的混合运算": 0.0011408184854078945, "有括号的四则混合运算": 0.0009541390968866026, "补充知识点6912": 0.0009541390968866026, "含有小括号的四则混合运算的运算顺序": 0.0006637489369645932, "补充知识点9389": 0.0006637489369645932, "补充知识点9390": 0.0006637489369645932, "运用乘、除法各部分间的关系巧解算式": 0.00012445292568086122, "已知被除数、除数、商、余数的和及商和余数，求被除数": 0.00012445292568086122, "补充知识点9093": 0.00012445292568086122, "补充知识点9094": 0.00012445292568086122, "错中求解(乘除法各部分间的关系)": 0.00035261662276244013, "被除数看错(有余数除法)": 0.00035261662276244013, "补充知识点23700": 0.00035261662276244013, "补充知识点23701": 0.00035261662276244013, "补充知识点23702": 0.00035261662276244013, "除法各部分之间的关系(有余数)": 0.0006637489369645932, "已知被除数和除数，求商和余数": 0.0006637489369645932, "补充知识点9179": 0.0006637489369645932, "补充知识点9180": 0.0006637489369645932, "简单小数的加法和减法错中求解": 0.0007467175540851673, "错中求解(将被减数、减数看错的小数加减法)": 0.0007467175540851673, "补充知识点23739": 0.0007467175540851673, "补充知识点23740": 0.0007467175540851673, "补充知识点23741": 0.0007467175540851673, "小数加法数字谜": 0.0008711704797660285, "不含进位的小数加法数字谜": 0.0008711704797660285, "补充知识点24748": 0.0008711704797660285, "补充知识点24749": 0.0008711704797660285, "补充知识点24750": 0.0008711704797660285, "小数的加、减法混合运算": 0.0015556615710107652, "小数的加减混合运算": 0.0015556615710107652, "小数连减计算": 0.0015556615710107652, "补充知识点7264": 0.0015556615710107652, "应用等式的性质2解方程": 0.00037335877704258366, "解方程：ax=b这种类型(a≠0)": 0.00037335877704258366, "解形如ax=b\"这种类型方程的过程及判断(a≠0)\"": 0.00037335877704258366, "补充知识点11091": 0.00037335877704258366, "补充知识点11092": 0.00037335877704258366, "应用等式的性质1解方程": 0.0004563273941631578, "解方程：x±a=b这种类型": 0.00035261662276244013, "解方程与方程的解": 0.00035261662276244013, "补充知识点11047": 0.00035261662276244013, "补充知识点11048": 0.00035261662276244013, "小数连加解决实际问题（一位小数）": 0.000580780319844019, "求几个量的和": 0.000580780319844019, "补充知识点7322": 0.000580780319844019, "复杂水中浸物（求溢出水的体积）": 0.0010371077140071767, "估测单个物体体积（多个相同物体，有溢水）-": 0.0010371077140071767, "补充知识点19556": 0.0010371077140071767, "补充知识点19557": 0.0010371077140071767, "分数的加、减法混合运算的应用": 0.004584016095911721, "分数加、减混合运算解决问题": 0.0019912468108937795, "分数加、减混合运算解决物长度、重量、时长...问题": 0.0019912468108937795, "补充知识点8033": 0.0019912468108937795, "分数的意义": 0.008773931260500715, "根据涂色写分数": 0.0035883926904648317, "补充知识点2501": 0.0035883926904648317, "同分母分数加、减法的应用": 0.002281636970815789, "同分母分数加法在实际生活的应用": 0.0008296861712057415, "同分母分数加减法的混合应用": 0.0008296861712057415, "补充知识点7880": 0.0008296861712057415, "解决问题(一半的一半应用题)": 0.002592769285017942, "两种液体混合后一半的一半问题": 0.002592769285017942, "补充知识点8024": 0.002592769285017942, "同分母分数减法的含义及计算方法": 0.0008089440169255979, "理解同分母分数减法的含义和算理": 0.0008089440169255979, "补充知识点7855": 0.0008089440169255979, "真分数的意义和特征": 0.0002281636970815789, "真分数特征": 0.0002281636970815789, "补充知识点2705": 0.0002281636970815789, "补充知识点2706": 0.0002281636970815789, "异分母分数连加在实际生活中的应用-": 0.000850428325485885, "异分母分数连加的实际应用（结果需比较）-": 0.000850428325485885, "补充知识点7962": 0.000850428325485885, "搭配综合问题": 4.1484308560287074e-05, "任取一种的问题": 4.1484308560287074e-05, "补充知识点14667": 4.1484308560287074e-05, "补充知识点14668": 4.1484308560287074e-05, "整数乘法分配律": 0.006181161975482774, "乘法分配律": 0.0016801144966916263, "运用实例探究乘法分配律": 0.0016801144966916263, "补充知识点9595": 0.0016801144966916263, "补充知识点9596": 0.0016801144966916263, "整数加法交换律": 0.0014104664910497605, "加法交换律": 0.0014104664910497605, "用不同方式表示加法交换律": 0.0014104664910497605, "补充知识点9397": 0.0014104664910497605, "补充知识点9398": 0.0014104664910497605, "数三角形": 0.0004978117027234449, "数三角形个数": 0.0004978117027234449, "补充知识点25153": 0.0004978117027234449, "补充知识点25154": 0.0004978117027234449, "补充知识点25155": 0.0004978117027234449, "多个小数的大小比较": 0.0018667938852129182, "根据大小关系(和位数)填/选合适的数": 0.0018667938852129182, "补充知识点2324": 0.0018667938852129182, "补充知识点2325": 0.0018667938852129182, "小数的组成": 0.0018875360394930618, "根据数的组成写数(文字)": 0.0018875360394930618, "补充知识点2118": 0.0018875360394930618, "补充知识点2119": 0.0018875360394930618, "用平均数倒推": 0.0006430067826844496, "根据平均数与其它数求剩下一个数的大小": 0.0006430067826844496, "补充知识点25893": 0.0006430067826844496, "补充知识点25894": 0.0006430067826844496, "补充知识点25895": 0.0006430067826844496, "平均数的应用": 6.222646284043061e-05, "选择对平均分影响最大的数据": 6.222646284043061e-05, "补充知识点22552": 6.222646284043061e-05, "补充知识点22553": 6.222646284043061e-05, "根据表中数据解决问题": 0.0018045674223724877, "移多补少": 0.0018045674223724877, "补充知识点22632": 0.0018045674223724877, "补充知识点22633": 0.0018045674223724877, "补充知识点22634": 0.0018045674223724877, "解决两位数乘一位数(连续进位)的实际问题": 0.00018667938852129183, "补充条件": 0.00018667938852129183, "补充知识点6087": 0.00018667938852129183, "格点与面积": 0.0007674597083653108, "方格图中的面积": 0.0007674597083653108, "画与已知图形面积相等的图形": 0.0007674597083653108, "补充知识点25523": 0.0007674597083653108, "补充知识点25524": 0.0007674597083653108, "小数加减法的简算（一位小数）": 2.0742154280143537e-05, "交换位置简算": 2.0742154280143537e-05, "补充知识点9728": 2.0742154280143537e-05, "三位数除以一位数的笔算(被除数首位能整除)": 0.000580780319844019, "竖式计算并验算": 0.000580780319844019, "补充知识点6332": 0.000580780319844019, "三位数除以一位数的笔算(商是两位数)": 0.0006637489369645932, "补充知识点6385": 0.0006637489369645932, "组合思想解决问题": 0.003132065296301674, "打比赛问题": 0.003132065296301674, "补充知识点14571": 0.003132065296301674, "补充知识点14572": 0.003132065296301674, "小数大小比较的应用（一位小数）": 0.00020742154280143537, "找比某数大/小的数": 0.00020742154280143537, "补充知识点2314": 0.00020742154280143537, "补充知识点2315": 0.00020742154280143537, "数线上的小数（一位小数）": 0.0004770695484433013, "用小数表示数轴上的对应点": 0.0004770695484433013, "补充知识点1688": 0.0004770695484433013, "补充知识点1689": 0.0004770695484433013, "排列中的组数问题": 0.002509800667897368, "组没有重复数字的三位数（不含0）": 0.002509800667897368, "补充知识点14509": 0.002509800667897368, "补充知识点14510": 0.002509800667897368, "辨认东、西、南、北四个方向": 0.0016386301881313394, "方向与前后左右": 0.0016386301881313394, "补充知识点21204": 0.0016386301881313394, "补充知识点21205": 0.0016386301881313394, "分数加减混合运算的运算顺序及应用-": 0.000269648005641866, "分数混合运算的运算顺序-": 0.000269648005641866, "补充知识点8013": 0.000269648005641866, "按边分类的应用": 0.00035261662276244013, "三角形与其它图形的转换": 0.00035261662276244013, "补充知识点16835": 0.00035261662276244013, "补充知识点16836": 0.00035261662276244013, "同分母分数连减在实际生活中的应用-": 0.00041484308560287074, "同分母分数连减的简单应用": 0.00041484308560287074, "补充知识点7897": 0.00041484308560287074, "乘法的简单应用（应用加法计算结果）": 4.1484308560287074e-05, "乘法意义的应用题": 4.1484308560287074e-05, "用乘法解决问题": 4.1484308560287074e-05, "补充知识点5179": 4.1484308560287074e-05, "排列思想解决其他类型问题": 0.0026549957478583727, "分东西问题(每人至少分一个）": 0.0026549957478583727, "补充知识点14533": 0.0026549957478583727, "补充知识点14534": 0.0026549957478583727, "认识钟面指针及时间读法": 0.000850428325485885, "一般时间与钟面指针的位置": 0.0007882018626454544, "生活中的时间(几时几分)": 0.00010371077140071768, "判断指针": 0.00010371077140071768, "补充知识点13008": 0.00010371077140071768, "分段计算费用问题": 0.00031113231420215305, "分段计算车费问题": 0.00031113231420215305, "补充知识点7308": 0.00031113231420215305, "数据的搜集与整理": 0.003878782850386841, "用调查法收集数据及认识简单的统计表": 0.0006430067826844496, "根据调查结果整理的统计表给出建议/解决方案/预判": 0.0006430067826844496, "补充知识点22221": 0.0006430067826844496, "补充知识点22222": 0.0006430067826844496, "补充知识点22223": 0.0006430067826844496, "同级运算的计算与应用（乘除混合、连乘、连除）": 0.0006222646284043061, "根据题意列式（不计算结果）": 0.0006222646284043061, "补充知识点6855": 0.0006222646284043061, "用分数和小数表示阴影部分（一位小数）": 0.0007467175540851673, "根据已知小数涂色": 0.0007467175540851673, "补充知识点1696": 0.0007467175540851673, "补充知识点1697": 0.0007467175540851673, "小数加、减法混合运算解决问题（一位小数）": 0.0005185538570035884, "看图解决实际问题": 0.0005185538570035884, "补充知识点7310": 0.0005185538570035884, "一位小数减法的计算(不退位)": 4.1484308560287074e-05, "一位小数减法(不退位)的竖式计算": 4.1484308560287074e-05, "补充知识点7165": 4.1484308560287074e-05, "加减算式中某部分的变化问题": 0.00024890585136172244, "已知被减数和减数的变化，问差的变化": 0.00024890585136172244, "补充知识点9071": 0.00024890585136172244, "补充知识点9072": 0.00024890585136172244, "根据小数的意义进行元角分的单位换算": 0.00024890585136172244, "把元角分形式改写成以元为单位的小数": 0.00024890585136172244, "补充知识点1816": 0.00024890585136172244, "补充知识点1817": 0.00024890585136172244, "小数的读法和写法(两位及以上)": 0.0006015224741241625, "补充知识点1916": 0.0006015224741241625, "补充知识点1917": 0.0006015224741241625, "与两位数乘一位数有关的填数问题": 4.1484308560287074e-05, "两位数乘一位数填一位数": 4.1484308560287074e-05, "补充知识点24772": 4.1484308560287074e-05, "补充知识点24773": 4.1484308560287074e-05, "补充知识点24774": 4.1484308560287074e-05, "相对方向(四个方向)": 0.001514177262450478, "影子与方向": 0.001514177262450478, "补充知识点21212": 0.001514177262450478, "补充知识点21213": 0.001514177262450478, "与小时有关的单位换算": 0.00012445292568086122, "星期与小时的换算": 0.00012445292568086122, "补充知识点13243": 0.00012445292568086122, "数线段、直线和射线": 0.0003318744684822966, "一条线上的数线段、直线、射线": 0.0003318744684822966, "补充知识点25165": 0.0003318744684822966, "补充知识点25166": 0.0003318744684822966, "补充知识点25167": 0.0003318744684822966, "复式统计表的信息提取": 0.0009126547883263156, "最多/少，谁多/少": 0.0009126547883263156, "补充知识点22731": 0.0009126547883263156, "补充知识点22732": 0.0009126547883263156, "补充知识点22733": 0.0009126547883263156, "两位数乘两位数的笔算(不进位)的应用": 0.0007467175540851673, "补充知识点6528": 0.0007467175540851673, "三位数除以一位数，被除数中间有0": 0.0005185538570035884, "商中间有0的一位数除法(被除数中间有0)": 0.0005185538570035884, "补充知识点6408": 0.0005185538570035884, "打电话问题": 0.0019082781937732052, "基础打电话问题": 0.0011408184854078945, "每两人之间给礼物，求需要礼物总数": 0.0011408184854078945, "补充知识点14988": 0.0011408184854078945, "复杂打电话问题": 0.0007674597083653108, "每人n分钟内传递1人，求可以通知的最多人数": 0.0007674597083653108, "补充知识点14981": 0.0007674597083653108, "含字母的分数运算中的推理问题-": 0.0002281636970815789, "求字母和（知异分母分数的和）-": 0.0002281636970815789, "补充知识点7926": 0.0002281636970815789, "分数与除法的关系": 0.006720457986766505, "平均分与分数-": 0.0016386301881313394, "平均分，求一份的量": 0.0016386301881313394, "补充知识点2571": 0.0016386301881313394, "通分的认识及应用": 0.0010993341768476073, "通分的意义和方法": 0.0009126547883263156, "通分步骤和方法": 0.0009126547883263156, "补充知识点3077": 0.0009126547883263156, "补充知识点3078": 0.0009126547883263156, "分数与除法关系的应用-": 0.003484681919064114, "求每份量（分数表示）-": 0.003484681919064114, "补充知识点2585": 0.003484681919064114, "长方体、正方体的容积": 0.0015971458795710522, "求容积": 0.0004978117027234449, "根据三视图求容积（两个方向）-": 0.0004978117027234449, "补充知识点18680": 0.0004978117027234449, "补充知识点18681": 0.0004978117027234449, "长方体表面积的应用": 0.0031942917591421044, "生活中的长方体面积问题": 0.0031942917591421044, "求教室/房间墙面粉刷面积-": 0.0031942917591421044, "补充知识点18514": 0.0031942917591421044, "长方体底面积": 0.0004563273941631578, "看图找底面并计算-": 0.0006430067826844496, "补充知识点18485": 0.0004563273941631578, "组合体的体积": 0.0025512849764576548, "组合体的体积（长方体、正方体）": 0.0019912468108937795, "小正方体堆叠求体积": 0.001472692953890191, "不同的摆法比较体积/表面积（个数已知）-": 0.001472692953890191, "补充知识点19490": 0.001472692953890191, "补充知识点19491": 0.001472692953890191, "组合体的表面积": 0.002634253593578229, "组合体的表面积（长方体、正方体）": 0.0023438634336562197, "小正方体堆叠求面积": 0.0014104664910497605, "不同大小正方体堆叠，求表面积": 0.0014104664910497605, "补充知识点19424": 0.0014104664910497605, "补充知识点19425": 0.0014104664910497605, "正方体的特征": 0.001659372342411483, "正方体的认识": 0.001659372342411483, "摆大正方体需要小正方体的个数-": 0.001659372342411483, "补充知识点18381": 0.001659372342411483, "补充知识点18382": 0.001659372342411483, "观察物体": 0.01360685320777416, "三视图的画法": 0.0012860135653688992, "根据立体图形画出平面图形(观察物体三)": 0.0005185538570035884, "根据两个方形的平面图，画出组合体其它方向看到的平面图9": 0.0005185538570035884, "补充知识点18056": 0.0005185538570035884, "补充知识点18057": 0.0005185538570035884, "正方体的表面积": 0.0015349194167306216, "正方体表面积的应用": 0.0002281636970815789, "生活中的正方体面积问题": 0.0002281636970815789, "正方体表面积的实际问题(已知棱长和)": 0.0002281636970815789, "补充知识点18545": 0.0002281636970815789, "立体图形切挖的体积问题（长方体、正方体）": 0.001659372342411483, "长/正方体挖取部分的体积问题-（考法需重放）": 0.0009956234054468898, "求原体积（知总长、段数、增加面积）-（移后删）": 0.0009956234054468898, "补充知识点20107": 0.0009956234054468898, "补充知识点20108": 0.0009956234054468898, "体积、容积及其单位": 0.0038995250046669847, "容积及容积单位的认识": 0.0016386301881313394, "认识容积单位": 0.0013482400282093297, "体积、容积辨析-": 0.0013482400282093297, "补充知识点21945": 0.0013482400282093297, "正方体的体积": 0.0015971458795710522, "正方体体积的计算": 0.0008711704797660285, "1m3，ldm3，1cm3的认识": 0.0008711704797660285, "补充知识点18648": 0.0008711704797660285, "补充知识点18649": 0.0008711704797660285, "正方体表面积的计算": 0.0013067557196490428, "正方体表面积": 0.0010993341768476073, "正方体各个面积的认识-": 0.0010993341768476073, "补充知识点18532": 0.0010993341768476073, "质数与合数的认识及运用": 0.003795814233266267, "质数与合数的综合运用（一）": 0.000850428325485885, "质数、合数的辨析-": 0.000850428325485885, "补充知识点1377": 0.000850428325485885, "质数的认识": 0.0010371077140071767, "枚举法找符合要求的质数": 0.0010371077140071767, "补充知识点1368": 0.0010371077140071767, "运算结果奇偶性综合运用-": 0.001203044948248325, "复杂算式结果的奇偶性判断-": 0.001203044948248325, "补充知识点1306": 0.001203044948248325, "和的奇偶性": 0.0012445292568086122, "由和奇偶性，写出满足条件的加数-": 0.0012445292568086122, "补充知识点1321": 0.0012445292568086122, "2、5的倍数特征": 0.0035054240733442574, "解决有关2的倍数的实际问题": 0.00014519507996100476, "解决简单实际问题(最多最少)": 0.00014519507996100476, "补充知识点1129": 0.00014519507996100476, "2、3、5的倍数特征综合": 0.0032772603762626787, "解决有关2、5、3的倍数的实际问题": 0.00035261662276244013, "2、5、3的倍数特征": 0.001659372342411483, "补充知识点1211": 0.00035261662276244013, "因数和倍数的认识": 0.003484681919064114, "因数的运用-": 0.0008089440169255979, "数的因数个数的规律-": 0.0008089440169255979, "补充知识点986": 0.0008089440169255979, "根据三视图确认几何体": 0.0057455767355997596, "通过三视图会摆放立体图": 0.004189915164588994, "从一个方向看用小正方体摆出相应组合体-": 0.0015556615710107652, "添加(或去掉)一个小正方体，从某一方向看到的平面图形": 0.0015556615710107652, "补充知识点18086": 0.0015556615710107652, "从三个方向看用小正方体摆出相应组合体": 0.000850428325485885, "判断组合体的三视图": 0.000850428325485885, "补充知识点18079": 0.000850428325485885, "找一个数的倍数及倍数的特征": 0.00031113231420215305, "找出一个数的倍数-": 0.00031113231420215305, "根据倍数的特征填□": 0.00031113231420215305, "补充知识点1046": 0.00031113231420215305, "通过数字还原立体图": 0.0007259753998050237, "根据积木上的数字还原几何体": 0.0007259753998050237, "由上面数字，绘制组合体两个方向看到的平面图形-": 0.0007259753998050237, "补充知识点18130": 0.0007259753998050237, "立体图形计数": 0.002634253593578229, "从一个方向看组合体确定小正方体数量": 0.00012445292568086122, "求从某个方向看到正方形的数量": 0.00012445292568086122, "补充知识点25336": 0.00012445292568086122, "补充知识点25337": 0.00012445292568086122, "补充知识点25338": 0.00012445292568086122, "由观察到的图形确定立体图形需要的小正方体数量": 0.0004978117027234449, "从两个方向看确定所需小正方体的数量": 0.0004978117027234449, "补充知识点25327": 0.0004978117027234449, "补充知识点25328": 0.0004978117027234449, "补充知识点25329": 0.0004978117027234449, "确定立体图形的摆法-一个方向": 0.0008711704797660285, "由一个方向确定拼摆的摆法": 0.0008711704797660285, "补充知识点18102": 0.0008711704797660285, "确定立体图形的摆法-两个方向": 0.00024890585136172244, "正方体数量不定型的摆法": 0.00024890585136172244, "补充知识点18097": 0.00024890585136172244, "学科情境中的搭配问题": 0.0007882018626454544, "数字组成问题": 0.0007882018626454544, "补充知识点14723": 0.0007882018626454544, "补充知识点14724": 0.0007882018626454544, "实际情境中的搭配": 0.0022401526622555018, "搭配餐食": 0.0022401526622555018, "补充知识点14703": 0.0022401526622555018, "补充知识点14704": 0.0022401526622555018, "长方形计数": 4.1484308560287074e-05, "独立图形中数长方形": 4.1484308560287074e-05, "补充知识点25234": 4.1484308560287074e-05, "补充知识点25235": 4.1484308560287074e-05, "补充知识点25236": 4.1484308560287074e-05, "较复杂的取币值问题": 0.0005600381655638754, "有几种取币的情况": 0.0005600381655638754, "补充知识点14611": 0.0005600381655638754, "补充知识点14612": 0.0005600381655638754, "小数的写法（一位小数）": 0.00012445292568086122, "根据读法写小数": 0.00012445292568086122, "补充知识点1946": 0.00012445292568086122, "补充知识点1947": 0.00012445292568086122, "小数连减解决实际问题（一位小数）": 0.00035261662276244013, "求三段中的其中一段": 0.00035261662276244013, "补充知识点7326": 0.00035261662276244013, "小数加减法的错中求解（一位小数）": 0.000539296011283732, "错中求解（看错、看漏小数点）": 0.000539296011283732, "补充知识点23844": 0.000539296011283732, "补充知识点23845": 0.000539296011283732, "补充知识点23846": 0.000539296011283732, "小数排列的规律": 0.00024890585136172244, "根据规律写小数": 0.00024890585136172244, "递增加法规律": 0.00024890585136172244, "补充知识点23328": 0.00024890585136172244, "补充知识点23329": 0.00024890585136172244, "补充知识点23330": 0.00024890585136172244, "一位小数加、减法的应用": 0.0006222646284043061, "计算并在数轴上表示结果": 0.0006222646284043061, "补充知识点7179": 0.0006222646284043061, "同分母分数的大小比较": 0.00041484308560287074, "两个同分母分数比大小": 0.00020742154280143537, "补充知识点2601": 0.00020742154280143537, "探究大正方体变化情况": 0.0010578498682873203, "两面涂色": 8.296861712057415e-05, "挖掉若干小正方体后，求两面涂色的小正方体的数量": 8.296861712057415e-05, "补充知识点18762": 8.296861712057415e-05, "补充知识点18763": 8.296861712057415e-05, "不确定现象和确定现象": 2.0742154280143537e-05, "用一定、可能或不可能描述随机现象(一)": 2.0742154280143537e-05, "补充知识点22905": 2.0742154280143537e-05, "补充知识点22906": 2.0742154280143537e-05, "补充知识点22907": 2.0742154280143537e-05, "长方体和正方体综合": 0.0026757379021385163, "正方体和长方体的综合运用": 0.0004978117027234449, "个数已知的小正方体摆不同几何体，判断表面积、体积大小关系": 0.0004978117027234449, "补充知识点18262": 0.0004978117027234449, "补充知识点18263": 0.0004978117027234449, "长方体和正方体体积综合": 0.0010371077140071767, "长方体、正方体体积辨析-": 0.0010371077140071767, "补充知识点18206": 0.0010371077140071767, "补充知识点18207": 0.0010371077140071767, "长方体棱长的认识及运用-": 0.0010163655597270331, "从所给长度的小棒中选择小棒搭建长方体9": 0.0010163655597270331, "补充知识点18315": 0.0010163655597270331, "补充知识点18316": 0.0010163655597270331, "合数的认识": 0.00031113231420215305, "认识合数": 0.00031113231420215305, "补充知识点1364": 0.00031113231420215305, "列综合算式解决问题(表内混合运算)": 0.0007467175540851673, "列综合算式解决问题(有括号)(加减法)": 0.0007467175540851673, "补充知识点6954": 0.0007467175540851673, "找出物品对应的形状": 2.0742154280143537e-05, "描述生活中的物品(选择)": 2.0742154280143537e-05, "补充知识点18137": 2.0742154280143537e-05, "补充知识点18138": 2.0742154280143537e-05, "补充知识点18139": 2.0742154280143537e-05, "分数的基本性质的应用": 0.004086204393188277, "分数的基本性质应用": 0.0006844910912447367, "涂色并比较所给分数大小-": 0.0006844910912447367, "补充知识点2957": 0.0006844910912447367, "补充知识点2958": 0.0006844910912447367, "通过三视图还原立体图": 0.0008296861712057415, "根据从两个方向看到的图形推测几何组合体": 2.0742154280143537e-05, "根据从两个方向看到的图形，判断符合条件的几何体": 2.0742154280143537e-05, "补充知识点18109": 2.0742154280143537e-05, "长正方体拼结的表面积问题": 0.0009333969426064591, "求正方体表面积（长+正型）": 0.0009333969426064591, "补充知识点19408": 0.0009333969426064591, "补充知识点19409": 0.0009333969426064591, "比较小数大小的方法": 0.0027379643649789467, "补充知识点2388": 0.0013689821824894733, "补充知识点2389": 0.0013689821824894733, "以亿\"作单位的改写与取近似数(小数)\"": 0.001514177262450478, "以亿\"作单位的改写\"": 0.001514177262450478, "补充知识点2435": 0.001514177262450478, "整万数的读法和写法": 2.0742154280143537e-05, "整万数的写法": 2.0742154280143537e-05, "补充知识点747": 2.0742154280143537e-05, "解决与小数近似数有关的实际问题": 0.0005600381655638754, "求长方形面积": 0.0005600381655638754, "补充知识点2474": 0.0005600381655638754, "带单位的小数比较": 0.0016801144966916263, "两个带不同单位的小数比大小": 0.0016801144966916263, "补充知识点1786": 0.0016801144966916263, "补充知识点1787": 0.0016801144966916263, "运用小数点移动规律计算": 0.0011408184854078945, "补充知识点2244": 0.0011408184854078945, "补充知识点2245": 0.0011408184854078945, "沏茶问题": 0.0010578498682873203, "合理安排时间": 0.00014519507996100476, "判断哪些事情可以同时做": 0.00014519507996100476, "补充知识点14964": 0.00014519507996100476, "运用乘除混合运算解决问题": 0.0001659372342411483, "乘两位数计算应用": 0.0001659372342411483, "补充知识点6492": 0.0001659372342411483, "年龄问题": 0.00031113231420215305, "年龄中的倍数问题": 0.00018667938852129183, "已知现在年龄，求几年后(前)年龄的倍数关系": 0.00018667938852129183, "补充知识点14129": 0.00018667938852129183, "补充知识点14130": 0.00018667938852129183, "不计算判断结果": 0.0004978117027234449, "选得数正确的算式": 0.0004978117027234449, "补充知识点7087": 0.0004978117027234449, "两位数除以一位数": 0.0018460517309327746, "两位数除以一位数的口算": 0.0005600381655638754, "通过口算感悟乘、除法之间的关系": 0.0005600381655638754, "补充知识点6263": 0.0005600381655638754, "计量秒的关系": 2.0742154280143537e-05, "电子表计量秒": 2.0742154280143537e-05, "补充知识点13035": 2.0742154280143537e-05, "质数、合数、因数、倍数的运用-": 0.0005185538570035884, "根据条件找数-": 0.0005185538570035884, "补充知识点1391": 0.0005185538570035884, "因数和倍数的意义及关系": 0.0024475742050569372, "根据乘法算式认识因数、倍数": 0.0024475742050569372, "补充知识点1002": 0.0024475742050569372, "折扣问题": 0.0035883926904648317, "求现价（折扣问题）": 0.0007674597083653108, "已知原价和折扣，求现价": 0.0005600381655638754, "多种优惠叠加求现价-": 0.0005600381655638754, "补充知识点8644": 0.0005600381655638754, "打折的意义及应用（分数）": 0.000850428325485885, "折扣的意义": 0.000850428325485885, "对几折\"的理解-\"": 0.000850428325485885, "补充知识点8632": 0.000850428325485885, "成数的意义": 0.000850428325485885, "成数和百分数的互化": 0.000850428325485885, "补充知识点3297": 0.000850428325485885, "补充知识点3298": 0.000850428325485885, "平面图形的认识": 4.1484308560287074e-05, "图形间的关系": 4.1484308560287074e-05, "补充知识点15407": 4.1484308560287074e-05, "补充知识点15408": 4.1484308560287074e-05, "同圆(或等圆)中直径和半径的关系": 0.0001659372342411483, "求半径与直径的关系": 0.0001659372342411483, "补充知识点17144": 0.0001659372342411483, "补充知识点17145": 0.0001659372342411483, "圆的认识": 0.00024890585136172244, "认识圆的各部分名称（无图）": 0.00024890585136172244, "补充知识点17156": 0.00024890585136172244, "补充知识点17157": 0.00024890585136172244, "圆面积的应用": 0.000539296011283732, "圆的周长、面积与半径的倍数关系(不带比)": 8.296861712057415e-05, "圆的周长与半径的倍数关系": 0.0002281636970815789, "补充知识点17336": 8.296861712057415e-05, "圆周率的意义": 0.00010371077140071768, "圆周率的概念": 0.00010371077140071768, "补充知识点17227": 0.00010371077140071768, "圆环的面积": 0.00041484308560287074, "圆环面积的实际应用（考法需和圆环面积计算重整）": 6.222646284043061e-05, "求圆环面积(知内外半径)": 6.222646284043061e-05, "补充知识点17347": 6.222646284043061e-05, "补充知识点17348": 6.222646284043061e-05, "含圆的组合图形的计算（周长和面积）": 0.0008296861712057415, "含圆的组合图形的周长": 0.00020742154280143537, "求与圆有关的不规则图形的周长": 0.00020742154280143537, "圆和长(正)方形的组合图形的周长": 0.00020742154280143537, "补充知识点17463": 0.00020742154280143537, "角度的简单计算--对折": 6.222646284043061e-05, "圆形对折": 6.222646284043061e-05, "补充知识点19974": 6.222646284043061e-05, "补充知识点19975": 6.222646284043061e-05, "补充知识点19976": 6.222646284043061e-05, "多边形内角和的应用": 0.00024890585136172244, "已知多边形内角和，求多边形形状": 0.00024890585136172244, "补充知识点17023": 0.00024890585136172244, "补充知识点17024": 0.00024890585136172244, "成绩中的负数": 0.00018667938852129183, "由正负数所记分数，求实际分数-": 0.00018667938852129183, "补充知识点3471": 0.00018667938852129183, "补充知识点3472": 0.00018667938852129183, "小数比大小的应用": 0.0006430067826844496, "交换数位上的数字后得最大/最小数": 0.0006430067826844496, "补充知识点2404": 0.0006430067826844496, "补充知识点2405": 0.0006430067826844496, "解决小数与单位换算的实际问题": 0.0016386301881313394, "对比用去后比较剩下的长短(需单位换算)": 0.0016386301881313394, "补充知识点1864": 0.0016386301881313394, "补充知识点1865": 0.0016386301881313394, "十几减5~2的退位减法": 0.0014104664910497605, "算式比大小（十几减5、4、）": 0.00010371077140071768, "先计算再比较大小": 0.0001659372342411483, "补充知识点4256": 0.00010371077140071768, "算式比大小（十几减7、6）": 0.0001659372342411483, "算式排序": 0.0001659372342411483, "补充知识点4199": 0.0001659372342411483, "两位数加两位数(不进位)解决实际问题": 0.00014519507996100476, "两位数加两位数的简单实际应用": 0.00014519507996100476, "补充知识点4522": 0.00014519507996100476, "图形的旋转": 0.0007467175540851673, "找到旋转规律并接着画(顺/逆时针)": 0.0007467175540851673, "补充知识点20605": 0.0007467175540851673, "补充知识点20606": 0.0007467175540851673, "补充知识点20607": 0.0007467175540851673, "同一物体多个比例尺问题（一物两图）-": 0.00024890585136172244, "知其中一图比例尺，对比求另一图比例尺（一物画两图）-": 0.00024890585136172244, "补充知识点12780": 0.00024890585136172244, "补充知识点12781": 0.00024890585136172244, "笔算两位数加一位数(不进位)": 0.00014519507996100476, "列竖式计算两位数+一位数(不进位)": 0.00014519507996100476, "补充知识点4441": 0.00014519507996100476, "奇数与偶数的运用": 0.0006637489369645932, "按要求组数": 0.0006637489369645932, "补充知识点1256": 0.0006637489369645932, "分数单位的意义": 0.0015349194167306216, "根据分数直接写分数单位": 0.0015349194167306216, "补充知识点2546": 0.0015349194167306216, "求两个数公因数/最大公因数的方法": 0.0015349194167306216, "集合法求两数最大公因数": 0.0015349194167306216, "补充知识点1459": 0.0015349194167306216, "同分母分数减法在实际生活的应用-": 0.00035261662276244013, "同分母分数减法的应用（直接计算）": 0.00035261662276244013, "补充知识点7902": 0.00035261662276244013, "周期问题": 0.0018667938852129182, "简单周期问题": 0.0019497625023334924, "日历中的周期问题": 0.00041484308560287074, "求某个月有几个星期零几天": 0.00041484308560287074, "补充知识点26043": 0.00041484308560287074, "补充知识点26044": 0.00041484308560287074, "补充知识点5856": 0.000580780319844019, "根据除余关系求除数": 0.00041484308560287074, "根据余数判断最小除数": 0.00041484308560287074, "补充知识点5893": 0.00041484308560287074, "基本周期问题": 0.0014519507996100475, "排队问题": 0.001659372342411483, "补充知识点26063": 0.0014519507996100475, "补充知识点26064": 0.0014519507996100475, "周长的认识和大小比较": 0.0009333969426064591, "比较图形的周长": 0.0004978117027234449, "不规则图形比较周长": 0.0004978117027234449, "补充知识点16011": 0.0004978117027234449, "补充知识点16012": 0.0004978117027234449, "约分的认识及应用": 0.0015556615710107652, "约分的意义和方法": 0.001203044948248325, "通过画图法理解约分的含义": 0.001203044948248325, "补充知识点3035": 0.001203044948248325, "补充知识点3036": 0.001203044948248325, "用分数基本性质推理求分数-": 0.0001659372342411483, "知原分数分子、分母和，求原分数-": 0.0001659372342411483, "补充知识点3019": 0.0001659372342411483, "补充知识点3020": 0.0001659372342411483, "选用一种图形作单位来测量面积": 0.0014312086453299039, "一种图形作单位求面积的大小": 0.0014312086453299039, "补充知识点16177": 0.0014312086453299039, "补充知识点16178": 0.0014312086453299039, "认识周长": 0.0003318744684822966, "判断物体是否有周长": 0.0003318744684822966, "补充知识点16043": 0.0003318744684822966, "补充知识点16044": 0.0003318744684822966, "巧求周长": 0.0004978117027234449, "不规则图形求周长": 0.000269648005641866, "缺角型计算周长": 0.000269648005641866, "补充知识点25493": 0.000269648005641866, "补充知识点25494": 0.000269648005641866, "改变正方形的边长求面积变化量": 0.0004770695484433013, "边长增加(或减少)，面积变化": 0.0004770695484433013, "补充知识点16347": 0.0004770695484433013, "补充知识点16348": 0.0004770695484433013, "四边形内角和的认识": 0.000580780319844019, "求剪拼后四边形的内角和": 0.000580780319844019, "补充知识点17031": 0.000580780319844019, "补充知识点17032": 0.000580780319844019, "小数减法(小数部分位数不同)": 0.0001659372342411483, "两个小数的数位不对齐，但不借位": 0.0001659372342411483, "补充知识点7202": 0.0001659372342411483, "正、反比例意义辨析": 0.0003318744684822966, "程序操作中判断正反比例": 0.0003318744684822966, "补充知识点12496": 0.0003318744684822966, "补充知识点12497": 0.0003318744684822966, "圆锥体积公式的正用": 0.0007467175540851673, "部分圆锥体积的计算": 0.0007467175540851673, "补充知识点19350": 0.0007467175540851673, "补充知识点19351": 0.0007467175540851673, "圆锥的认识及特征": 0.0016386301881313394, "圆锥的认识（底面、侧面、高）": 0.0007882018626454544, "圆锥的认识（各部分名称）": 0.0007882018626454544, "补充知识点19220": 0.0007882018626454544, "补充知识点19221": 0.0007882018626454544, "平行底面切/拼圆柱的体积计算": 0.0004355852398830143, "求小圆柱体积（知增加面积+原圆柱长+段数）": 0.0004355852398830143, "补充知识点20191": 0.0004355852398830143, "补充知识点20192": 0.0004355852398830143, "圆柱展开图的运用（涉及圆柱底面信息、高）": 0.0006222646284043061, "由展开图信息，求圆柱底面相关量/高": 0.0006222646284043061, "补充知识点18916": 0.0006222646284043061, "补充知识点18917": 0.0006222646284043061, "收支中的负数": 0.00024890585136172244, "由表格中数据计算收入/支出-": 0.00024890585136172244, "补充知识点3507": 0.00024890585136172244, "补充知识点3508": 0.00024890585136172244, "反比例的应用": 0.0027379643649789467, "反比例的应用（一）": 0.0023023791250959325, "简单的反比例的应用（直接计算）": 0.0023023791250959325, "补充知识点12612": 0.0023023791250959325, "补充知识点12613": 0.0023023791250959325, "解决与圆锥体积有关的实际问题（一）": 0.00037335877704258366, "圆柱/酒瓶中液体倒入圆锥的问题": 0.00037335877704258366, "补充知识点19340": 0.00037335877704258366, "补充知识点19341": 0.00037335877704258366, "圆柱的体积（容积）": 0.004189915164588994, "圆柱体积（容积）的实际应用（一）": 0.0012445292568086122, "计算后解决净含量是否正确问题-": 0.0012445292568086122, "补充知识点19088": 0.0012445292568086122, "补充知识点19089": 0.0012445292568086122, "圆柱体积公式的正用": 0.001659372342411483, "圆柱体积有关的综合辨析-": 0.001659372342411483, "补充知识点19118": 0.001659372342411483, "补充知识点19119": 0.001659372342411483, "圆柱的表面积的应用": 0.00041484308560287074, "圆柱中倒水求接触面积-": 0.00041484308560287074, "补充知识点19038": 0.00041484308560287074, "补充知识点19039": 0.00041484308560287074, "圆柱的展开图认识": 0.0009333969426064591, "最短爬行路线-": 0.0009333969426064591, "补充知识点18874": 0.0009333969426064591, "补充知识点18875": 0.0009333969426064591, "圆柱体积公式的逆用": 0.00020742154280143537, "求底面积（知体积+高）": 0.00020742154280143537, "补充知识点19108": 0.00020742154280143537, "补充知识点19109": 0.00020742154280143537, "利率问题": 0.0022401526622555018, "求利息": 0.0010371077140071767, "存款利息-": 0.0010371077140071767, "购物中的分期还款问题（移后删）": 0.0010371077140071767, "补充知识点8734": 0.0010371077140071767, "了解利率": 0.0003941009313227272, "本金、利息、利率的含义": 0.0003941009313227272, "了解利率-": 0.0003941009313227272, "补充知识点8755": 0.0003941009313227272, "正负数的概念及辨认": 0.0008296861712057415, "用正、负数表示相反意义的量-": 0.00014519507996100476, "用正负数记录具有相反意义的量": 0.00014519507996100476, "补充知识点3439": 0.00014519507996100476, "补充知识点3440": 0.00014519507996100476, "正比例在行程问题中的应用（一）": 0.00031113231420215305, "求行驶时间（知图上距离，行驶速度）-": 0.00031113231420215305, "补充知识点12562": 0.00031113231420215305, "补充知识点12563": 0.00031113231420215305, "用反比例解决行程问题-": 0.00018667938852129183, "求返回时间（需先计算量，再解决问题）": 0.00018667938852129183, "补充知识点12648": 0.00018667938852129183, "补充知识点12649": 0.00018667938852129183, "等量代换": 0.0006222646284043061, "含三个对象等量代换求质量": 0.0001659372342411483, "三个量代换(文字)": 0.0001659372342411483, "补充知识点10928": 0.0001659372342411483, "补充知识点10929": 0.0001659372342411483, "补充知识点10930": 0.0001659372342411483, "分数的初步认识": 0.0002281636970815789, "分数的各部分名称以及读写法": 4.1484308560287074e-05, "写分数各部分名称": 4.1484308560287074e-05, "补充知识点2491": 4.1484308560287074e-05, "由比例尺求图上距离": 0.0015764037252909088, "求图上距离（知缩小数值比例尺+实际距离）": 0.0015764037252909088, "补充知识点12820": 0.0015764037252909088, "补充知识点12821": 0.0015764037252909088, "比例尺的定义及分类": 0.0014312086453299039, "比例尺的定义": 0.0014312086453299039, "补充知识点12734": 0.0014312086453299039, "补充知识点12735": 0.0014312086453299039, "质数与合数的应用题": 0.000539296011283732, "哥德巴赫猜想的运用-": 0.00024890585136172244, "判断算式是否符合猜想": 0.00024890585136172244, "补充知识点1424": 0.00024890585136172244, "与最小公倍数有关的推理问题-": 0.000539296011283732, "知多个合数的最小公倍数，求合数-": 0.000539296011283732, "补充知识点1552": 0.000539296011283732, "求两数公因数、最大公因数的特殊情况": 0.0030490966791810997, "特殊关系两数的最大公因数-": 0.0030490966791810997, "补充知识点1489": 0.0030490966791810997, "认识互质数": 0.000269648005641866, "互质数的含义": 0.000269648005641866, "补充知识点1466": 0.000269648005641866, "用分数基本性质解决分数值仍不变的问题-": 0.0014934351081703347, "分子、分母乘/除某数-": 0.0014934351081703347, "补充知识点3013": 0.0014934351081703347, "补充知识点3014": 0.0014934351081703347, "含字母的分数问题-": 0.0007674597083653108, "分子中含相同字母，求其最大、最小值（真、假分数）-": 0.0007674597083653108, "补充知识点2859": 0.0007674597083653108, "补充知识点2860": 0.0007674597083653108, "等余问题": 0.0003941009313227272, "等余问题求一共(总量已知)": 0.0003941009313227272, "补充知识点5925": 0.0003941009313227272, "进一法应用题": 0.0012860135653688992, "补充知识点5935": 0.0006430067826844496, "看图列综合算式并计算": 0.00020742154280143537, "看图列综合算式(含括号)": 0.00020742154280143537, "补充知识点6923": 0.00020742154280143537, "优惠策略": 0.0006430067826844496, "满减优惠问题": 0.0006430067826844496, "补充知识点6947": 0.0006430067826844496, "口算两位数加整十数(不进位)": 6.222646284043061e-05, "补充知识点4449": 6.222646284043061e-05, "错题改正(两位数减一位数、整十数)": 2.0742154280143537e-05, "错题改正(两位数减整十数)": 2.0742154280143537e-05, "补充知识点4486": 2.0742154280143537e-05, "探索珠子的个数与摆出的数的个数之间的关系": 0.00031113231420215305, "已知珠子的颗数，求摆的数的个数": 0.00031113231420215305, "补充知识点557": 0.00031113231420215305, "求鸽巢数量-": 0.000580780319844019, "求鸽巢总数（知总数，至少一个巢的鸽子数）-": 0.000580780319844019, "补充知识点15296": 0.000580780319844019, "长（正）方形裁剪后的面积问题": 0.00031113231420215305, "沿折痕剪开后每个图形的面积（长方形）": 0.00031113231420215305, "补充知识点19944": 0.00031113231420215305, "补充知识点19945": 0.00031113231420215305, "补充知识点19946": 0.00031113231420215305, "利润问题": 0.00024890585136172244, "知盈利亏损情况，求成本-": 2.0742154280143537e-05, "求进价（知减去定价的不同百分率后盈利亏损情况）": 2.0742154280143537e-05, "补充知识点8829": 2.0742154280143537e-05, "补充知识点8830": 2.0742154280143537e-05, "错中求解（小数乘整数）9": 6.222646284043061e-05, "写错数位上的数字": 6.222646284043061e-05, "补充知识点7382": 6.222646284043061e-05, "较复杂鸽巢原理\"（与数字性质/运算有关）\"": 0.00018667938852129183, "取出奇偶倍因数的鸽巢原理问题-": 0.00018667938852129183, "补充知识点15301": 0.00018667938852129183, "质、合、奇、偶数综合运用": 0.0013067557196490428, "补充知识点1277": 0.0013067557196490428, "质数的运用": 0.0003941009313227272, "质数的个位数字特征": 0.0003941009313227272, "补充知识点1352": 0.0003941009313227272, "公倍数和最小公倍数的意义": 0.0015349194167306216, "理解公倍数和最小公倍数的含义": 0.0015349194167306216, "补充知识点1510": 0.0015349194167306216, "口算两位数减整十数解决实际问题": 0.00020742154280143537, "两位数减整十数的简单实际应用": 0.00020742154280143537, "补充知识点4503": 0.00020742154280143537, "两位数与一位数的不退位减法": 0.0006222646284043061, "口算两位数减一位数(不退位)解决实际问题": 0.000269648005641866, "还要多少一样多/超过": 0.000269648005641866, "补充知识点4473": 0.000269648005641866, "移多补少(求原来)(100以内)": 0.0001659372342411483, "移多补少求原来多多少": 0.0001659372342411483, "补充知识点24031": 0.0001659372342411483, "补充知识点24032": 0.0001659372342411483, "补充知识点24033": 0.0001659372342411483, "正(长)方体的展开图中相对面的判定": 0.000269648005641866, "由展开图找对面的数字": 0.000269648005641866, "补充知识点18449": 0.000269648005641866, "补充知识点18450": 0.000269648005641866, "运用分数通分比较大小来解决问题": 0.002323121279376076, "根据走过相同路程所需时间来比较速度": 0.002323121279376076, "补充知识点2646": 0.002323121279376076, "等积变形（长方体、正方体）": 0.0014934351081703347, "等体积变形运用（长正方体）": 0.0010578498682873203, "在熔铸问题中的应用-": 0.0010578498682873203, "补充知识点19674": 0.0010578498682873203, "补充知识点19675": 0.0010578498682873203, "经济问题(表内除法)": 0.0011408184854078945, "经济问题(求数量)": 0.0011408184854078945, "补充知识点14221": 0.0011408184854078945, "补充知识点14222": 0.0011408184854078945, "买票问题": 0.0010993341768476073, "不涉及团体票，求总价": 0.0010993341768476073, "补充知识点15010": 0.0010993341768476073, "连乘的计算与应用": 0.0013689821824894733, "连乘的计算": 0.0013689821824894733, "补充知识点6592": 0.0013689821824894733, "统计表": 0.001203044948248325, "复式统计表的特点及填补": 0.001203044948248325, "制作复式统计表": 0.0009541390968866026, "可合并成一张统计表的特点": 0.0009541390968866026, "补充知识点22312": 0.0009541390968866026, "补充知识点22313": 0.0009541390968866026, "三位数除以一位数(每一位都能整除)的笔算": 0.000269648005641866, "竖式中某一部分的含义": 0.000269648005641866, "补充知识点6345": 0.000269648005641866, "方向变化推理题（四个方向）": 0.0008711704797660285, "转身与方向": 0.0010578498682873203, "补充知识点21190": 0.0008711704797660285, "补充知识点21191": 0.0008711704797660285, "几百几十（几千几百）数除以一位数的口算解决实际问题": 0.001161560639688038, "满赠问题": 0.001161560639688038, "补充知识点6238": 0.001161560639688038, "整十、整百、整千数除以一位数的实际问题": 0.0008089440169255979, "计算并比大小的实际问题": 0.0008089440169255979, "补充知识点6212": 0.0008089440169255979, "加、减、乘、除各部分间的关系的计算与应用": 0.0007467175540851673, "求等式中符号代表的未知数(破译密码)": 0.0007467175540851673, "补充知识点9103": 0.0007467175540851673, "补充知识点9104": 0.0007467175540851673, "物体三视图的认识": 0.006098193358362199, "从多个方向看到的形状相同的几何组合体": 0.0004770695484433013, "选择从不同方向观察到的形状相同的立体图形": 0.0004770695484433013, "补充知识点18006": 0.0004770695484433013, "补充知识点18007": 0.0004770695484433013, "乘法运算定律和除法的运算性质的综合运用": 0.001078592022567464, "运用乘法运算定律和除法的运算性质填空": 0.001078592022567464, "补充知识点9647": 0.001078592022567464, "补充知识点9648": 0.001078592022567464, "运用减法的运算性质简算": 0.0015971458795710522, "运用减法的性质进行两种方法的简算": 0.0015971458795710522, "补充知识点9845": 0.0015971458795710522, "补充知识点9846": 0.0015971458795710522, "除法的运算性质": 0.0009126547883263156, "运用实例探究除法的运算性质": 0.0009126547883263156, "补充知识点9685": 0.0009126547883263156, "补充知识点9686": 0.0009126547883263156, "小数的数位顺序表与计数单位综合应用": 0.0012237871025284686, "辨析相同数字在不同数位上的含义": 0.0012237871025284686, "补充知识点2130": 0.0012237871025284686, "补充知识点2131": 0.0012237871025284686, "两位数乘两位数的错中求解问题": 0.0004978117027234449, "看错了一个乘数": 0.0004978117027234449, "补充知识点23832": 0.0004978117027234449, "补充知识点23833": 0.0004978117027234449, "补充知识点23834": 0.0004978117027234449, "运用比的基本性质解决比值不变的问题": 0.000269648005641866, "比的前项或后项加上/减去一个数": 0.000269648005641866, "补充知识点11763": 0.000269648005641866, "补充知识点11764": 0.000269648005641866, "高变化有关的长、正方体体积计算-": 0.0001659372342411483, "求原长方体体积（截去高长变正）": 0.0001659372342411483, "补充知识点18274": 0.0001659372342411483, "补充知识点18275": 0.0001659372342411483, "运用乘法分配律解决实际问题": 0.0026964800564186595, "根据算式提出问题": 0.0026964800564186595, "补充知识点9627": 0.0026964800564186595, "补充知识点9628": 0.0026964800564186595, "租船/车问题": 0.001659372342411483, "计算人均费用": 0.001659372342411483, "补充知识点14999": 0.001659372342411483, "运用乘法交换律和结合律简算": 0.0007052332455248803, "改成两个数相乘，找好朋友数\"简算\"": 0.0007052332455248803, "补充知识点9897": 0.0007052332455248803, "补充知识点9898": 0.0007052332455248803, "与添加/去掉一个小正方体后相关的几何组合体问题": 0.0007259753998050237, "去掉一个小正方体后，剩下的图形从三个面看完全相同": 0.0007259753998050237, "补充知识点18026": 0.0007259753998050237, "补充知识点18027": 0.0007259753998050237, "错中求解(乘法分配律)": 0.0008711704797660285, "错用乘法分配律(去括号)，求计算结果与正确结果相差多少": 0.0008711704797660285, "补充知识点23658": 0.0008711704797660285, "补充知识点23659": 0.0008711704797660285, "补充知识点23660": 0.0008711704797660285, "横式谜（除数是一位数的除法）": 6.222646284043061e-05, "补充知识点24808": 6.222646284043061e-05, "补充知识点24809": 6.222646284043061e-05, "补充知识点24810": 6.222646284043061e-05, "差倍问题": 0.0004770695484433013, "隐藏的差倍问题（两位数除以一位数）": 0.00018667938852129183, "补充知识点25827": 0.00018667938852129183, "补充知识点25828": 0.00018667938852129183, "补充知识点25829": 0.00018667938852129183, "图文算式（两位数乘两位数）": 0.00010371077140071768, "由算式相等判断两个乘数的大小关系": 0.00010371077140071768, "补充知识点14057": 0.00010371077140071768, "补充知识点14058": 0.00010371077140071768, "100以内数的排序": 0.001161560639688038, "数的顺序（100以内）": 0.0007052332455248803, "找顺序不对的数": 0.0007052332455248803, "补充知识点489": 0.0007052332455248803, "算式的规律": 0.00031113231420215305, "算式的规律（整数）": 0.00031113231420215305, "探索两位数乘11的计算规律": 0.00018667938852129183, "先观察规律，再根据规律写结果": 0.00018667938852129183, "补充知识点23150": 0.00018667938852129183, "补充知识点23151": 0.00018667938852129183, "补充知识点23152": 0.00018667938852129183, "两位数乘两位数估算解决实际问题": 0.0004978117027234449, "有数值范围的估算策略": 0.0004978117027234449, "补充知识点7098": 0.0004978117027234449, "两位数乘两位数，积的位数": 0.0006222646284043061, "判断积是几位数": 0.0006222646284043061, "补充知识点7064": 0.0006222646284043061, "促销方案选择问题（多个方案）": 0.0012445292568086122, "多种优惠方式辨析-": 0.0012445292568086122, "补充知识点14153": 0.0012445292568086122, "补充知识点14154": 0.0012445292568086122, "求原价（折扣问题）": 0.0006222646284043061, "已知现价和折扣，求原价": 0.0006015224741241625, "已知折扣和赚/赔的钱数，求原价-": 0.0006015224741241625, "补充知识点8659": 0.0006015224741241625, "不规则物体的体积算法（圆柱、圆锥）": 0.0011823027939681814, "圆柱中的排水法（多个物体）-": 0.00010371077140071768, "估测物体体积（多个同样的物体）": 0.00010371077140071768, "补充知识点19664": 0.00010371077140071768, "补充知识点19665": 0.00010371077140071768, "圆柱的展开图求表面积": 0.00024890585136172244, "由长/正方形、圆的信息，求圆柱表面积": 0.00024890585136172244, "补充知识点19054": 0.00024890585136172244, "补充知识点19055": 0.00024890585136172244, "体积和体积单位的认识": 0.0022608948165356454, "体积定义": 0.0006222646284043061, "猜测物体（长宽高已给）-": 0.0006222646284043061, "补充知识点21985": 0.0006222646284043061, "成数问题": 0.0012445292568086122, "求增加或减少几成的实际问题": 0.0012237871025284686, "由增减成数，求量间百分率关系-": 0.0001659372342411483, "由等量关系判断原数大小（含成数）-（删不了）": 0.0001659372342411483, "补充知识点8776": 0.0001659372342411483, "正负数的读法和写法": 0.0006222646284043061, "正负数的读法": 0.00031113231420215305, "补充知识点3463": 0.00031113231420215305, "补充知识点3464": 0.00031113231420215305, "温度的认识及比较": 0.0008711704797660285, "温度中的负数": 0.0006637489369645932, "根据海拔与温度关系计算某位置的温度-": 0.0006637489369645932, "补充知识点3399": 0.0006637489369645932, "补充知识点3400": 0.0006637489369645932, "温度高低的比较": 0.00020742154280143537, "比较温度的高低": 0.00020742154280143537, "补充知识点3391": 0.00020742154280143537, "补充知识点3392": 0.00020742154280143537, "长方形旋转形成的圆柱体积计算": 0.00031113231420215305, "绕边旋转一定角度，求体积（知长方形长宽）-": 0.00031113231420215305, "补充知识点19154": 0.00031113231420215305, "补充知识点19155": 0.00031113231420215305, "圆柱与圆锥体积的关系": 0.0030490966791810997, "圆柱与圆锥的关系（公式/辨析/找图）-": 0.000539296011283732, "看图找出体积相等的圆柱和圆锥-": 0.000539296011283732, "补充知识点19290": 0.000539296011283732, "补充知识点19291": 0.000539296011283732, "运用推理法解决方格图中的方位问题（四个方向）": 4.1484308560287074e-05, "根据描述在方格图中标明位置": 4.1484308560287074e-05, "补充知识点21226": 4.1484308560287074e-05, "补充知识点21227": 4.1484308560287074e-05, "税率问题": 0.002157184045134928, "求应纳税额": 0.0012445292568086122, "求纳税额": 0.0012445292568086122, "个人所得税（工资/彩票...）": 0.0012445292568086122, "补充知识点8698": 0.0012445292568086122, "解决有关3的倍数的实际问题": 0.0001659372342411483, "解决简单实际问题": 0.0001659372342411483, "补充知识点1163": 0.0001659372342411483, "长方体棱长的实际问题-": 2.0742154280143537e-05, "为物体选择合适的包装盒尺寸-": 2.0742154280143537e-05, "补充知识点18379": 2.0742154280143537e-05, "补充知识点18380": 2.0742154280143537e-05, "长正方体拼接问题": 0.0004978117027234449, "已知拼接元件棱长，求组合体的表面积/体积": 0.0004978117027234449, "补充知识点19474": 0.0004978117027234449, "补充知识点19475": 0.0004978117027234449, "分数墙的应用": 6.222646284043061e-05, "利用分数墙比较异分母分数的大小": 6.222646284043061e-05, "补充知识点2613": 6.222646284043061e-05, "从同一位置观察多个不同几何组合体": 0.0023023791250959325, "从同一位置观察不同组合体，看到的图形可能相同，也可能不同": 0.0023023791250959325, "补充知识点18014": 0.0023023791250959325, "补充知识点18015": 0.0023023791250959325, "容积有关的复杂实际应用": 0.0010993341768476073, "物体浸没在长方体容器中的系列问题": 0.0010993341768476073, "补充知识点18696": 0.0010993341768476073, "补充知识点18697": 0.0010993341768476073, "涂色的小正方体个数": 0.0006637489369645932, "四个面涂色的小正方体个数-": 0.0006637489369645932, "补充知识点18730": 0.0006637489369645932, "补充知识点18731": 0.0006637489369645932, "分数量的理解（如3/4千克）-": 0.001203044948248325, "分数量的理解（填空）-": 0.001203044948248325, "补充知识点2517": 0.001203044948248325, "倍数和因数的综合应用": 0.0015764037252909088, "因数与倍数综合-": 0.0009748812511667462, "根据含字母的算式，判断字母间的关系-": 0.0009748812511667462, "补充知识点1073": 0.0009748812511667462, "立体图形的切挖（长方体、正方体的表面积）": 0.001472692953890191, "长正方体切割的表面积问题（考法需整合）": 0.0013067557196490428, "长（正）方体挖掉一部分后的表面积": 0.0013067557196490428, "补充知识点20067": 0.0013067557196490428, "补充知识点20068": 0.0013067557196490428, "不规则几何体体积（可分割为长正方体）-": 2.0742154280143537e-05, "求不规则几何体的表面积、体积": 2.0742154280143537e-05, "补充知识点19518": 2.0742154280143537e-05, "补充知识点19519": 2.0742154280143537e-05, "100以内数的写法": 0.0001659372342411483, "理解数的组成再写数": 0.0001659372342411483, "补充知识点451": 0.0001659372342411483, "比较数的大小解决实际问题": 0.0002281636970815789, "比赛名次问题": 0.0002281636970815789, "补充知识点478": 0.0002281636970815789, "用小棒摆平面图形": 0.00018667938852129183, "判断哪一组小棒可拼成长（正）方形": 0.00018667938852129183, "补充知识点15429": 0.00018667938852129183, "补充知识点15430": 0.00018667938852129183, "认识100": 0.0001659372342411483, "100的组成": 0.0001659372342411483, "补充知识点550": 0.0001659372342411483, "最大、最小的几位数问题": 0.00018667938852129183, "特殊要求的数的综合": 0.00018667938852129183, "补充知识点475": 0.00018667938852129183, "十几减5、4、3、2的计算": 0.000269648005641866, "十几减2-5的计算方法": 0.000269648005641866, "补充知识点4232": 0.000269648005641866, "两位数减一位数（退位）解决实际问题": 0.0005185538570035884, "还要多少才能超过/一样多": 0.0005185538570035884, "补充知识点4636": 0.0005185538570035884, "圆柱的侧面积": 0.0014519507996100475, "求圆柱侧面积": 0.0006844910912447367, "由截面面积，求侧面积-": 0.0006844910912447367, "补充知识点18950": 0.0006844910912447367, "补充知识点18951": 0.0006844910912447367, "瓶中水（含圆柱的正倒放）": 0.0008711704797660285, "求液体体积（正倒放）-": 0.0008711704797660285, "补充知识点19726": 0.0008711704797660285, "补充知识点19727": 0.0008711704797660285, "比例中某项变化解决比例仍成立问题": 0.0007259753998050237, "其中一项写错或看错": 0.0007259753998050237, "补充知识点12253": 0.0007259753998050237, "补充知识点12254": 0.0007259753998050237, "利率、本金、存期": 0.0004978117027234449, "求利率-": 0.00024890585136172244, "知存期，本金，本息和，求年利率": 0.00024890585136172244, "补充知识点8746": 0.00024890585136172244, "圆锥的展开图（含底面量计算）-": 0.0003941009313227272, "半/直径已知的圆剪成大小相等扇形，求圆锥底面量": 0.0003941009313227272, "补充知识点19244": 0.0003941009313227272, "补充知识点19245": 0.0003941009313227272, "求折扣（折扣问题）": 0.0007882018626454544, "由原价、现价、售价关系求折扣": 0.0005185538570035884, "求折扣解决问题-": 0.0005185538570035884, "补充知识点8682": 0.0005185538570035884, "公式法求圆柱表面积": 0.0012237871025284686, "由所求圆柱的面积，反找物体": 0.0012237871025284686, "补充知识点18984": 0.0012237871025284686, "补充知识点18985": 0.0012237871025284686, "利润和折扣综合求折扣": 0.0001659372342411483, "多次调价求折扣": 0.0001659372342411483, "补充知识点8669": 0.0001659372342411483, "正负数在数轴上的表示": 0.0010578498682873203, "用直线上的点表示正、负数": 0.0010578498682873203, "根据数线上的点判断说法对错": 0.0010578498682873203, "补充知识点3583": 0.0010578498682873203, "补充知识点3584": 0.0010578498682873203, "运用乘法运算定律简算": 0.0013689821824894733, "补充知识点9835": 0.0006844910912447367, "补充知识点9836": 0.0006844910912447367, "被除数、除数、商之间的关系（除数是一位数）": 0.00012445292568086122, "除数不变，商增加数与被除数增加数的关系": 0.00012445292568086122, "补充知识点9205": 0.00012445292568086122, "补充知识点9206": 0.00012445292568086122, "两位数乘两位数的数字谜": 0.00037335877704258366, "积是三位数的竖式谜": 0.00037335877704258366, "补充知识点24415": 0.00037335877704258366, "补充知识点24416": 0.00037335877704258366, "补充知识点24417": 0.00037335877704258366, "提问题\"、\"填条件\"问题(100以内)\"": 0.00012445292568086122, "根据问题选择合适的条件": 0.00012445292568086122, "补充知识点13871": 0.00012445292568086122, "补充知识点13872": 0.00012445292568086122, "解决两位数乘一位数(不进位)的实际问题": 0.00012445292568086122, "两位数乘一位数不进位笔算应用": 0.00012445292568086122, "补充知识点6052": 0.00012445292568086122, "三位数减两、三位数(连续退位，中间有0)的应用": 2.0742154280143537e-05, "距离问题": 2.0742154280143537e-05, "补充知识点5076": 2.0742154280143537e-05, "整十、整百、整千数除以一位数的应用": 0.0004978117027234449, "补充知识点6198": 0.0004978117027234449, "简单页码问题": 6.222646284043061e-05, "由连着两页的页码和，求页码": 6.222646284043061e-05, "补充知识点25955": 6.222646284043061e-05, "补充知识点25956": 6.222646284043061e-05, "补充知识点25957": 6.222646284043061e-05, "根据统计表解决问题": 0.0018875360394930618, "读取统计表信息判断条件变化是否会改变结果": 0.0018875360394930618, "补充知识点22587": 0.0018875360394930618, "补充知识点22588": 0.0018875360394930618, "补充知识点22589": 0.0018875360394930618, "乘法分配律的应用": 0.0007467175540851673, "乘法分配律计算方法与数形结合": 0.0007467175540851673, "补充知识点9617": 0.0007467175540851673, "补充知识点9618": 0.0007467175540851673, "根据平面图形确定立体图形": 0.0006637489369645932, "已知从一个方向看到的视图，求已知数量的小正方体拼成的立体图形": 0.0006637489369645932, "补充知识点18121": 0.0006637489369645932, "整数加法结合律": 0.004065462238908133, "加法交换律和结合律的综合运用": 0.0019290203480533488, "同时运用加法交换律和结合律的等式": 0.0019290203480533488, "补充知识点9415": 0.0019290203480533488, "补充知识点9416": 0.0019290203480533488, "根据立体图形画出平面图形(观察物体二)": 0.0007467175540851673, "在方格纸上画出从一个角度看到的平面图形": 0.0007467175540851673, "补充知识点18048": 0.0007467175540851673, "补充知识点18049": 0.0007467175540851673, "两个小数的大小比较": 0.0004978117027234449, "比较两个小数的大小(不含单位换算)": 0.0004978117027234449, "补充知识点2382": 0.0004978117027234449, "补充知识点2383": 0.0004978117027234449, "运用乘、除法的意义和各部分之间的关系解决实际问题": 0.0007467175540851673, "年龄问题与差倍问题": 0.0007467175540851673, "补充知识点9229": 0.0007467175540851673, "补充知识点9230": 0.0007467175540851673, "乘法运算定律的综合运用": 0.0006222646284043061, "根据乘法运算定律填空": 0.0006222646284043061, "补充知识点9583": 0.0006222646284043061, "补充知识点9584": 0.0006222646284043061, "小数的数位顺序表": 0.001472692953890191, "理解某一数位上的数表示的意义": 0.0014934351081703347, "补充知识点2106": 0.001472692953890191, "补充知识点2107": 0.001472692953890191, "整数乘法结合律": 0.0032772603762626787, "运用乘法交换律和结合律解决实际问题": 0.0012652714110887556, "解决有关面积的实际问题": 0.0012652714110887556, "补充知识点9531": 0.0012652714110887556, "补充知识点9532": 0.0012652714110887556, "除加、除减混合运算": 0.0010993341768476073, "除加除减应用题(表内混合运算)": 0.0010993341768476073, "应用题-除加": 0.0010993341768476073, "补充知识点6876": 0.0010993341768476073, "小数的性质": 0.008089440169255979, "判断原数去掉0后大小是否改变": 0.0030076123706208125, "补充知识点2038": 0.0030076123706208125, "补充知识点2039": 0.0030076123706208125, "乘法各部分之间的关系": 0.0004770695484433013, "已知算式直接得出各部分是多少": 0.0004770695484433013, "补充知识点9139": 0.0004770695484433013, "补充知识点9140": 0.0004770695484433013, "加法各部分之间的关系": 0.0008089440169255979, "加法各部分间的关系": 0.0008089440169255979, "补充知识点8995": 0.0008089440169255979, "补充知识点8996": 0.0008089440169255979, "减法各部分之间的关系": 0.00035261662276244013, "运用减法各部分间的关系解决问题": 0.00035261662276244013, "补充知识点9033": 0.00035261662276244013, "补充知识点9034": 0.00035261662276244013, "圆柱中的排水法（单个物体）-": 0.001078592022567464, "排水法测物体体积步骤": 0.001078592022567464, "补充知识点19642": 0.001078592022567464, "补充知识点19643": 0.001078592022567464, "立体图形的切拼（圆锥）": 0.0002281636970815789, "平行底面切圆锥相关问题-": 2.0742154280143537e-05, "比较剩下部分与切去部分关系-": 2.0742154280143537e-05, "补充知识点20275": 2.0742154280143537e-05, "补充知识点20276": 2.0742154280143537e-05, "组合体的表面积（圆柱）": 0.0002903901599220095, "含圆柱的立体组合图形的表面积": 0.0002903901599220095, "3个圆柱叠加": 0.0002903901599220095, "补充知识点19460": 0.0002903901599220095, "补充知识点19461": 0.0002903901599220095, "侧面积的实际应用": 0.0006015224741241625, "铁皮上剪做圆柱体求最多个数": 0.0006015224741241625, "补充知识点18938": 0.0006015224741241625, "补充知识点18939": 0.0006015224741241625, "折扣中的盈利亏损问题": 0.0001659372342411483, "进价未知，判断盈亏情况": 0.0001659372342411483, "补充知识点14261": 0.0001659372342411483, "补充知识点14262": 0.0001659372342411483, "运用除法的运算性质解决实际问题": 0.0010578498682873203, "运用除法的运算性质解决实际问题(三个数相除)": 0.0010578498682873203, "补充知识点9657": 0.0010578498682873203, "补充知识点9658": 0.0010578498682873203, "带有小括号的混合运算": 0.001203044948248325, "含有小括号的四则混合运算": 0.00041484308560287074, "根据文字描述计算含有小括号的四则混合运算": 0.00041484308560287074, "补充知识点6882": 0.00041484308560287074, "乘法分配律的逆运算": 0.0004355852398830143, "乘法分配律的逆用提取公因数，求公因数": 0.0004355852398830143, "补充知识点9607": 0.0004355852398830143, "补充知识点9608": 0.0004355852398830143, "三角形的稳定性及应用": 0.0016386301881313394, "三角形的稳定性": 0.0016386301881313394, "三角形的稳定性在生活中的应用": 0.0016386301881313394, "补充知识点16781": 0.0016386301881313394, "补充知识点16782": 0.0016386301881313394, "运用除法的运算性质进行简算": 0.0004978117027234449, "多种方法计算判断对错": 0.0004978117027234449, "补充知识点9865": 0.0004978117027234449, "补充知识点9866": 0.0004978117027234449, "运用加法运算定律简算": 0.0016801144966916263, "几个加数能凑成整十、整百、整千数的简便计算": 0.0016801144966916263, "补充知识点9905": 0.0016801144966916263, "补充知识点9906": 0.0016801144966916263, "整数减法的性质": 0.0028624172906598078, "运用减法的运算性质解决实际问题": 0.0012860135653688992, "解决求还剩的实际问题": 0.0012860135653688992, "补充知识点9483": 0.0012860135653688992, "补充知识点9484": 0.0012860135653688992, "优惠方案问题(表内除法)": 0.0005600381655638754, "优惠促销问题": 0.0005600381655638754, "补充知识点14245": 0.0005600381655638754, "补充知识点14246": 0.0005600381655638754, "位置中的负数": 0.000539296011283732, "用正负数表示物体现在位置/运动情况（横向）": 0.000539296011283732, "补充知识点3517": 0.000539296011283732, "补充知识点3518": 0.000539296011283732, "根据公式求速度": 0.00018667938852129183, "先求时间，再求速度": 0.00018667938852129183, "补充知识点14327": 0.00018667938852129183, "求便宜的钱数-": 0.0005600381655638754, "已知原价和折扣，求便宜的钱数": 0.0005600381655638754, "补充知识点8688": 0.0005600381655638754, "补充知识点8689": 0.0005600381655638754, "在方格纸上画出对称，旋转、平移后的图形（考法需整合）": 0.0012652714110887556, "数对与平移、旋转综合（图形未知）-": 0.0012652714110887556, "补充知识点20809": 0.0012652714110887556, "补充知识点20810": 0.0012652714110887556, "补充知识点20811": 0.0012652714110887556, "根据正比例的图象解决问题": 0.001078592022567464, "完善表格、描绘图线、解决问题综合-": 0.0013274978739291864, "补充知识点12452": 0.001078592022567464, "补充知识点12453": 0.001078592022567464, "数组合图形小正方体的数量": 0.00041484308560287074, "数小正方体的数量(两层)": 0.00041484308560287074, "补充知识点25360": 0.00041484308560287074, "补充知识点25361": 0.00041484308560287074, "补充知识点25362": 0.00041484308560287074, "漏看小数点写数问题": 0.0006015224741241625, "已知错误读法及原数0的读法，求原数": 0.0006015224741241625, "补充知识点1886": 0.0006015224741241625, "补充知识点1887": 0.0006015224741241625, "在数线上表示小数": 0.0012237871025284686, "判断小数在数线上的大约位置": 0.0012237871025284686, "补充知识点1770": 0.0012237871025284686, "补充知识点1771": 0.0012237871025284686, "复式统计表中开放性问题": 0.00035261662276244013, "根据统计结果给出建议/预判/解决方案等": 0.00035261662276244013, "补充知识点22578": 0.00035261662276244013, "补充知识点22579": 0.00035261662276244013, "补充知识点22580": 0.00035261662276244013, "认识复式统计表": 0.00010371077140071768, "适合用复式统计表的情况": 0.00010371077140071768, "补充知识点22324": 0.00010371077140071768, "补充知识点22325": 0.00010371077140071768, "队列问题": 0.00018667938852129183, "求队列总人数": 0.00018667938852129183, "补充知识点21172": 0.00018667938852129183, "补充知识点21173": 0.00018667938852129183, "运用推理法解决物体移动的位置与方向问题": 0.00018667938852129183, "推理单个物体移动后的位置或方向": 0.00018667938852129183, "补充知识点21298": 0.00018667938852129183, "补充知识点21299": 0.00018667938852129183, "方向变化推理题（八个方向）": 0.00018667938852129183, "补充知识点21260": 0.00018667938852129183, "补充知识点21261": 0.00018667938852129183, "稍复杂水中浸物": 0.0004978117027234449, "求容器的容积（知容器底面积、物体体积）": 0.0004978117027234449, "补充知识点19604": 0.0004978117027234449, "补充知识点19605": 0.0004978117027234449, "2、5、3倍数的运用（一）": 0.0013067557196490428, "补充知识点1183": 0.0013067557196490428, "2的倍数特征": 0.0004355852398830143, "所给数中找出2的倍数-": 0.0004355852398830143, "补充知识点1103": 0.0004355852398830143, "按要求写分数": 0.0001659372342411483, "写出给定范围內分数的最值": 0.0001659372342411483, "补充知识点2757": 0.0001659372342411483, "补充知识点2758": 0.0001659372342411483, "分率、分数量比较（如1/4与1/4米）-": 0.0014104664910497605, "直接比较（分率、分数量）-": 0.0014104664910497605, "补充知识点2654": 0.0014104664910497605, "从两个方向看组合体确定小正方体数量": 0.000580780319844019, "根据从两个方向看到的图形，确定小正方体所需最多和最少的数量": 0.000580780319844019, "补充知识点25288": 0.000580780319844019, "补充知识点25289": 0.000580780319844019, "补充知识点25290": 0.000580780319844019, "单位“1”的认识与确定": 0.0008296861712057415, "露出部分推整体": 0.0006637489369645932, "看图根据分数和所给个数求总个数-": 0.0006637489369645932, "补充知识点2567": 0.0006637489369645932, "奇数的认识（一）": 0.00041484308560287074, "已知多个奇数的和，求其中的奇数": 0.00041484308560287074, "补充知识点1249": 0.00041484308560287074, "单位换算（分数）-": 0.0007674597083653108, "大单位化小单位-": 0.0007674597083653108, "补充知识点2593": 0.0007674597083653108, "真假带分数认识综合-": 0.0019497625023334924, "给分数分类-": 0.0019497625023334924, "补充知识点2717": 0.0019497625023334924, "补充知识点2718": 0.0019497625023334924, "瓶中水（长、正方体）-": 0.0004355852398830143, "同一容器不同放置方式求液体体积": 0.0004355852398830143, "补充知识点19690": 0.0004355852398830143, "补充知识点19691": 0.0004355852398830143, "用含有括号的四则混合运算解决常见数学问题": 0.0007882018626454544, "油桶问题": 0.000850428325485885, "补充知识点6971": 0.0007882018626454544, "乘法交换律和结合律的综合运用": 0.0010993341768476073, "同时运用乘法交换律和结合律": 0.0010993341768476073, "补充知识点9543": 0.0010993341768476073, "补充知识点9544": 0.0010993341768476073, "用不含括号的三步混合运算解决实际问题": 0.0005185538570035884, "列综合算式解决经济问题": 0.0005185538570035884, "补充知识点6977": 0.0005185538570035884, "减法的运算性质": 0.001078592022567464, "错用减法凑整去括号的运算性质，求结果与正确结果相差多少": 0.001078592022567464, "补充知识点9463": 0.001078592022567464, "补充知识点9464": 0.001078592022567464, "加小括号问题综合": 0.00041484308560287074, "与添去括号相关说法的对错判断": 0.00041484308560287074, "补充知识点9363": 0.00041484308560287074, "补充知识点9364": 0.00041484308560287074, "小数的改写": 0.0018253095766526312, "根据计数单位(写法)确定改写的位数，再进行改写": 0.0009126547883263156, "补充知识点2427": 0.0009126547883263156, "按要求写数": 0.001203044948248325, "根据各数位上的数写小数": 0.001203044948248325, "补充知识点1934": 0.001203044948248325, "补充知识点1935": 0.001203044948248325, "两位数乘两位数的简便运算": 0.0002903901599220095, "结合律的变形使用": 0.0002903901599220095, "补充知识点23566": 0.0002903901599220095, "补充知识点23567": 0.0002903901599220095, "补充知识点23568": 0.0002903901599220095, "图文算式（万以内）": 2.0742154280143537e-05, "根据算式相等比较大小": 2.0742154280143537e-05, "补充知识点14045": 2.0742154280143537e-05, "补充知识点14046": 2.0742154280143537e-05, "除法应用题(7、8、9)": 0.002011988965173923, "简单除法应用题(一步)": 0.002011988965173923, "补充知识点5765": 0.002011988965173923, "表内计算及运用（含除法）": 0.0009333969426064591, "乘除法的计算(表内)": 0.0007259753998050237, "接龙计算": 0.0007259753998050237, "补充知识点5843": 0.0007259753998050237, "巧填运算符号": 0.0010163655597270331, "巧填算符(表内混合运算)": 8.296861712057415e-05, "四个数之间巧填算符": 8.296861712057415e-05, "补充知识点24157": 8.296861712057415e-05, "补充知识点24158": 8.296861712057415e-05, "补充知识点24159": 8.296861712057415e-05, "整数平均分的意义及应用": 0.004003235776067702, "大数目物品列表平均分": 0.0005185538570035884, "根据分的份数和每份数相同确定总数": 0.0005185538570035884, "补充知识点5507": 0.0005185538570035884, "包含分(按每几个一份平均分)": 0.0009333969426064591, "按每几个一份平均分": 0.0009333969426064591, "补充知识点5501": 0.0009333969426064591, "填算符(加减乘除1-9)": 0.00012445292568086122, "填算符(÷或×)": 0.00012445292568086122, "补充知识点24199": 0.00012445292568086122, "补充知识点24200": 0.00012445292568086122, "补充知识点24201": 0.00012445292568086122, "由一道乘法算式计算两道除法算式(2-6)": 0.0005185538570035884, "用同一句乘法口诀计算乘、除法算式": 0.0005185538570035884, "补充知识点5638": 0.0005185538570035884, "平均分求每份数（列除法算式）": 0.0009541390968866026, "总数相同，每份数不同的多种分法并列式计算": 0.0009541390968866026, "补充知识点5559": 0.0009541390968866026, "用正\"字记录数据\"": 0.0012445292568086122, "根据正字统计法整理的统计表提数学问题并解答": 0.0012445292568086122, "补充知识点22194": 0.0012445292568086122, "补充知识点22195": 0.0012445292568086122, "补充知识点22196": 0.0012445292568086122, "通过同时平移和旋转重合": 0.0007882018626454544, "补充知识点20893": 0.0007882018626454544, "补充知识点20894": 0.0007882018626454544, "补充知识点20895": 0.0007882018626454544, "表内除法错中求解": 0.0002903901599220095, "看错除数求商": 0.0002903901599220095, "补充知识点23676": 0.0002903901599220095, "补充知识点23677": 0.0002903901599220095, "补充知识点23678": 0.0002903901599220095, "除法应用题一(2-6)（移后删）": 0.0011823027939681814, "用除法解决经济问题(2-6)": 0.0011823027939681814, "补充知识点5799": 0.0011823027939681814, "用不同的方法记录数据": 0.0010993341768476073, "根据不同记录法整理的统计表提数学问题并解答": 0.0010993341768476073, "补充知识点22245": 0.0010993341768476073, "补充知识点22246": 0.0010993341768476073, "补充知识点22247": 0.0010993341768476073, "除法横式谜(表内2-6)": 0.000580780319844019, "转化法求算式中图形代表的数(例：加法→乘法或除法)": 0.000580780319844019, "补充知识点24616": 0.000580780319844019, "补充知识点24617": 0.000580780319844019, "补充知识点24618": 0.000580780319844019, "生活中的对称现象": 0.0008089440169255979, "倒影/平面镜呈像": 0.0008089440169255979, "补充知识点20365": 0.0008089440169255979, "补充知识点20366": 0.0008089440169255979, "补充知识点20367": 0.0008089440169255979, "用数的组成解决问题": 0.0006637489369645932, "补充知识点560": 0.0003318744684822966, "数的排列规律（100以内）": 0.000269648005641866, "数的排列规律的应用": 0.000269648005641866, "补充知识点482": 0.000269648005641866, "组数问题": 0.00012445292568086122, "组成最大（小）的两位数（含0）": 0.00012445292568086122, "补充知识点561": 0.00012445292568086122, "找规律画图": 4.1484308560287074e-05, "图形成组重复出现": 4.1484308560287074e-05, "补充知识点23399": 4.1484308560287074e-05, "补充知识点23400": 4.1484308560287074e-05, "补充知识点23401": 4.1484308560287074e-05, "油桶问题(两、三位数的加减法)": 6.222646284043061e-05, "补充知识点5089": 6.222646284043061e-05, "倍的认识": 0.000539296011283732, "运用画图法解决倍数问题": 0.00014519507996100476, "根据条件画示意图解决问题": 0.0001659372342411483, "补充知识点1664": 0.00014519507996100476, "补充知识点1665": 0.00014519507996100476, "补充知识点1666": 0.00014519507996100476, "推理求原分数-": 0.0005600381655638754, "求原分数（知原分数分子分母差、最简分数）-": 0.0005600381655638754, "补充知识点3173": 0.0005600381655638754, "补充知识点3174": 0.0005600381655638754, "在方格纸上按一定的比将图形缩小": 0.0006015224741241625, "在方格纸画出缩小后的图形": 0.0006015224741241625, "补充知识点12880": 0.0006015224741241625, "补充知识点12881": 0.0006015224741241625, "根据描述确定位置（四个方向）": 0.0003941009313227272, "由方向和距离在网格图中标位置": 0.0003941009313227272, "补充知识点21244": 0.0003941009313227272, "补充知识点21245": 0.0003941009313227272, "画圆及扇形": 6.222646284043061e-05, "画圆": 4.1484308560287074e-05, "尺规作图": 4.1484308560287074e-05, "保留作图痕迹画线段": 4.1484308560287074e-05, "补充知识点17576": 4.1484308560287074e-05, "运用平移、对称、旋转设计图案": 0.0004978117027234449, "画出与圆有关的图案": 4.1484308560287074e-05, "给定圆心和半径画圆": 4.1484308560287074e-05, "补充知识点20914": 4.1484308560287074e-05, "补充知识点20915": 4.1484308560287074e-05, "补充知识点20916": 4.1484308560287074e-05, "含圆的组合图形的面积": 0.0007674597083653108, "最大活动范围计算": 6.222646284043061e-05, "求周长（无障碍物）": 6.222646284043061e-05, "补充知识点17472": 6.222646284043061e-05, "扇形的周长和面积": 0.00018667938852129183, "扇环的面积": 2.0742154280143537e-05, "半环形的面积": 2.0742154280143537e-05, "补充知识点17508": 2.0742154280143537e-05, "补充知识点17509": 2.0742154280143537e-05, "扇形的面积": 0.00010371077140071768, "计算圆心角度数是周角度数的几分之几或扇形是圆面积的几分之几": 0.00010371077140071768, "补充知识点17516": 0.00010371077140071768, "补充知识点17517": 0.00010371077140071768, "求与圆有关的不规则图形的面积": 0.000269648005641866, "补充知识点17468": 0.000269648005641866, "圆与三角形组合的面积问题-": 0.00014519507996100476, "补充知识点17482": 0.00014519507996100476, "不变量解决比的问题": 4.1484308560287074e-05, "两个单量建立比的关系": 4.1484308560287074e-05, "补充知识点11967": 4.1484308560287074e-05, "补充知识点11968": 4.1484308560287074e-05, "已知一个分量和分量比，求其它量": 0.0001659372342411483, "已知分量比和其中1个量，求其他量（求和）": 0.0001659372342411483, "补充知识点11955": 0.0001659372342411483, "补充知识点11956": 0.0001659372342411483, "求百分率": 0.001078592022567464, "补充知识点8472": 0.000539296011283732, "补充知识点8473": 0.000539296011283732, "分数的速算与巧算": 0.00010371077140071768, "分数列项计算": 0.00010371077140071768, "分子和分母分别写成两数和与两数积的形式巧算": 0.00010371077140071768, "补充知识点23593": 0.00010371077140071768, "补充知识点23594": 0.00010371077140071768, "补充知识点23595": 0.00010371077140071768, "节约用水": 0.00010371077140071768, "根据折线统计解决问题": 0.00010371077140071768, "补充知识点22659": 0.00010371077140071768, "补充知识点22660": 0.00010371077140071768, "补充知识点22661": 0.00010371077140071768, "圆与比/倍数的综合问题": 0.0004563273941631578, "根据两圆中所给量，求其它量的关系（移后删）": 0.0004563273941631578, "补充知识点17329": 0.0004563273941631578, "方中圆和圆中方的面积问题": 0.0007882018626454544, "变形的方中圆（考法需重整）": 0.0004978117027234449, "利用正方形的面积求正方形内圆的面积": 0.0004978117027234449, "补充知识点17387": 0.0004978117027234449, "补充知识点17388": 0.0004978117027234449, "弧、圆心角、扇形的认识": 0.00012445292568086122, "扇形的认识": 0.00010371077140071768, "扇形的辨析": 0.00010371077140071768, "补充知识点17492": 0.00010371077140071768, "补充知识点17493": 0.00010371077140071768, "等周长的圆和其他图形的面积关系": 0.00014519507996100476, "补充知识点17251": 0.00014519507996100476, "圆的周长比与半径比、直径比的关系": 8.296861712057415e-05, "根据直径比判断计算周长比": 8.296861712057415e-05, "补充知识点17247": 8.296861712057415e-05, "变形的圆中方": 0.0001659372342411483, "方与圆部分重叠，相关面积的计算": 0.0001659372342411483, "补充知识点17421": 0.0001659372342411483, "补充知识点17422": 0.0001659372342411483, "圆环面积的计算": 0.00035261662276244013, "圆环的认识": 0.00035261662276244013, "补充知识点17361": 0.00035261662276244013, "补充知识点17362": 0.00035261662276244013, "圆的面积公式逆用": 2.0742154280143537e-05, "已知圆的面积求直径": 2.0742154280143537e-05, "补充知识点17309": 2.0742154280143537e-05, "圆在生活中的应用": 2.0742154280143537e-05, "所有同圆或等圆的直径都相等": 2.0742154280143537e-05, "补充知识点17166": 2.0742154280143537e-05, "补充知识点17167": 2.0742154280143537e-05, "与圆相关的轴对称图形": 0.0003318744684822966, "圆是轴对称图形": 0.0001659372342411483, "圆的轴对称应用": 0.0001659372342411483, "补充知识点17200": 0.0001659372342411483, "补充知识点17201": 0.0001659372342411483, "圆的组合图形的对称轴": 0.0001659372342411483, "根据对称轴画出图形的另一半": 0.0001659372342411483, "补充知识点17190": 0.0001659372342411483, "补充知识点17191": 0.0001659372342411483, "已知总量，分量比未知，求分量": 8.296861712057415e-05, "先求两个分量的比，再解决问题": 8.296861712057415e-05, "补充知识点11889": 8.296861712057415e-05, "补充知识点11890": 8.296861712057415e-05, "根据两数的等量关系求比": 0.0001659372342411483, "根据等量关系式求比": 0.0001659372342411483, "补充知识点11977": 0.0001659372342411483, "补充知识点11978": 0.0001659372342411483, "分数与除法": 6.222646284043061e-05, "通过平均分理解分数与除法的关系": 6.222646284043061e-05, "补充知识点8247": 6.222646284043061e-05, "用方程法解分数除法应用题": 2.0742154280143537e-05, "用分数除法解方程(单步计算)": 2.0742154280143537e-05, "补充知识点11211": 2.0742154280143537e-05, "补充知识点11212": 2.0742154280143537e-05, "三人合作工程问题": 8.296861712057415e-05, "三人合作的工效和": 8.296861712057415e-05, "补充知识点15393": 8.296861712057415e-05, "补充知识点15394": 8.296861712057415e-05, "列方程解决稍复杂的追及问题\"\"": 0.00014519507996100476, "列方程解决典型的追及问题\"\"": 0.00014519507996100476, "补充知识点11555": 0.00014519507996100476, "补充知识点11556": 0.00014519507996100476, "补充知识点11557": 0.00014519507996100476, "速度、时间、路程之间的关系": 0.00035261662276244013, "速度、时间和路程的含义": 0.00035261662276244013, "补充知识点14337": 0.00035261662276244013, "一个数除以分数的简单应用及错中求解": 2.0742154280143537e-05, "一个数除以分数的算理": 2.0742154280143537e-05, "补充知识点8260": 2.0742154280143537e-05, "三角形面积公式逆用": 0.00037335877704258366, "已知面积和高，计算底": 0.00037335877704258366, "补充知识点17078": 0.00037335877704258366, "平行四边形高与底变化引起面积的变化": 0.00012445292568086122, "底/高不变，高/底扩大(或缩小)到原来的N倍，求平行四边形面积": 0.00012445292568086122, "补充知识点16565": 0.00012445292568086122, "画平行四边形": 0.0001659372342411483, "摆平行四边形": 8.296861712057415e-05, "补充知识点16523": 8.296861712057415e-05, "补充知识点16524": 8.296861712057415e-05, "应用小数乘整数进行单位换算": 8.296861712057415e-05, "复名数换算": 8.296861712057415e-05, "补充知识点7358": 8.296861712057415e-05, "堆放材料的数量": 0.00010371077140071768, "先计算层数，再运用梯形面积公式计算数量": 0.00010371077140071768, "补充知识点16680": 0.00010371077140071768, "三角形面积的实际运用": 0.00018667938852129183, "运用三角形面积计算总量": 0.00018667938852129183, "补充知识点17109": 0.00018667938852129183, "列方程解相遇问题": 0.0002903901599220095, "列方程解决简单的相遇问题\"\"": 0.0001659372342411483, "求相遇时间": 0.0001659372342411483, "补充知识点11480": 0.0001659372342411483, "补充知识点11481": 0.0001659372342411483, "补充知识点11482": 0.0001659372342411483, "列方程解和差倍问题": 0.00041484308560287074, "列方程解决和倍问题\"（ax+bx=c）\"": 0.0002281636970815789, "解决基础和倍问题": 0.0002281636970815789, "补充知识点11441": 0.0002281636970815789, "补充知识点11442": 0.0002281636970815789, "补充知识点11443": 0.0002281636970815789, "列方程解决实际问题ax=b(a≠0)": 0.00035261662276244013, "看图列方程": 0.00035261662276244013, "补充知识点11312": 0.00035261662276244013, "补充知识点11313": 0.00035261662276244013, "补充知识点11314": 0.00035261662276244013, "等式与方程": 4.1484308560287074e-05, "方程的判断": 4.1484308560287074e-05, "补充知识点11633": 4.1484308560287074e-05, "补充知识点11634": 4.1484308560287074e-05, "补充知识点11635": 4.1484308560287074e-05, "应用等式的性质1和2解方程": 0.0005185538570035884, "解方程：ax±b=c这种类型(a≠0)": 0.0005185538570035884, "补充知识点11123": 0.0005185538570035884, "补充知识点11124": 0.0005185538570035884, "等式的性质2": 0.0001659372342411483, "根据等式的性质2的补全式子": 8.296861712057415e-05, "补充知识点11031": 8.296861712057415e-05, "补充知识点11032": 8.296861712057415e-05, "用字母表示数量关系(ax±bx)": 0.00035261662276244013, "用字母表示两积之和问题中的量": 0.00035261662276244013, "补充知识点10646": 0.00035261662276244013, "补充知识点10647": 0.00035261662276244013, "用字母表示运算定律及计算公式": 0.000850428325485885, "含字母式子的计算9": 0.0003318744684822966, "比较含字母等式中字母的大小-": 0.0003318744684822966, "补充知识点10820": 0.0003318744684822966, "补充知识点10821": 0.0003318744684822966, "用字母表示数量关系(a±bx)或(ax±b)": 0.001078592022567464, "用字母表示乘减的数量关系": 0.001078592022567464, "补充知识点10658": 0.001078592022567464, "补充知识点10659": 0.001078592022567464, "用字母表示工效、工时、工总间的关系": 2.0742154280143537e-05, "用字母表示工作总量": 2.0742154280143537e-05, "补充知识点10636": 2.0742154280143537e-05, "补充知识点10637": 2.0742154280143537e-05, "用字母表示数量关系a±x或x±a": 0.0007052332455248803, "用字母表示与减法相关的问题": 0.0007052332455248803, "补充知识点10718": 0.0007052332455248803, "补充知识点10719": 0.0007052332455248803, "用字母表示数量关系ax或x÷a或a÷x": 0.0007259753998050237, "用文字表述含字母的算式": 0.0007259753998050237, "补充知识点10686": 0.0007259753998050237, "补充知识点10687": 0.0007259753998050237, "含有字母式子的化简与求值": 0.0007052332455248803, "化简含字母的式子": 0.0007052332455248803, "改写含字母的式子-": 0.0007052332455248803, "补充知识点10844": 0.0007052332455248803, "补充知识点10845": 0.0007052332455248803, "补充知识点10846": 0.0007052332455248803, "用字母表示计算公式": 0.0004770695484433013, "用字母表示计算公式的应用": 0.0004770695484433013, "补充知识点10800": 0.0004770695484433013, "补充知识点10801": 0.0004770695484433013, "方程的意义": 0.0006844910912447367, "方程的认识": 0.00031113231420215305, "根据方程的定义判断": 0.00031113231420215305, "补充知识点10967": 0.00031113231420215305, "补充知识点10968": 0.00031113231420215305, "与小数点移动相关的和差倍问题": 4.1484308560287074e-05, "小数点向左移动与小数的大小变化(小数除法)": 4.1484308560287074e-05, "根据计算直接写出得数": 4.1484308560287074e-05, "补充知识点7550": 4.1484308560287074e-05, "商的变化规律在小数除法中的应用": 2.0742154280143537e-05, "判断商是否相等": 2.0742154280143537e-05, "补充知识点10344": 2.0742154280143537e-05, "补充知识点10345": 2.0742154280143537e-05, "补充知识点10346": 2.0742154280143537e-05, "小数乘加、乘减混合运算": 6.222646284043061e-05, "补充知识点7708": 6.222646284043061e-05, "补充知识点7709": 6.222646284043061e-05, "有关时间推算的锯木头问题": 4.1484308560287074e-05, "已知锯木头次数求段数": 4.1484308560287074e-05, "补充知识点15147": 4.1484308560287074e-05, "利用整体的几分之几解决问题（有图或求几分之几）": 0.00041484308560287074, "生活中简单的几分之几(整体是群体)": 0.00041484308560287074, "补充知识点8157": 0.00041484308560287074, "根据分数大小比较解决实际问题": 0.00014519507996100476, "利用比较分数大小解决问题": 0.00014519507996100476, "补充知识点2634": 0.00014519507996100476, "1减几分之几的应用": 0.0003318744684822966, "补充知识点7877": 0.0001659372342411483, "认识整体的几分之几（整体是群体）": 0.0003318744684822966, "看图写分数": 0.0003318744684822966, "补充知识点2540": 0.0003318744684822966, "含多边形的组合图形的面积": 0.0008711704797660285, "拼接后求周长": 0.0006637489369645932, "2个相同长、正方形拼后求周长": 0.0006637489369645932, "补充知识点17668": 0.0006637489369645932, "补充知识点17669": 0.0006637489369645932, "补充知识点17670": 0.0006637489369645932, "剪后求周长": 0.0002281636970815789, "长、正方形剪后计算周长的应用": 0.0002281636970815789, "补充知识点25511": 0.0002281636970815789, "补充知识点25512": 0.0002281636970815789, "两位数乘一位数(不进位)的笔算": 0.00010371077140071768, "两位数乘一位数的笔算": 0.00010371077140071768, "补充知识点6058": 0.00010371077140071768, "三位数乘一位数(连续进位)的笔算": 0.00012445292568086122, "三位数乘一位数(连续进位)的笔算算理": 0.00012445292568086122, "补充知识点6102": 0.00012445292568086122, "有趣的乘法计算": 0.00018667938852129183, "回文算式的积": 0.00018667938852129183, "补充知识点23542": 0.00018667938852129183, "补充知识点23543": 0.00018667938852129183, "补充知识点23544": 0.00018667938852129183, "照样子写计算过程": 6.222646284043061e-05, "照样子写出计算过程（两乘数相同）": 6.222646284043061e-05, "补充知识点23557": 6.222646284043061e-05, "补充知识点23558": 6.222646284043061e-05, "补充知识点23559": 6.222646284043061e-05, "三位数除以一位数的有余数除法": 0.0003318744684822966, "周期问题（三位数除以一位数）": 2.0742154280143537e-05, "已知总数，求某图形的个数": 2.0742154280143537e-05, "补充知识点5974": 2.0742154280143537e-05, "解决有关面积的问题(小数点移动规律)": 0.0006637489369645932, "已知变化后的面积求原来的面积": 0.0006637489369645932, "补充知识点2254": 0.0006637489369645932, "补充知识点2255": 0.0006637489369645932, "解决文字叙述问题(小数点移动规律)": 0.00170085665097177, "根据商的变化规律求商": 0.00170085665097177, "补充知识点2228": 0.00170085665097177, "补充知识点2229": 0.00170085665097177, "运用加法运算定律解决实际问题": 0.0014519507996100475, "解决求一共的实际问题": 0.0014519507996100475, "补充知识点9445": 0.0014519507996100475, "补充知识点9446": 0.0014519507996100475, "组合图形数小正方体数量的规律": 0.00014519507996100476, "组合图形小正方体数量的规律(加法或乘法)": 0.00014519507996100476, "补充知识点25378": 0.00014519507996100476, "补充知识点25379": 0.00014519507996100476, "补充知识点25380": 0.00014519507996100476, "海拔高度的表示": 0.0006637489369645932, "用正负数记录海拔高度（直接）": 0.0006637489369645932, "补充知识点3477": 0.0006637489369645932, "补充知识点3478": 0.0006637489369645932, "数的认识及分类（含负数）-": 0.0004978117027234449, "按要求给数分类（含负数）-": 0.0004978117027234449, "补充知识点3421": 0.0004978117027234449, "补充知识点3422": 0.0004978117027234449, "十几减5、4、3、2的实际问题": 0.0004770695484433013, "解决十几减5、4、3、2较复杂的实际问题": 0.0004770695484433013, "补充知识点4237": 0.0004770695484433013, "十几减9的退位减法": 0.0011200763311277509, "破十法(十几减9)": 4.1484308560287074e-05, "用破十法先圈再计算": 0.00010371077140071768, "补充知识点4119": 4.1484308560287074e-05, "解比例在平面图形中的应用-": 0.00010371077140071768, "求直角梯形面积（直角梯形变为正方形）": 0.00010371077140071768, "补充知识点12351": 0.00010371077140071768, "补充知识点12352": 0.00010371077140071768, "削最大正方体的体积问题-": 0.00035261662276244013, "求正方体的体积（长削正）": 0.00035261662276244013, "补充知识点20135": 0.00035261662276244013, "补充知识点20136": 0.00035261662276244013, "倒数与比例-": 0.0004978117027234449, "求内项值（知外项互为倒数、某一个内项）-": 0.0004978117027234449, "补充知识点12279": 0.0004978117027234449, "补充知识点12280": 0.0004978117027234449, "圆柱高变化与表面积问题-": 0.00037335877704258366, "求剩余圆柱体表面积（知原高+截取长度+减少表面积）": 0.00037335877704258366, "补充知识点19016": 0.00037335877704258366, "补充知识点19017": 0.00037335877704258366, "瓶中水/沙（含圆锥的正倒放）-": 0.00018667938852129183, "求水面高度（圆柱与圆锥组合容器的正倒放）": 0.00018667938852129183, "补充知识点19744": 0.00018667938852129183, "补充知识点19745": 0.00018667938852129183, "削成最大的圆柱": 0.00031113231420215305, "求所削最大圆柱体积（知正方体的棱长）": 0.00031113231420215305, "补充知识点20237": 0.00031113231420215305, "补充知识点20238": 0.00031113231420215305, "圆柱体切拼成近似长方体的体积": 0.0003318744684822966, "求圆柱体积（知圆柱半径+增加的表面积）": 0.0003318744684822966, "补充知识点20251": 0.0003318744684822966, "补充知识点20252": 0.0003318744684822966, "体积单位间的进率与换算（立方厘米、立方分米和立方米）": 0.0011823027939681814, "含体积单位的计算": 4.1484308560287074e-05, "含体积单位的计算(相同单名数)": 4.1484308560287074e-05, "补充知识点21991": 4.1484308560287074e-05, "几个小部分拼接成大长方体": 2.0742154280143537e-05, "每部分数量相同，根据看到的大长方体，选出其中一部分几何体": 2.0742154280143537e-05, "补充知识点18129": 2.0742154280143537e-05, "解决有关5的倍数的实际问题": 0.0003318744684822966, "解决付费是否正确的实际问题": 0.0003318744684822966, "补充知识点1134": 0.0003318744684822966, "比例尺有关的路线图问题-": 4.1484308560287074e-05, "计算实际距离或图上距离": 4.1484308560287074e-05, "补充知识点12906": 4.1484308560287074e-05, "补充知识点12907": 4.1484308560287074e-05, "求图形放大/缩小后的量（边长/周长/面积/体积）-": 0.0002903901599220095, "求变化后图形的边长（知比+原来边长）": 0.0002903901599220095, "补充知识点12842": 0.0002903901599220095, "补充知识点12843": 0.0002903901599220095, "用比例基本性质求值（解比例）-": 0.0006430067826844496, "借助比例基本性质求图中面积（有图）-": 0.0006430067826844496, "补充知识点12309": 0.0006430067826844496, "补充知识点12310": 0.0006430067826844496, "等底等高的圆柱和圆锥-": 0.0009748812511667462, "求体积间关系": 0.0009748812511667462, "补充知识点19274": 0.0009748812511667462, "补充知识点19275": 0.0009748812511667462, "认识计数单位": 0.000269648005641866, "计数单位一（个）、十、百之间的关系": 0.000269648005641866, "补充知识点569": 0.000269648005641866, "不含括号的表内混合运算": 0.0003318744684822966, "判断不含括号的混合运算顺序": 0.0003318744684822966, "补充知识点6982": 0.0003318744684822966, "无括号的运算顺序": 0.0010163655597270331, "含有两级运算的运算顺序": 0.0002903901599220095, "判断两级运算的运算顺序": 0.0002903901599220095, "补充知识点9277": 0.0002903901599220095, "补充知识点9278": 0.0002903901599220095, "圆锥体积公式的逆用": 0.000580780319844019, "求圆锥高（知底面半径+体积）": 0.000580780319844019, "补充知识点19360": 0.000580780319844019, "补充知识点19361": 0.000580780319844019, "由体积和/差，求等底等高圆柱、圆锥体积": 0.0009956234054468898, "求圆锥、圆锥体积（知体积差）(隐含条件)": 0.0009956234054468898, "补充知识点19306": 0.0009956234054468898, "补充知识点19307": 0.0009956234054468898, "圆柱侧面积的变化规律": 0.0001659372342411483, "求侧面积变化倍数（知直径/半径、高的变化倍数）": 0.0001659372342411483, "补充知识点18968": 0.0001659372342411483, "补充知识点18969": 0.0001659372342411483, "等底等体积的圆柱与圆锥-": 0.0003941009313227272, "求高间关系/高之比": 0.0003941009313227272, "补充知识点19270": 0.0003941009313227272, "补充知识点19271": 0.0003941009313227272, "圆锥的表面积-": 0.00018667938852129183, "竖切圆锥表面积的变化-": 0.0001659372342411483, "求圆锥高（知增加表面积+底面量）": 0.0001659372342411483, "补充知识点18822": 0.0001659372342411483, "补充知识点18823": 0.0001659372342411483, "小数的意义(两位及以上小数)": 0.0011200763311277509, "认识小数的意义": 0.0011200763311277509, "补充知识点1730": 0.0011200763311277509, "补充知识点1731": 0.0011200763311277509, "以万\"作单位的改写与取近似数(小数)\"": 0.0013482400282093297, "以万\"作单位的改写并取近似数\"": 0.0013482400282093297, "补充知识点2439": 0.0013482400282093297, "低级单位换算高级单位": 0.0010993341768476073, "低级单位换算成高级单位的方法": 0.0010993341768476073, "补充知识点1798": 0.0010993341768476073, "补充知识点1799": 0.0010993341768476073, "除法的意义": 0.0008711704797660285, "理解除法的意义": 0.0008711704797660285, "补充知识点9149": 0.0008711704797660285, "补充知识点9150": 0.0008711704797660285, "加小括号或中括号改变运算顺序问题": 0.0009333969426064591, "按指定运算顺序添加括号": 0.0009333969426064591, "补充知识点9355": 0.0009333969426064591, "补充知识点9356": 0.0009333969426064591, "锐角、直角、钝角、平角和周角之间的大小关系": 0.00024890585136172244, "角之间的大小关系": 0.00024890585136172244, "补充知识点15863": 0.00024890585136172244, "补充知识点15864": 0.00024890585136172244, "轴对称的剪纸问题": 0.0014312086453299039, "如何得到剪纸图案": 0.0009748812511667462, "对折三次，剪纸，找展开图": 0.0009748812511667462, "补充知识点20446": 0.0009748812511667462, "补充知识点20447": 0.0009748812511667462, "补充知识点20448": 0.0009748812511667462, "除法的计算与运用(7、8、9)（移后删）": 0.00041484308560287074, "图文算式": 0.00041484308560287074, "补充知识点5665": 0.00041484308560287074, "用乘法口诀求商(2-6)": 0.0016386301881313394, "想乘法口诀求商": 0.0016386301881313394, "补充知识点5621": 0.0016386301881313394, "理解平均分（一）（移后删）": 0.0012445292568086122, "添/去/移后使其平均分": 0.0012445292568086122, "补充知识点5523": 0.0012445292568086122, "积末尾0的个数（口算）": 0.0004978117027234449, "知乘数末尾0的个数，判断积末尾0的个数": 0.0004978117027234449, "补充知识点7104": 0.0004978117027234449, "一般应用题": 0.00024890585136172244, "解决分段计费问题": 8.296861712057415e-05, "解决时间间隔的相关应用": 8.296861712057415e-05, "补充知识点25701": 8.296861712057415e-05, "补充知识点25702": 8.296861712057415e-05, "补充知识点25703": 8.296861712057415e-05, "先补充条件，再解答": 0.00010371077140071768, "根据题意补充条件": 0.00010371077140071768, "补充知识点13857": 0.00010371077140071768, "补充知识点13858": 0.00010371077140071768, "乘法结合律": 0.0007052332455248803, "用图表示乘法结合律": 0.0007052332455248803, "补充知识点9555": 0.0007052332455248803, "补充知识点9556": 0.0007052332455248803, "列方程解含两个未知数的问题": 0.0002903901599220095, "解含有两个未知数的方程": 0.0002903901599220095, "写等量关系并列方程-": 0.0002903901599220095, "补充知识点11600": 0.0002903901599220095, "补充知识点11601": 0.0002903901599220095, "补充知识点11602": 0.0002903901599220095, "根据条件推理比例": 0.00024890585136172244, "写比例（知外项和、差及比值）-": 0.00024890585136172244, "补充知识点12265": 0.00024890585136172244, "补充知识点12266": 0.00024890585136172244, "认识几分之一": 0.00018667938852129183, "根据图示判断几分之一": 0.00018667938852129183, "补充知识点2495": 0.00018667938852129183, "小数的化简": 0.000891912634046172, "小数化简的方法": 0.000891912634046172, "补充知识点2028": 0.000891912634046172, "补充知识点2029": 0.000891912634046172, "四个方向描述简单的行走路线": 0.0004978117027234449, "根据去的路线推理返回路线": 0.0004978117027234449, "补充知识点21178": 0.0004978117027234449, "补充知识点21179": 0.0004978117027234449, "正方体棱长和": 0.0008089440169255979, "知一个面的面积，求棱长和-": 0.0008089440169255979, "补充知识点18409": 0.0008089440169255979, "补充知识点18410": 0.0008089440169255979, "正方体棱长变化的运用": 0.0006637489369645932, "由棱长变化倍数，求棱长总和、表面积、体积变化倍数-": 0.0006637489369645932, "补充知识点18642": 0.0006637489369645932, "补充知识点18643": 0.0006637489369645932, "解决总价问题(表内乘法)": 0.00018667938852129183, "购物求总价问题": 0.00018667938852129183, "补充知识点14285": 0.00018667938852129183, "补充知识点14286": 0.00018667938852129183, "同级运算的运算顺序": 0.00031113231420215305, "判断同级运算的运算顺序": 0.00031113231420215305, "补充知识点9289": 0.00031113231420215305, "补充知识点9290": 0.00031113231420215305, "长方体和正方体棱长综合-": 0.0006844910912447367, "长方体中切最大正方体，求正方体棱长-": 0.0006844910912447367, "补充知识点18238": 0.0006844910912447367, "补充知识点18239": 0.0006844910912447367, "长方体、正方体表面积综合（考法需整合）": 0.000269648005641866, "已知增加的高和表面积，求原长方体的表面积-": 0.000269648005641866, "补充知识点18248": 0.000269648005641866, "补充知识点18249": 0.000269648005641866, "体积单位的认识": 0.0016386301881313394, "理解长度，面积，体积-": 0.0016386301881313394, "补充知识点21975": 0.0016386301881313394, "整数乘法交换律": 0.0004770695484433013, "乘法交换律": 0.0004770695484433013, "根据乘法交换律填空": 0.0004770695484433013, "补充知识点9517": 0.0004770695484433013, "补充知识点9518": 0.0004770695484433013, "体积单位间的进率和换算": 0.0011408184854078945, "体积单位间关系推理-": 0.0011408184854078945, "补充知识点21994": 0.0011408184854078945, "田忌赛马问题": 0.0003318744684822966, "田忌赛马": 0.0003318744684822966, "球类比赛": 0.0003318744684822966, "补充知识点14971": 0.0003318744684822966, "烙饼问题": 0.001161560639688038, "单数张": 0.000580780319844019, "补充知识点14967": 0.000580780319844019, "从三个方向看组合体确定小正方体数量": 0.0008711704797660285, "从三个方向看形状相同确定所需最多（少）数量": 0.0008711704797660285, "补充知识点25306": 0.0008711704797660285, "补充知识点25307": 0.0008711704797660285, "补充知识点25308": 0.0008711704797660285, "解决有关3、5的倍数的实际问题": 0.00018667938852129183, "解决平均分问题": 0.0002903901599220095, "补充知识点1218": 0.00018667938852129183, "从两个方向看用小正方体摆出相应组合体": 0.0006222646284043061, "由两个面看到的平面图，确定第三个面看到的平面图-": 0.0006222646284043061, "补充知识点18074": 0.0006222646284043061, "2、5的倍数的辦析": 0.0010578498682873203, "补充知识点1090": 0.0010578498682873203, "差的奇偶性": 0.0001659372342411483, "差的奇偶性判断（两个数）": 0.0001659372342411483, "补充知识点1317": 0.0001659372342411483, "4的倍数特征": 0.00037335877704258366, "4的倍数特征的实际问题": 0.00018667938852129183, "补充知识点1609": 0.00018667938852129183, "圆锥体积的变化-": 0.00014519507996100476, "由半径/直径和高的变化，求体积变化": 0.00014519507996100476, "补充知识点19368": 0.00014519507996100476, "补充知识点19369": 0.00014519507996100476, "长正方体挖洞的表面积问题-": 0.0001659372342411483, "求挖洞后立体图形表面积（正方体中挖正方体1次）-": 0.0001659372342411483, "补充知识点20101": 0.0001659372342411483, "补充知识点20102": 0.0001659372342411483, "由涂色情况反求体积-": 0.0001659372342411483, "由两面涂色小正方体数量，求正方体体积(或表面积)": 0.0001659372342411483, "补充知识点18756": 0.0001659372342411483, "补充知识点18757": 0.0001659372342411483, "浓度问题": 0.00020742154280143537, "根据浓度和溶液求溶质": 0.00012445292568086122, "补充知识点26149": 6.222646284043061e-05, "补充知识点26150": 6.222646284043061e-05, "补充知识点26151": 6.222646284043061e-05, "两、三位数乘一位数的实际应用": 0.0013482400282093297, "两位数乘一位数的口算的实际问题": 0.0006637489369645932, "方阵问题": 0.0006637489369645932, "补充知识点6123": 0.0006637489369645932, "几百几十数乘一位数的口算的实际问题": 0.0005185538570035884, "补充知识点6117": 0.0005185538570035884, "解决三位数(乘数中间有0)乘一位数的实际问题": 0.0001659372342411483, "三位数中间有0的乘法应用": 0.0001659372342411483, "补充知识点6133": 0.0001659372342411483, "容积的运用": 0.000269648005641866, "根据长方体的展开图求容积": 0.000269648005641866, "补充知识点21961": 0.000269648005641866, "油桶问题（除数是一位数的除法）": 0.0001659372342411483, "有等分的油桶问题": 0.0001659372342411483, "补充知识点25641": 0.0001659372342411483, "补充知识点25642": 0.0001659372342411483, "补充知识点25643": 0.0001659372342411483, "正方体底面积": 0.00018667938852129183, "补充知识点18529": 0.00018667938852129183, "运用对称解决剪纸问题": 0.00018667938852129183, "对折次数与剪成整个图形数量之间的关系": 0.00018667938852129183, "补充知识点20425": 0.00018667938852129183, "补充知识点20426": 0.00018667938852129183, "补充知识点20427": 0.00018667938852129183, "根据平移的方向和距离解决问题": 0.00035261662276244013, "确定平移的方向(和距离)": 0.00035261662276244013, "补充知识点20464": 0.00035261662276244013, "补充知识点20465": 0.00035261662276244013, "补充知识点20466": 0.00035261662276244013, "生活中的平移": 0.00020742154280143537, "汉字、字母、甲骨文等的左右平移重合": 0.00020742154280143537, "补充知识点20521": 0.00020742154280143537, "补充知识点20522": 0.00020742154280143537, "补充知识点20523": 0.00020742154280143537, "数100以内的数": 0.00024890585136172244, "一个数添（少）几是另一个数": 0.00012445292568086122, "再添多少就是几十": 0.00012445292568086122, "补充知识点438": 0.00012445292568086122, "再添多少就是100": 8.296861712057415e-05, "看图添够100": 8.296861712057415e-05, "补充知识点432": 8.296861712057415e-05, "求成数": 0.000539296011283732, "看图求成数-": 0.000539296011283732, "补充知识点8759": 0.000539296011283732, "分段计算解决纳税问题": 0.0002903901599220095, "求税后额": 0.00018667938852129183, "税后收入（总收入+起征点+每段税率已知）": 0.00018667938852129183, "补充知识点8720": 0.00018667938852129183, "求原来的量-": 0.00035261662276244013, "知现在量和成数，求原来的量-": 0.00035261662276244013, "补充知识点8770": 0.00035261662276244013, "选择储蓄的最佳方案": 0.0002903901599220095, "复利问题计算": 0.0002903901599220095, "复利计算": 0.0002903901599220095, "补充知识点8753": 0.0002903901599220095, "利润和折扣问题": 0.0005185538570035884, "利润和折扣综合求成本": 0.0002903901599220095, "根据按不同折扣出售的盈亏情况，求进价-": 0.0002903901599220095, "补充知识点26100": 0.0002903901599220095, "补充知识点26101": 0.0002903901599220095, "补充知识点26102": 0.0002903901599220095, "浓度问题中的蒸发问题": 2.0742154280143537e-05, "求蒸发的溶剂": 2.0742154280143537e-05, "补充知识点26209": 2.0742154280143537e-05, "补充知识点26210": 2.0742154280143537e-05, "补充知识点26211": 2.0742154280143537e-05, "组合体的体积（圆柱、圆锥）": 0.0005600381655638754, "含圆锥的组合体的体积（叠放型）": 0.0002281636970815789, "圆锥与圆柱叠放-": 0.0002281636970815789, "补充知识点19550": 0.0002281636970815789, "补充知识点19551": 0.0002281636970815789, "真、假、带分数的综合运用": 0.0014934351081703347, "由所给分子/分母值，写最大/最小分数-": 0.0014934351081703347, "补充知识点2787": 0.0014934351081703347, "补充知识点2788": 0.0014934351081703347, "加法结合律": 0.0006844910912447367, "根据加法结合律填空": 0.0006844910912447367, "补充知识点9433": 0.0006844910912447367, "补充知识点9434": 0.0006844910912447367, "竖切圆柱体积的计算": 0.00024890585136172244, "求圆柱体积（知增加面积+圆柱高）": 0.00024890585136172244, "补充知识点20221": 0.00024890585136172244, "补充知识点20222": 0.00024890585136172244, "列方程解决稍复杂的相遇问题\"\"": 0.00012445292568086122, "列方程解决相反方向出发的问题": 0.00012445292568086122, "补充知识点11522": 0.00012445292568086122, "补充知识点11523": 0.00012445292568086122, "补充知识点11524": 0.00012445292568086122, "时区时差中的负数": 0.00020742154280143537, "用正负数表示时差": 0.00020742154280143537, "补充知识点3495": 0.00020742154280143537, "补充知识点3496": 0.00020742154280143537, "长度/面积/体积/容积中的负数": 2.0742154280143537e-05, "根据范围判断是否合格": 2.0742154280143537e-05, "补充知识点3553": 2.0742154280143537e-05, "补充知识点3554": 2.0742154280143537e-05, "利用正负数解决实际问题": 0.0009126547883263156, "用正负数解决其它实际问题-": 0.00041484308560287074, "消费相关的实际问题-": 0.00041484308560287074, "补充知识点10048": 0.00041484308560287074, "补充知识点10049": 0.00041484308560287074, "补充知识点10050": 0.00041484308560287074, "解决与税率有关的实际问题": 0.00024890585136172244, "税率有关的其它实际问题": 0.00024890585136172244, "求实际到账额（服务费，费率已知）（提现...）": 0.00024890585136172244, "补充知识点8690": 0.00024890585136172244, "两位数除以一位数的口算除法的实际问题": 0.0008711704797660285, "移多补少问题": 0.0008711704797660285, "补充知识点6276": 0.0008711704797660285, "偶数的认识（一）": 0.0007674597083653108, "已知多个偶数的和，求其中的偶数-": 0.0007674597083653108, "补充知识点1240": 0.0007674597083653108, "三位数除以一位数的笔算的实际问题（被除数首位能整除）": 0.0003318744684822966, "方案的选择": 0.0003318744684822966, "补充知识点6351": 0.0003318744684822966, "三位数除以一位数(最高位不能整除)的笔算的应用": 0.0002903901599220095, "算式中某一部分表示的含义": 0.0002903901599220095, "补充知识点6358": 0.0002903901599220095, "认识折线统计图及其特点": 0.0012860135653688992, "根据文字描述选择合适的折线统计图": 0.0012860135653688992, "补充知识点22416": 0.0012860135653688992, "补充知识点22417": 0.0012860135653688992, "几百几十（几千几百）数除以一位数的口算运用": 0.00024890585136172244, "补充知识点6221": 0.00024890585136172244, "找规律(表内乘法)": 4.1484308560287074e-05, "找规律(双重)": 4.1484308560287074e-05, "补充知识点23238": 4.1484308560287074e-05, "补充知识点23239": 4.1484308560287074e-05, "补充知识点23240": 4.1484308560287074e-05, "根据规律判断第几个图形是什么": 4.1484308560287074e-05, "根据一般规律判断图形": 4.1484308560287074e-05, "补充知识点23360": 4.1484308560287074e-05, "补充知识点23361": 4.1484308560287074e-05, "补充知识点23362": 4.1484308560287074e-05, "定义新运算": 0.0002903901599220095, "根据新运算，代入数求值-": 0.00014519507996100476, "补充知识点24082": 0.00014519507996100476, "补充知识点24083": 0.00014519507996100476, "补充知识点24084": 0.00014519507996100476, "两位数乘一位数(连续进位)的笔算": 2.0742154280143537e-05, "两位数乘一位数(连续进位)的算理": 2.0742154280143537e-05, "补充知识点6097": 2.0742154280143537e-05, "竖切圆柱体的表面积": 0.00041484308560287074, "求原来圆柱侧面积（知高+段数+增加表面积）": 0.00041484308560287074, "补充知识点20201": 0.00041484308560287074, "补充知识点20202": 0.00041484308560287074, "公因数与最大公因数的运用-": 0.0002281636970815789, "已知最大公因数，求分解质因数中的字母-": 0.0002281636970815789, "补充知识点1471": 0.0002281636970815789, "求两个数的最小公倍数的特殊情况": 0.001161560639688038, "成倍数关系-": 0.001161560639688038, "补充知识点1524": 0.001161560639688038, "和公因数、公倍数有关的新定义问题": 2.0742154280143537e-05, "已知最大公因数和最小公倍数的和，求其中一个数": 2.0742154280143537e-05, "补充知识点1535": 2.0742154280143537e-05, "推理法求带分数-": 0.00018667938852129183, "求带分数（知带分数和所化假分数的分子）-": 0.00018667938852129183, "补充知识点2897": 0.00018667938852129183, "补充知识点2898": 0.00018667938852129183, "其他倍数特征": 0.0003318744684822966, "7的倍数特征": 0.0001659372342411483, "补充知识点1601": 0.0001659372342411483, "因、倍、奇、偶数综合运用": 0.0007467175540851673, "按要求找数（范围已给）": 0.0007467175540851673, "补充知识点1286": 0.0007467175540851673, "从不同位置观察同一几何组合体": 0.0024268320507767937, "从某个方向观察多个用正方体搭成的组合体，最多/少能看到几个正方体": 0.0024268320507767937, "补充知识点17990": 0.0024268320507767937, "补充知识点17991": 0.0024268320507767937, "运用分数的基本性质进行改写": 0.0005600381655638754, "改写成分子相同的分数": 0.0005600381655638754, "补充知识点2987": 0.0005600381655638754, "补充知识点2988": 0.0005600381655638754, "假分数的意义和特征": 0.000580780319844019, "判断假分数": 0.000580780319844019, "补充知识点2671": 0.000580780319844019, "补充知识点2672": 0.000580780319844019, "求存期": 0.0001659372342411483, "已知本金，利息，利率，求存期": 0.0001659372342411483, "补充知识点8745": 0.0001659372342411483, "变化的量": 0.0002903901599220095, "反比例图象的认识": 0.0002903901599220095, "由反比例图象解决问题": 0.00024890585136172244, "补充知识点12411": 0.00024890585136172244, "分数与除法的转化": 0.0008296861712057415, "分数变为除法": 0.0008296861712057415, "补充知识点2578": 0.0008296861712057415, "长方体侧面积": 8.296861712057415e-05, "求侧面积（知底面积，侧面展开图形状）-": 8.296861712057415e-05, "补充知识点18490": 8.296861712057415e-05, "3、5倍数的运用": 0.0006222646284043061, "根据3，5倍数特征，确定三位数个数（三位数不全）-": 0.0006222646284043061, "补充知识点1193": 0.0006222646284043061, "探究面积": 8.296861712057415e-05, "求涂色的面积（规则立体图形）-": 8.296861712057415e-05, "补充知识点18752": 8.296861712057415e-05, "补充知识点18753": 8.296861712057415e-05, "一个数的因数是另一个数的倍数": 0.0001659372342411483, "根据所给因数和倍数条件，求数-": 0.0001659372342411483, "补充知识点1067": 0.0001659372342411483, "2、3倍数的运用": 0.0007052332455248803, "2、3的倍数的辨析": 0.0007052332455248803, "补充知识点1173": 0.0007052332455248803, "分解质因数": 0.00024890585136172244, "运用推理法解决质数积的问题": 2.0742154280143537e-05, "简单的分解": 2.0742154280143537e-05, "补充知识点1446": 2.0742154280143537e-05, "5的倍数特征": 0.0003318744684822966, "所给数中找出5的倍数-": 0.0003318744684822966, "补充知识点1111": 0.0003318744684822966, "运用推理法和尝试法求分数问题": 0.000269648005641866, "求真、假、带分数（分母相同）-": 0.000269648005641866, "补充知识点2771": 0.000269648005641866, "补充知识点2772": 0.000269648005641866, "积的奇偶性": 0.000580780319844019, "实际运用": 0.000580780319844019, "补充知识点1332": 0.000580780319844019, "解决多个数是同一个数的倍数的问题": 0.00018667938852129183, "多个数是同一个数的倍数的之和问题": 0.00018667938852129183, "补充知识点14113": 0.00018667938852129183, "补充知识点14114": 0.00018667938852129183, "运用质数的特征解决组合质数的问题": 0.0006222646284043061, "已知两质数的和与积，求这两数-": 0.0006222646284043061, "补充知识点1384": 0.0006222646284043061, "解决有关2、3的倍数的实际问题": 0.00010371077140071768, "补充知识点1204": 0.00010371077140071768, "圆柱的底面、侧面、高、截面": 0.0007052332455248803, "圆柱的截面": 0.0007052332455248803, "补充知识点18832": 0.0007052332455248803, "补充知识点18833": 0.0007052332455248803, "等高等体积的圆柱和圆锥-": 0.00014519507996100476, "求圆锥/柱底面积（知圆柱/圆锥底面积）": 0.00014519507996100476, "补充知识点19266": 0.00014519507996100476, "补充知识点19267": 0.00014519507996100476, "圆柱体积公式推导": 0.000539296011283732, "圆柱与所拼长方体间量的比较": 0.000539296011283732, "补充知识点19144": 0.000539296011283732, "补充知识点19145": 0.000539296011283732, "乘法的意义": 0.0010371077140071767, "求平面图形的面积": 0.0010371077140071767, "补充知识点9123": 0.0010371077140071767, "补充知识点9124": 0.0010371077140071767, "减法的意义": 0.0007882018626454544, "利用减法的意义解决画图题型": 0.0007882018626454544, "补充知识点9021": 0.0007882018626454544, "补充知识点9022": 0.0007882018626454544, "填算符(加减乘除2-6)（移题后删）": 0.00012445292568086122, "填算符(+、-、×、÷)": 0.00012445292568086122, "补充知识点24214": 0.00012445292568086122, "补充知识点24215": 0.00012445292568086122, "补充知识点24216": 0.00012445292568086122, "等分(按指定的份数平均分)": 0.000850428325485885, "按指定的份数平均分": 0.000850428325485885, "补充知识点5495": 0.000850428325485885, "乘除法的计算与应用(2-6)": 0.0011408184854078945, "运用乘法口诀填未知数": 0.0011408184854078945, "补充知识点5786": 0.0011408184854078945, "除法意义的理解与运用（移后删）": 0.0007259753998050237, "选择合适的条件并解决问题": 0.0007259753998050237, "补充知识点5537": 0.0007259753998050237, "除法计算与运用(2-6)": 0.0006637489369645932, "加减乘除四种运算的计算": 0.0006637489369645932, "补充知识点5610": 0.0006637489369645932, "不含括号的四则混合运算的运算顺序": 0.00041484308560287074, "四则运算概念": 0.00041484308560287074, "补充知识点9267": 0.00041484308560287074, "补充知识点9268": 0.00041484308560287074, "与最大/小因数、最大/小倍数有关的问题-": 0.00037335877704258366, "由最小倍数与最大因数，求这个数-": 0.00037335877704258366, "补充知识点1082": 0.00037335877704258366, "合数的运用9": 6.222646284043061e-05, "按要求找合数-": 6.222646284043061e-05, "补充知识点1348": 6.222646284043061e-05, "巧用加法运算定律和减法的运算性质": 0.00031113231420215305, "运用分组凑某个固定的数的简便计算": 0.00031113231420215305, "补充知识点9827": 0.00031113231420215305, "补充知识点9828": 0.00031113231420215305, "十几减9的实际问题": 0.000580780319844019, "根据题意列式计算": 0.000580780319844019, "补充知识点4127": 0.000580780319844019, "分类图形计数": 2.0742154280143537e-05, "图形的分类与计数": 2.0742154280143537e-05, "补充知识点15417": 2.0742154280143537e-05, "补充知识点15418": 2.0742154280143537e-05, "和与差的变化规律": 2.0742154280143537e-05, "差相等的减法算式": 2.0742154280143537e-05, "计算(破十法、平十法)": 2.0742154280143537e-05, "补充知识点10128": 2.0742154280143537e-05, "补充知识点10129": 2.0742154280143537e-05, "补充知识点10130": 2.0742154280143537e-05, "看图列减法算式(整体与部分)": 4.1484308560287074e-05, "看图列式(十几减6、7、8)": 4.1484308560287074e-05, "补充知识点13933": 4.1484308560287074e-05, "补充知识点13934": 4.1484308560287074e-05, "根据规律补全图形或数": 2.0742154280143537e-05, "图形成组重复规律": 2.0742154280143537e-05, "补充知识点23348": 2.0742154280143537e-05, "补充知识点23349": 2.0742154280143537e-05, "补充知识点23350": 2.0742154280143537e-05, "枚举法数正方形": 4.1484308560287074e-05, "数含符号的正方形": 4.1484308560287074e-05, "补充知识点25267": 4.1484308560287074e-05, "补充知识点25268": 4.1484308560287074e-05, "补充知识点25269": 4.1484308560287074e-05, "拼一拼，比一比": 4.1484308560287074e-05, "求用多个小长方形拼成的不同图形周长": 4.1484308560287074e-05, "补充知识点16033": 4.1484308560287074e-05, "补充知识点16034": 4.1484308560287074e-05, "用除法解决问题": 0.0007467175540851673, "再增加/减少多少能够平均分": 0.0007467175540851673, "补充知识点5568": 0.0007467175540851673, "十几减7、6的计算": 8.296861712057415e-05, "找得数相等的算式": 8.296861712057415e-05, "补充知识点4194": 8.296861712057415e-05, "十几减8的计算": 0.00012445292568086122, "计算结果并连线（涂色）": 0.00012445292568086122, "补充知识点4172": 0.00012445292568086122, "十几减9的计算": 0.00012445292568086122, "补充知识点4141": 0.00012445292568086122, "利润和折扣综合求定价": 0.0002281636970815789, "多次优惠后知少卖的钱数，求定价-": 0.0002281636970815789, "补充知识点26124": 0.0002281636970815789, "补充知识点26125": 0.0002281636970815789, "补充知识点26126": 0.0002281636970815789, "解百分数方程": 0.000539296011283732, "含括号的百分数方程": 0.0001659372342411483, "补充知识点11247": 0.0001659372342411483, "补充知识点11248": 0.0001659372342411483, "加法的意义": 0.0007882018626454544, "利用加法的意义计算(文字描述)": 0.0007674597083653108, "补充知识点8979": 0.0007674597083653108, "补充知识点8980": 0.0007674597083653108, "质数与合数解决实际问题": 0.0002903901599220095, "质数合数的综合应用": 0.0002903901599220095, "补充知识点1427": 0.0002903901599220095, "运用加、减法的意义和各部分之间的关系解决实际问题": 0.0003941009313227272, "根据加、减法的意义解决实际问题(加减混合)": 0.0003941009313227272, "补充知识点8905": 0.0003941009313227272, "补充知识点8906": 0.0003941009313227272, "认识容量": 2.0742154280143537e-05, "通过图直接比较两个容器的容量": 2.0742154280143537e-05, "补充知识点21953": 2.0742154280143537e-05, "比一个数多/少百分之几的数是多少": 0.0005600381655638754, "解决商品提价、降价问题(百分数)": 0.00020742154280143537, "知商品提价/降价百分率的辨析-": 0.00020742154280143537, "补充知识点8564": 0.00020742154280143537, "补充知识点8565": 0.00020742154280143537, "增/减几成的数-": 0.0001659372342411483, "数量/质量/价格变化，由预期获利求定价（知进价+原来数量/质量/价格）": 0.0001659372342411483, "补充知识点8764": 0.0001659372342411483, "税率的认识和意义-": 0.0001659372342411483, "税率的意义-": 0.0001659372342411483, "纳税中相关计算公式-": 0.0001659372342411483, "补充知识点8730": 0.0001659372342411483, "百分数的其他问题": 0.0003318744684822966, "行程中的百分数问题": 0.00014519507996100476, "求距离（知两车速度比+相遇后各自所提百分数+一车到一车还差距离）": 0.00014519507996100476, "补充知识点8869": 0.00014519507996100476, "补充知识点8870": 0.00014519507996100476, "三角形与长/正方形的面积": 0.00010371077140071768, "边长已知的正方形拼一起，求与其有关的三角形问题": 0.00010371077140071768, "补充知识点17130": 0.00010371077140071768, "公式法求平均数": 0.00170085665097177, "补充知识点22492": 0.000850428325485885, "补充知识点22493": 0.000850428325485885, "读取复式条形统计图并回答问题": 0.0003318744684822966, "读取横向复式条形统计图并填空": 0.0003318744684822966, "补充知识点22368": 0.0003318744684822966, "补充知识点22369": 0.0003318744684822966, "在统计表中计算平均数": 0.000850428325485885, "根据统计表求平均数": 0.000850428325485885, "补充知识点22526": 0.000850428325485885, "补充知识点22527": 0.000850428325485885, "认识平均数": 0.00020742154280143537, "个体与平均数之间的关系": 0.00020742154280143537, "补充知识点22504": 0.00020742154280143537, "补充知识点22505": 0.00020742154280143537, "增加去掉一个数后的平均数": 0.000269648005641866, "根据平均数的变化求去掉的数": 0.000269648005641866, "补充知识点25923": 0.000269648005641866, "补充知识点25924": 0.000269648005641866, "补充知识点25925": 0.000269648005641866, "用平均数比较数据的总体情况": 0.0001659372342411483, "根据平均数判断哪个队成绩更好": 0.0001659372342411483, "补充知识点22562": 0.0001659372342411483, "补充知识点22563": 0.0001659372342411483, "移多补少求平均数": 0.00010371077140071768, "用移多补少求与平均数的差距": 0.00010371077140071768, "补充知识点22514": 0.00010371077140071768, "补充知识点22515": 0.00010371077140071768, "用平移解决周长问题": 0.0003318744684822966, "先补全图形，再求周长": 0.0003318744684822966, "补充知识点17782": 0.0003318744684822966, "补充知识点17783": 0.0003318744684822966, "补充知识点17784": 0.0003318744684822966, "根据公式求总价": 0.00024890585136172244, "补充知识点14195": 0.00012445292568086122, "补充知识点14196": 0.00012445292568086122, "先打折后提价-": 0.00010371077140071768, "现价与原价比较": 0.00010371077140071768, "补充知识点8653": 0.00010371077140071768, "直尺测量(毫米、厘米、分米)": 4.1484308560287074e-05, "补充知识点21606": 2.0742154280143537e-05, "补充知识点21607": 2.0742154280143537e-05, "小数减法数字谜": 0.00035261662276244013, "不含退位的小数减法数字谜": 0.00035261662276244013, "补充知识点24760": 0.00035261662276244013, "补充知识点24761": 0.00035261662276244013, "补充知识点24762": 0.00035261662276244013, "小数加减法的运用": 0.0007259753998050237, "小数点移动后，求所得数与原数的变化情况": 0.0007259753998050237, "补充知识点7213": 0.0007259753998050237, "认识复式折线统计图及其特点": 0.0005600381655638754, "统计图的综合辨析-": 0.0005600381655638754, "补充知识点22448": 0.0005600381655638754, "补充知识点22449": 0.0005600381655638754, "绘制复式折线统计图": 0.00041484308560287074, "找出正确的复式折线统计图": 0.00041484308560287074, "补充知识点22438": 0.00041484308560287074, "补充知识点22439": 0.00041484308560287074, "找次品的方法-": 0.00031113231420215305, "找次品的最少称量次数分析-": 0.00031113231420215305, "补充知识点15223": 0.00031113231420215305, "补充知识点15224": 0.00031113231420215305, "已知次品轻/重找次品（多个次品）": 0.00018667938852129183, "由称量过程和结果判断次品的号数-": 0.00018667938852129183, "补充知识点15231": 0.00018667938852129183, "补充知识点15232": 0.00018667938852129183, "异分母分数加法在实际生活的应用-": 0.0007259753998050237, "异分母分数加法的应用（结果需比较）": 0.0007259753998050237, "补充知识点7972": 0.0007259753998050237, "同分母分数连减计算": 0.00024890585136172244, "同分母分数连减的计算(含假分数)": 0.00024890585136172244, "补充知识点7987": 0.00024890585136172244, "分子是1的异分母分数减法的简便算法": 0.00010371077140071768, "分母是互质数的两分数的减法计算": 0.00010371077140071768, "补充知识点9773": 0.00010371077140071768, "补充知识点9774": 0.00010371077140071768, "分数加减中的错中求解-": 8.296861712057415e-05, "补充知识点7931": 8.296861712057415e-05, "求最大公因数、最小公倍数-": 0.002530542822177511, "知两数质因数-": 0.002530542822177511, "补充知识点1536": 0.002530542822177511, "分数与小数互化（计算）-": 0.0015556615710107652, "分数、小数互化-": 0.0015556615710107652, "补充知识点3231": 0.0015556615710107652, "补充知识点3232": 0.0015556615710107652, "注水中的折线图问题-": 8.296861712057415e-05, "从折线图获取信息-": 8.296861712057415e-05, "补充知识点22881": 8.296861712057415e-05, "补充知识点22882": 8.296861712057415e-05, "补充知识点22883": 8.296861712057415e-05, "绘制单式折线统计图": 0.00024890585136172244, "绘制单式折线统计图的方法": 0.00024890585136172244, "补充知识点22410": 0.00024890585136172244, "补充知识点22411": 0.00024890585136172244, "画三角形": 0.0003318744684822966, "在点子图上画三角形": 0.0003318744684822966, "画锐角/直角/钝角/等腰/等腰直角/等边三角形": 0.0003318744684822966, "补充知识点16937": 0.0003318744684822966, "补充知识点16938": 0.0003318744684822966, "同分母分数连加在实际生活中的应用-": 0.0001659372342411483, "同分母分数连加的简单应用": 0.0001659372342411483, "补充知识点7901": 0.0001659372342411483, "旋转的含义": 0.00041484308560287074, "判断旋转现象": 0.00041484308560287074, "补充知识点20626": 0.00041484308560287074, "补充知识点20627": 0.00041484308560287074, "补充知识点20628": 0.00041484308560287074, "平移、旋转在拼图中的应用-": 0.0002281636970815789, "拼图中平移、旋转的方法（打乱图片还原）-": 0.0002281636970815789, "补充知识点20866": 0.0002281636970815789, "补充知识点20867": 0.0002281636970815789, "补充知识点20868": 0.0002281636970815789, "旋转与重合": 0.0008089440169255979, "找绕中心旋转指定角度后，不能与原图重合的图形": 0.0008089440169255979, "补充知识点20665": 0.0008089440169255979, "补充知识点20666": 0.0008089440169255979, "补充知识点20667": 0.0008089440169255979, "旋转的特征": 0.00024890585136172244, "认识旋转的特征": 0.00024890585136172244, "补充知识点20695": 0.00024890585136172244, "补充知识点20696": 0.00024890585136172244, "补充知识点20697": 0.00024890585136172244, "基础图形的运动-": 0.0004563273941631578, "基础图形的旋转": 0.0004563273941631578, "补充知识点20641": 0.0004563273941631578, "补充知识点20642": 0.0004563273941631578, "补充知识点20643": 0.0004563273941631578, "不含括号的四则混合运算": 0.00018667938852129183, "判断对错并改正": 0.00018667938852129183, "补充知识点6994": 0.00018667938852129183, "含有中括号的四则混合运算": 0.00018667938852129183, "根据文字描述列式计算(含中括号)": 0.00018667938852129183, "补充知识点6908": 0.00018667938852129183, "小括号对运算结果的影响": 4.1484308560287074e-05, "小括号不同位置对运算结果的影响": 4.1484308560287074e-05, "补充知识点6895": 4.1484308560287074e-05, "错中求解(表内混合运算)": 0.0003318744684822966, "错中求解综合": 0.0003318744684822966, "补充知识点23727": 0.0003318744684822966, "补充知识点23728": 0.0003318744684822966, "补充知识点23729": 0.0003318744684822966, "楼层中的负数": 0.00014519507996100476, "用正负数表示楼层变化": 0.00014519507996100476, "补充知识点3487": 0.00014519507996100476, "补充知识点3488": 0.00014519507996100476, "含字母的最大公因数、最小公倍数推理问题-": 0.00010371077140071768, "求两字母的最小公倍数（知两字母质因数乘积和最大公因数）-": 0.00010371077140071768, "补充知识点1566": 0.00010371077140071768, "分数与数轴-": 8.296861712057415e-05, "知一个分数位置，求另一分数可能位置（分母是相同字母）-": 8.296861712057415e-05, "补充知识点2891": 8.296861712057415e-05, "补充知识点2892": 8.296861712057415e-05, "与最大公因数、最小公倍数有关的推理问题-": 0.0001659372342411483, "求另一个数（知最小公倍数、最大公因数和其中一个数）-": 0.0001659372342411483, "补充知识点1569": 0.0001659372342411483, "从分数角度认识整数-": 0.0004770695484433013, "将涂色部分用分数表示-": 0.0004770695484433013, "补充知识点2749": 0.0004770695484433013, "补充知识点2750": 0.0004770695484433013, "分数与小数互化的应用": 0.00037335877704258366, "分数与小数互化的实际应用问题": 0.00037335877704258366, "补充知识点3217": 0.00037335877704258366, "补充知识点3218": 0.00037335877704258366, "求比一个数多(或少)百分之几的数是多少": 0.00031113231420215305, "求比一个数多(少)百分之几的数是多少（数形结合）": 0.00031113231420215305, "补充知识点8584": 0.00031113231420215305, "补充知识点8585": 0.00031113231420215305, "求总量的实际问题（含比）-": 0.00010371077140071768, "求总量（知两量比+总量范围）": 0.00010371077140071768, "补充知识点12131": 0.00010371077140071768, "补充知识点12132": 0.00010371077140071768, "比例尺有关的综合应用题二（结合行程）-": 0.00014519507996100476, "求相遇时行驶的距离（知比例尺+图上距离+相遇时间+速度比）-": 0.00014519507996100476, "补充知识点12954": 0.00014519507996100476, "补充知识点12955": 0.00014519507996100476, "已知两量比，求两量关系-": 0.00012445292568086122, "求谁比谁多（少）几分之几/百分之几-": 0.00012445292568086122, "补充知识点12097": 0.00012445292568086122, "补充知识点12098": 0.00012445292568086122, "用逆推法解决年龄问题": 0.00012445292568086122, "逆推法解决年龄问题": 0.00012445292568086122, "补充知识点14125": 0.00012445292568086122, "补充知识点14126": 0.00012445292568086122, "数与形": 0.0018045674223724877, "数与形（探索规律）-": 0.000539296011283732, "用小棒摆三角形": 0.00010371077140071768, "已知三角形个数求小棒数": 0.00010371077140071768, "补充知识点15256": 0.00010371077140071768, "两、三位数乘一位数的估算": 0.0002903901599220095, "两、三位数乘一位数估算的过程": 0.0002903901599220095, "补充知识点7053": 0.0002903901599220095, "1000以内数的估计": 8.296861712057415e-05, "估计(1000以内)": 8.296861712057415e-05, "根据已知量估计整体量": 8.296861712057415e-05, "补充知识点601": 8.296861712057415e-05, "含百分数的大小比较": 0.00018667938852129183, "含百分数、小数、分数的多个数比大小": 0.00018667938852129183, "补充知识点3335": 0.00018667938852129183, "补充知识点3336": 0.00018667938852129183, "百分数在具体情景中的含义": 8.296861712057415e-05, "生活中常见的百分率的理解": 8.296861712057415e-05, "补充知识点3287": 8.296861712057415e-05, "补充知识点3288": 8.296861712057415e-05, "认识数字": 0.00010371077140071768, "认识记数方法": 0.00010371077140071768, "补充知识点906": 0.00010371077140071768, "初步认识鸽巢原理\"\"": 0.0006222646284043061, "鸽巢原理\"的探究\"": 0.0006222646284043061, "补充知识点15292": 0.0006222646284043061, "求鸽子总数-": 0.0006844910912447367, "求鸽子总数（鸽巢数、鸽子数隐含）-": 0.0006844910912447367, "补充知识点15299": 0.0006844910912447367, "公倍数与最小公倍数的运用": 0.0010578498682873203, "已知连续自然运算结果，求它们的最小公倍数-": 0.0010578498682873203, "补充知识点1529": 0.0010578498682873203, "点到直线的距离": 0.00014519507996100476, "画垂线段": 0.00014519507996100476, "补充知识点15665": 0.00014519507996100476, "补充知识点15666": 0.00014519507996100476, "过直线外一点画平行线": 2.0742154280143537e-05, "画己知直线的平行线": 2.0742154280143537e-05, "在方格纸上画互相平行的直线": 2.0742154280143537e-05, "补充知识点15699": 2.0742154280143537e-05, "补充知识点15700": 2.0742154280143537e-05, "梯形的周长": 0.0001659372342411483, "等腰梯形周长相关的计算": 0.0001659372342411483, "求腰长": 0.0001659372342411483, "补充知识点16661": 0.0001659372342411483, "补充知识点16662": 0.0001659372342411483, "梯形与平行四边形的面积问题": 0.00014519507996100476, "知平行四边形面积和拼接关系，求梯形面积": 0.00014519507996100476, "补充知识点16725": 0.00014519507996100476, "添补法求不规则图形的面积": 6.222646284043061e-05, "割补法求简单不规则图形面积": 6.222646284043061e-05, "补充知识点17839": 6.222646284043061e-05, "补充知识点17840": 6.222646284043061e-05, "补充知识点17841": 6.222646284043061e-05, "方中圆问题（一方一圆）-": 0.00010371077140071768, "求长方形中最大圆的直/半径-": 0.00010371077140071768, "补充知识点17445": 0.00010371077140071768, "补充知识点17446": 0.00010371077140071768, "高变化有关的长、正方体表面积计算-": 2.0742154280143537e-05, "求增加/减少的表面积（知棱长+高增加/减少量）-": 2.0742154280143537e-05, "补充知识点18272": 2.0742154280143537e-05, "补充知识点18273": 2.0742154280143537e-05, "图形的运动（轴对称、平移、旋转）": 0.0002281636970815789, "通过平移旋转求阴影部分的面积": 0.0002281636970815789, "补充知识点20848": 0.0002281636970815789, "补充知识点20849": 0.0002281636970815789, "补充知识点20850": 0.0002281636970815789, "求与圆有关的阴影部分面积-": 0.00014519507996100476, "整体减空白法求阴影面积（半圆中含等腰直角三角形）-": 0.00014519507996100476, "补充知识点17476": 0.00014519507996100476, "根据运动情况绘制示意图-": 2.0742154280143537e-05, "绘制另一时刻两物体的位置示意图（知速度+某时刻发现的物体位置）": 2.0742154280143537e-05, "补充知识点21532": 2.0742154280143537e-05, "补充知识点21533": 2.0742154280143537e-05, "含圆柱的立体组合图形的体积(挖去型)": 0.00014519507996100476, "挖半个圆柱型的不规则几何体（含长/正方体）-": 0.00014519507996100476, "补充知识点19542": 0.00014519507996100476, "补充知识点19543": 0.00014519507996100476, "统计图的选择": 0.0007467175540851673, "统计图的选择（扇形统计图）": 0.0007467175540851673, "选择合适的统计图": 0.0007467175540851673, "统计图使用情况的正误辨析": 0.0007467175540851673, "补充知识点22487": 0.0007467175540851673, "将分数化为最简分数-": 0.0006015224741241625, "将分数化简-": 0.0006015224741241625, "补充知识点3115": 0.0006015224741241625, "补充知识点3116": 0.0006015224741241625, "求因数、公因数、最大公因数问题-": 0.000850428325485885, "填写两数的因数，公因数、最大公因数-": 0.000850428325485885, "补充知识点1478": 0.000850428325485885, "推理求最简分数-": 0.0008089440169255979, "反求原最简分数（知分子分母变化后的分数）": 0.0008089440169255979, "补充知识点3153": 0.0008089440169255979, "补充知识点3154": 0.0008089440169255979, "涂色/画图理解分数基本性质的变式-": 6.222646284043061e-05, "涂色并补全分数变式-": 6.222646284043061e-05, "补充知识点3031": 6.222646284043061e-05, "补充知识点3032": 6.222646284043061e-05, "通过分数基本性质变化后比较大小-": 0.0007882018626454544, "比较两分数大小-": 0.001203044948248325, "补充知识点2997": 0.0007882018626454544, "补充知识点2998": 0.0007882018626454544, "分数比大小（真、假、带、整）-": 0.00041484308560287074, "补充知识点2851": 0.00041484308560287074, "补充知识点2852": 0.00041484308560287074, "扇形统计图和条形统计图的比较": 0.00041484308560287074, "两种统计图之间的复杂数据分析": 0.00041484308560287074, "补充知识点22677": 0.00041484308560287074, "补充知识点22678": 0.00041484308560287074, "补充知识点22679": 0.00041484308560287074, "简单事件发生的可能性求解": 0.00031113231420215305, "运用列表法解决可能性问题": 8.296861712057415e-05, "用列表法分析两数之和的可能": 8.296861712057415e-05, "补充知识点23097": 8.296861712057415e-05, "补充知识点23098": 8.296861712057415e-05, "补充知识点23099": 8.296861712057415e-05, "扇形统计图中获取信息计算-": 0.00018667938852129183, "由图中数据，计算某量占总量百分比": 0.00018667938852129183, "补充知识点22740": 0.00018667938852129183, "补充知识点22741": 0.00018667938852129183, "补充知识点22742": 0.00018667938852129183, "由实际问题，找两量的正确图线-": 6.222646284043061e-05, "找出用水量与水费的正确图线": 6.222646284043061e-05, "补充知识点22890": 6.222646284043061e-05, "补充知识点22891": 6.222646284043061e-05, "补充知识点22892": 6.222646284043061e-05, "根据可能性大小进行推测": 6.222646284043061e-05, "根据结果推测数量的多少": 6.222646284043061e-05, "补充知识点23070": 6.222646284043061e-05, "补充知识点23071": 6.222646284043061e-05, "补充知识点23072": 6.222646284043061e-05, "游戏规则的公平性": 0.0002281636970815789, "可能性判断游戏规则的公平": 0.0002281636970815789, "游戏规则公平的方案": 0.0002281636970815789, "补充知识点23055": 0.0002281636970815789, "补充知识点23056": 0.0002281636970815789, "补充知识点23057": 0.0002281636970815789, "根据可能性大小反求个数-": 6.222646284043061e-05, "由摸出颜色可能性大小，反求球的最少数量": 6.222646284043061e-05, "补充知识点23034": 6.222646284043061e-05, "补充知识点23035": 6.222646284043061e-05, "小数加法(小数部分位数不同)": 8.296861712057415e-05, "两个小数的数位不对齐，但不进位": 8.296861712057415e-05, "补充知识点7195": 8.296861712057415e-05, "圆柱的认识-": 0.0003318744684822966, "填写圆柱各部分名称（有图）": 0.0003318744684822966, "补充知识点18864": 0.0003318744684822966, "补充知识点18865": 0.0003318744684822966, "圆柱的形成-": 0.0001659372342411483, "旋转形成的圆柱与长方形的关系（不计算）": 0.0001659372342411483, "补充知识点18848": 0.0001659372342411483, "补充知识点18849": 0.0001659372342411483, "先提价后打折-": 0.00010371077140071768, "求利润-": 0.00012445292568086122, "补充知识点8656": 0.00010371077140071768, "圆柱的底面积": 0.00010371077140071768, "圆柱的占地面积": 0.00010371077140071768, "补充知识点19405": 0.00010371077140071768, "补充知识点19406": 0.00010371077140071768, "补充知识点19407": 0.00010371077140071768, "数与形（归纳递推）": 0.0012652714110887556, "运用数与形总结规律": 0.0011823027939681814, "倍数型": 0.0011823027939681814, "补充知识点15271": 0.0011823027939681814, "用小棒摆其他平面图形": 0.00024890585136172244, "摆正方形": 0.00024890585136172244, "补充知识点15249": 0.00024890585136172244, "算式比大小(整数的四则混合运算)": 0.00014519507996100476, "根据算式的特点计算比大小": 0.00014519507996100476, "补充知识点24388": 0.00014519507996100476, "补充知识点24389": 0.00014519507996100476, "补充知识点24390": 0.00014519507996100476, "购物问题": 0.0002281636970815789, "补充知识点14305": 0.0002281636970815789, "补充知识点14306": 0.0002281636970815789, "平行的特征及性质": 0.00020742154280143537, "关于平行的相关判断": 0.0001659372342411483, "根据平行的特点判断是否平行": 0.0001659372342411483, "补充知识点15637": 0.0001659372342411483, "补充知识点15638": 0.0001659372342411483, "比轻重": 4.1484308560287074e-05, "三种物品的重量比较": 2.0742154280143537e-05, "根据轻重排序(3个量)": 2.0742154280143537e-05, "补充知识点114": 2.0742154280143537e-05, "天平上的等量代换-": 6.222646284043061e-05, "有关物品的等量代换": 6.222646284043061e-05, "补充知识点10901": 6.222646284043061e-05, "补充知识点10902": 6.222646284043061e-05, "补充知识点10903": 6.222646284043061e-05, "绿色出行": 2.0742154280143537e-05, "出行方式相关的问题": 2.0742154280143537e-05, "补充知识点14313": 2.0742154280143537e-05, "往返行程问题": 0.00018667938852129183, "往返行程求总路程": 0.00018667938852129183, "补充知识点14341": 0.00018667938852129183, "北京五日游-": 0.00012445292568086122, "五日游中的费用问题-": 0.00010371077140071768, "由表中数据，计算预算总费用": 0.00010371077140071768, "补充知识点14374": 0.00010371077140071768, "优惠方案问题(买门票、购物等)": 0.0002281636970815789, "解决优惠方案问题": 0.0002281636970815789, "补充知识点15015": 0.0002281636970815789, "有趣的平衡-": 0.0002281636970815789, "看图使天平平衡，求其中一个物体重量": 0.0002281636970815789, "补充知识点12634": 0.0002281636970815789, "补充知识点12635": 0.0002281636970815789, "解决一个数的因数是另一个数的倍数的实际问题": 6.222646284043061e-05, "解决简单实际问题(给定范围)": 6.222646284043061e-05, "补充知识点1065": 6.222646284043061e-05, "分数的产生": 0.0001659372342411483, "分数产生的原因-": 0.0001659372342411483, "补充知识点2521": 0.0001659372342411483, "异分母分数连加计算-": 0.0003941009313227272, "分数单位相加巧算（分母成倍数关系）": 0.0003941009313227272, "补充知识点8016": 0.0003941009313227272, "异分母分数连减计算-": 6.222646284043061e-05, "补充知识点8022": 6.222646284043061e-05, "辨析情景中分数大小（单位1不同）-": 0.0004355852398830143, "比较分数大小说明理由-": 0.0004355852398830143, "补充知识点2662": 0.0004355852398830143, "正比例在工程问题中的应用-": 0.00014519507996100476, "求还需工时（知总量、前N天的量需算）": 0.00014519507996100476, "补充知识点12576": 0.00014519507996100476, "补充知识点12577": 0.00014519507996100476, "比例尺、图上距离、实际距离综合-": 0.00020742154280143537, "完成表格填写-": 0.00020742154280143537, "补充知识点12832": 0.00020742154280143537, "补充知识点12833": 0.00020742154280143537, "由图上/实际比，求实际/图上比-": 6.222646284043061e-05, "由图上长度比，求实际长度比（知比例尺）-": 6.222646284043061e-05, "补充知识点12948": 6.222646284043061e-05, "补充知识点12949": 6.222646284043061e-05, "运用最简分数推理求值（加减的数/字母）-": 0.00041484308560287074, "知两最简分数间的自然数个数，求分数中的字母值-": 0.00041484308560287074, "补充知识点3129": 0.00041484308560287074, "补充知识点3130": 0.00041484308560287074, "由表中数据探究两量的比例关系（正比例）-": 0.0006015224741241625, "写比、求比值、判断比例关系-": 0.0006015224741241625, "补充知识点12438": 0.0006015224741241625, "补充知识点12439": 0.0006015224741241625, "由表中数据探究两量的比例关系（反比例）-": 0.001389724336769617, "完善表格探究两量关系并计算-": 0.001389724336769617, "补充知识点12506": 0.001389724336769617, "补充知识点12507": 0.001389724336769617, "根据正比例关系计算": 0.00024890585136172244, "知两量比例关系、给出三个数值，求其中一个量数值-": 0.00024890585136172244, "补充知识点12293": 0.00024890585136172244, "补充知识点12294": 0.00024890585136172244, "组成比例（复杂情景）-": 4.1484308560287074e-05, "火车过隧道问题": 4.1484308560287074e-05, "补充知识点12193": 4.1484308560287074e-05, "补充知识点12194": 4.1484308560287074e-05, "比例各部分名称": 0.00035261662276244013, "判断一般形式的比例各部分的名称": 0.00035261662276244013, "补充知识点12221": 0.00035261662276244013, "补充知识点12222": 0.00035261662276244013, "直角梯形旋转的体积问题（含圆锥）-": 0.00018667938852129183, "求两梯形旋转成的几何体体积-": 0.00018667938852129183, "补充知识点19389": 0.00018667938852129183, "补充知识点19390": 0.00018667938852129183, "挖圆柱的表面积问题-": 6.222646284043061e-05, "大圆柱中挖小圆柱": 6.222646284043061e-05, "补充知识点19004": 6.222646284043061e-05, "补充知识点19005": 6.222646284043061e-05, "圆柱体积的变化": 0.00020742154280143537, "由底面半径/直径/周长+高的变化，求体积变化": 0.00020742154280143537, "补充知识点19072": 0.00020742154280143537, "补充知识点19073": 0.00020742154280143537, "圆柱配底问题-": 8.296861712057415e-05, "由侧面展开图信息，求所配底的信息": 8.296861712057415e-05, "补充知识点18904": 8.296861712057415e-05, "补充知识点18905": 8.296861712057415e-05, "含圆柱的立体组合图形的体积(叠放型)": 0.0001659372342411483, "由折线图完成圆柱组合体相关计算-": 0.0001659372342411483, "补充知识点19530": 0.0001659372342411483, "补充知识点19531": 0.0001659372342411483, "竖切圆锥体积的计算": 0.00020742154280143537, "求圆锥体积（已知截面积+高）-": 0.00020742154280143537, "补充知识点20267": 0.00020742154280143537, "补充知识点20268": 0.00020742154280143537, "圆锥的截面": 0.0001659372342411483, "求截面面积-": 0.0001659372342411483, "补充知识点19258": 0.0001659372342411483, "补充知识点19259": 0.0001659372342411483, "圆锥的形成": 0.00024890585136172244, "由三角形信息求旋转成的圆锥高/底面量": 0.00024890585136172244, "补充知识点19236": 0.00024890585136172244, "补充知识点19237": 0.00024890585136172244, "运用小数的性质解决问题": 0.0011823027939681814, "解决小数的改写的实际问题(点小数点)": 0.0011823027939681814, "补充知识点2068": 0.0011823027939681814, "补充知识点2069": 0.0011823027939681814, "根据读0个数的组数问题(小数)": 0.0007467175540851673, "根据读0个数的组数问题": 0.0007467175540851673, "补充知识点2166": 0.0007467175540851673, "补充知识点2167": 0.0007467175540851673, "小数组数问题": 0.000539296011283732, "根据所给数字组成不同的数(并排序)": 0.000539296011283732, "补充知识点2154": 0.000539296011283732, "补充知识点2155": 0.000539296011283732, "斜切圆柱的体积": 0.00020742154280143537, "求斜切圆柱的体积": 0.00020742154280143537, "补充知识点20235": 0.00020742154280143537, "补充知识点20236": 0.00020742154280143537, "长/正方形卷成圆柱问题（涉及底面信息、高）": 8.296861712057415e-05, "由长/正方形信息，求圆柱信息（边长已知）-": 8.296861712057415e-05, "补充知识点18908": 8.296861712057415e-05, "补充知识点18909": 8.296861712057415e-05, "长/正方形旋转成圆柱问题（求底面量、高）": 6.222646284043061e-05, "由长/正方形信息，求圆柱相关量": 6.222646284043061e-05, "补充知识点18914": 6.222646284043061e-05, "补充知识点18915": 6.222646284043061e-05, "正方体体积公式实际应用": 6.222646284043061e-05, "大正方体里最多放小正方体个数（棱长已知）9": 6.222646284043061e-05, "补充知识点18670": 6.222646284043061e-05, "补充知识点18671": 6.222646284043061e-05, "正方体棱长的认识及运用-": 0.00012445292568086122, "判断所给长度够不够-": 0.00012445292568086122, "补充知识点18405": 0.00012445292568086122, "补充知识点18406": 0.00012445292568086122, "小数连减应用题": 0.000539296011283732, "购买两种商品求还剩多少钱": 0.000539296011283732, "补充知识点7283": 0.000539296011283732, "确定平移后的图形": 0.0001659372342411483, "已知原图形及平移的方向和距离，判断现图形": 0.0001659372342411483, "补充知识点20548": 0.0001659372342411483, "补充知识点20549": 0.0001659372342411483, "补充知识点20550": 0.0001659372342411483, "图形的平移": 0.0009541390968866026, "通过平移重合(多个不同物体)": 0.0009541390968866026, "补充知识点20533": 0.0009541390968866026, "补充知识点20534": 0.0009541390968866026, "补充知识点20535": 0.0009541390968866026, "改动替换一个数后的平均数": 4.1484308560287074e-05, "根据平均数求改动的数": 4.1484308560287074e-05, "补充知识点25884": 4.1484308560287074e-05, "补充知识点25885": 4.1484308560287074e-05, "补充知识点25886": 4.1484308560287074e-05, "除法各部分之间的关系(无余数)": 0.00041484308560287074, "无余数的除法各部分间的关系": 0.00041484308560287074, "补充知识点9165": 0.00041484308560287074, "补充知识点9166": 0.00041484308560287074, "付款与找零": 0.000580780319844019, "由付款和找零确定商品价格": 0.000580780319844019, "补充知识点13362": 0.000580780319844019, "补充知识点13363": 0.000580780319844019, "简单的加、减运算(人民币)": 0.00010371077140071768, "相同单位相加减": 0.00010371077140071768, "补充知识点13474": 0.00010371077140071768, "补充知识点13475": 0.00010371077140071768, "人民币的计算与比较": 0.00018667938852129183, "简单换算后再比较": 0.00018667938852129183, "补充知识点13486": 0.00018667938852129183, "补充知识点13487": 0.00018667938852129183, "购物预算（钱数已知）": 0.0004355852398830143, "还差多少钱": 0.0004355852398830143, "补充知识点13378": 0.0004355852398830143, "补充知识点13379": 0.0004355852398830143, "人民币单位间的换算": 0.00035261662276244013, "人民币单位换算(单名数)": 0.00035261662276244013, "补充知识点13390": 0.00035261662276244013, "补充知识点13391": 0.00035261662276244013, "钱数问题": 0.00041484308560287074, "付钱方式": 0.00041484308560287074, "补充知识点13348": 0.00041484308560287074, "补充知识点13349": 0.00041484308560287074, "人民币的面额的应用": 0.000269648005641866, "选正好买的商品": 0.000269648005641866, "补充知识点13498": 0.000269648005641866, "补充知识点13499": 0.000269648005641866, "十元以上人民币的认识与计算": 8.296861712057415e-05, "认识大面额人民币": 8.296861712057415e-05, "根据面额写总钱数（多面额）": 8.296861712057415e-05, "补充知识点13554": 8.296861712057415e-05, "补充知识点13555": 8.296861712057415e-05, "由物品填合适的人民币单位": 0.0004563273941631578, "补充知识点13512": 0.0002281636970815789, "补充知识点13513": 0.0002281636970815789, "人民币的兑换": 0.0004978117027234449, "两种面值的钱币之间的兑换（大换小）": 0.0004978117027234449, "补充知识点13410": 0.0004978117027234449, "补充知识点13411": 0.0004978117027234449, "十元以下人民币的认识与计算": 0.000269648005641866, "认识小面额人民币": 0.000269648005641866, "根据面额写总钱数(多面额)": 0.000269648005641866, "补充知识点13534": 0.000269648005641866, "补充知识点13535": 0.000269648005641866, "一个数比某数多（少）几，求某数": 8.296861712057415e-05, "补充知识点4903": 8.296861712057415e-05, "两位数减两位数(退位减)的应用": 0.00024890585136172244, "补充知识点4729": 0.00024890585136172244, "口算两位数加一位数(不进位)的应用": 6.222646284043061e-05, "补充知识点4430": 6.222646284043061e-05, "看图列式（知多/少几）": 0.00010371077140071768, "补充知识点4913": 0.00010371077140071768, "减法问题结构": 0.00010371077140071768, "求剩下型": 0.00010371077140071768, "补充知识点4841": 0.00010371077140071768, "加法问题结构": 0.00018667938852129183, "添加型": 0.00018667938852129183, "补充知识点4816": 0.00018667938852129183, "组数问题（100以内加减法）": 8.296861712057415e-05, "由所给数字组数写算式": 8.296861712057415e-05, "补充知识点14605": 8.296861712057415e-05, "补充知识点14606": 8.296861712057415e-05, "笔算(连加)解决实际问题": 8.296861712057415e-05, "补充知识点4790": 8.296861712057415e-05, "两位数减两位数(不退位)的应用": 0.00010371077140071768, "补充知识点4551": 0.00010371077140071768, "减法竖式谜（100以内）": 0.00010371077140071768, "两位数减两位数竖式谜": 0.00010371077140071768, "补充知识点24538": 0.00010371077140071768, "补充知识点24539": 0.00010371077140071768, "补充知识点24540": 0.00010371077140071768, "图文算式（竖式计算）": 0.00014519507996100476, "找结果相等的竖式": 0.00014519507996100476, "补充知识点14049": 0.00014519507996100476, "补充知识点14050": 0.00014519507996100476, "笔算两位数加两位数(进位加)的应用": 0.00024890585136172244, "看图列式计算": 0.00024890585136172244, "补充知识点4668": 0.00024890585136172244, "连减的计算与应用": 0.0003318744684822966, "补充知识点4806": 0.0003318744684822966, "口算两位数减一位数(不退位)": 0.0001659372342411483, "与数位器结合先划再计算": 0.0001659372342411483, "补充知识点4469": 0.0001659372342411483, "口算两位数加两位数(不进位加)的应用": 2.0742154280143537e-05, "补充知识点4518": 2.0742154280143537e-05, "珠子摆数解决实际问题": 0.00012445292568086122, "已知珠子颗数，求数的可能情况": 0.00012445292568086122, "补充知识点566": 0.00012445292568086122, "长/正方体切割的体积问题-": 0.00031113231420215305, "长/正方体，切成不规则立体图形的体积": 0.00031113231420215305, "补充知识点20143": 0.00031113231420215305, "补充知识点20144": 0.00031113231420215305, "含容积单位的计算": 0.0001659372342411483, "含容积单位的计算(不涉及单位换算)": 0.0001659372342411483, "补充知识点22010": 0.0001659372342411483, "含多边形的组合图形的周长": 2.0742154280143537e-05, "多边形周长的计算": 2.0742154280143537e-05, "方格图中的周长": 2.0742154280143537e-05, "补充知识点17599": 2.0742154280143537e-05, "补充知识点17600": 2.0742154280143537e-05, "补充知识点17601": 2.0742154280143537e-05, "梯形与三角形面积问题-": 2.0742154280143537e-05, "梯形转化为三角形，求三角形底（知梯形面积+高）": 2.0742154280143537e-05, "补充知识点16732": 2.0742154280143537e-05, "阴影部分的周长和面积": 2.0742154280143537e-05, "平面图形的面积": 2.0742154280143537e-05, "组合图形(不含圆)求阴影部分面积": 2.0742154280143537e-05, "补充知识点17911": 2.0742154280143537e-05, "补充知识点17912": 2.0742154280143537e-05, "补充知识点17913": 2.0742154280143537e-05, "圆的周长公式正向应用": 0.0001659372342411483, "扩圆后的周长或增加的长度": 0.0001659372342411483, "补充知识点17274": 0.0001659372342411483, "圆的面积公式推导": 0.0003318744684822966, "圆面积公式的推导(拼成长方形或平行四边形)": 0.0003318744684822966, "补充知识点17297": 0.0003318744684822966, "计数器上增添珠子后表示的数": 0.0001659372342411483, "拿走珠子后表示的数": 0.0001659372342411483, "补充知识点553": 0.0001659372342411483, "数的顺序（100以内）的应用": 0.00018667938852129183, "书的页码问题": 0.00018667938852129183, "补充知识点485": 0.00018667938852129183, "用固定数珠子在计数器上表示数": 0.00018667938852129183, "固定颗数的珠子表示不同的两位数": 0.00018667938852129183, "补充知识点545": 0.00018667938852129183, "点到直线的距离应用": 0.000269648005641866, "垂线段的性质在生活中的具体应用(测量)": 0.000269648005641866, "补充知识点15675": 0.000269648005641866, "补充知识点15676": 0.000269648005641866, "几十几添上一个数是多少": 4.1484308560287074e-05, "99添1": 4.1484308560287074e-05, "补充知识点442": 4.1484308560287074e-05, "与比有关的工程问题-": 0.00010371077140071768, "工总一定，根据工时求工效比": 0.00010371077140071768, "补充知识点12101": 0.00010371077140071768, "补充知识点12102": 0.00010371077140071768, "按比分配": 4.1484308560287074e-05, "平面图形中简单的按比分配": 4.1484308560287074e-05, "补充知识点11877": 4.1484308560287074e-05, "补充知识点11878": 4.1484308560287074e-05, "用方程法解分数乘法应用题": 6.222646284043061e-05, "解决几倍多几实际问题（需先计算量）": 6.222646284043061e-05, "补充知识点11239": 6.222646284043061e-05, "补充知识点11240": 6.222646284043061e-05, "用字母表示平面图形中的量-": 8.296861712057415e-05, "用字母表示周长/面积（单个长方形）": 8.296861712057415e-05, "补充知识点10748": 8.296861712057415e-05, "补充知识点10749": 8.296861712057415e-05, "班级人数变化的百分数问题-": 2.0742154280143537e-05, "求转来的学生数": 2.0742154280143537e-05, "补充知识点8889": 2.0742154280143537e-05, "补充知识点8890": 2.0742154280143537e-05, "浓度问题中的增加溶质或溶剂问题": 8.296861712057415e-05, "求需要增加的溶剂量": 8.296861712057415e-05, "补充知识点26191": 8.296861712057415e-05, "补充知识点26192": 8.296861712057415e-05, "补充知识点26193": 8.296861712057415e-05, "有余数的小数除法9": 2.0742154280143537e-05, "已知算式中被除数和除数的变化情况，求余数": 2.0742154280143537e-05, "补充知识点7494": 2.0742154280143537e-05, "千克与克的相关应用题": 0.0007259753998050237, "乘法应用题": 0.0007259753998050237, "补充知识点13641": 0.0007259753998050237, "补充知识点13642": 0.0007259753998050237, "克、千克之间的换算与比较": 0.000580780319844019, "千克和克之间的进率及换算": 0.000580780319844019, "千克和克的换算": 0.000580780319844019, "补充知识点13589": 0.000580780319844019, "天平上的数学": 0.0002281636970815789, "天平称重": 0.0002281636970815789, "读取秤上数值": 0.0002281636970815789, "根据秤的显示写重量": 0.0002281636970815789, "补充知识点26397": 0.0002281636970815789, "补充知识点26398": 0.0002281636970815789, "克的认识": 0.0001659372342411483, "认识克": 0.0001659372342411483, "1克有多重": 0.0001659372342411483, "补充知识点13568": 0.0001659372342411483, "用几百几十、整十、整百和整千数的估算解决问题": 0.00041484308560287074, "用估算解决总价/总数量问题": 0.00041484308560287074, "补充知识点7042": 0.00041484308560287074, "封闭型数阵图(整百数)": 0.0001659372342411483, "给出角上的数": 0.0001659372342411483, "补充知识点24895": 0.0001659372342411483, "补充知识点24896": 0.0001659372342411483, "补充知识点24897": 0.0001659372342411483, "整百、整千数、几百几十数的加减法": 0.0007259753998050237, "整百、整千数、几百几十数加(减)法口算": 0.0007259753998050237, "补充知识点4960": 0.0007259753998050237, "整十、整百和整千数的加减法估算": 0.00018667938852129183, "用整十、整百、整千数估算": 0.00018667938852129183, "补充知识点7033": 0.00018667938852129183, "求万以内数的近似数": 0.001389724336769617, "直接写数的近似数": 0.001389724336769617, "补充知识点957": 0.001389724336769617, "万以内数的大小比较": 0.0019912468108937795, "两数比大小(万以内)": 0.0004770695484433013, "万以内数的比大小": 0.0004770695484433013, "补充知识点682": 0.0004770695484433013, "描述数之间的大小关系(多得多...)(万以内)": 0.00037335877704258366, "已知少得多，选数量": 0.00037335877704258366, "补充知识点672": 0.00037335877704258366, "方框中填数字(万以内数的比大小)": 0.0004355852398830143, "可以填几": 0.0004355852398830143, "补充知识点679": 0.0004355852398830143, "多数比大小(万以内)": 0.0004770695484433013, "补充知识点689": 0.0004770695484433013, "万以内数的读、写法": 0.00170085665097177, "读数和写数(10000以内)": 0.0013482400282093297, "读数写数": 0.0013482400282093297, "补充知识点647": 0.0013482400282093297, "万以内数的组成": 0.0007052332455248803, "数的组成(10000以内)": 0.0007052332455248803, "已知数，写组成(10000以内)": 0.0007052332455248803, "补充知识点666": 0.0007052332455248803, "组数问题(组四位数)": 0.000850428325485885, "四个数字组最大和最小四位数(不含0)": 0.000850428325485885, "补充知识点14439": 0.000850428325485885, "补充知识点14440": 0.000850428325485885, "认识含万级的数位顺序表": 0.0019082781937732052, "补充知识点838": 0.0009541390968866026, "找规律填数(10000以内)": 0.00018667938852129183, "依次递减(10000以内)": 0.00018667938852129183, "补充知识点23301": 0.00018667938852129183, "补充知识点23302": 0.00018667938852129183, "补充知识点23303": 0.00018667938852129183, "数万以内的数": 0.00018667938852129183, "数数(数到10000)": 0.00018667938852129183, "一个一个地数(数到10000)": 0.00018667938852129183, "补充知识点637": 0.00018667938852129183, "1000以内数的读、写法": 0.0007882018626454544, "根据条件写数(1000以内)": 0.00018667938852129183, "已知各数位上的数写数": 0.00037335877704258366, "补充知识点611": 0.00018667938852129183, "算盘的认识与使用": 0.0007259753998050237, "算盘上拨数(1000以内)": 0.00020742154280143537, "看错珠子": 0.000269648005641866, "补充知识点10494": 0.00020742154280143537, "补充知识点10495": 0.00020742154280143537, "补充知识点10496": 0.00020742154280143537, "算盘表示数(1000以内)": 0.00037335877704258366, "根据算盘写数": 0.0003941009313227272, "补充知识点10476": 0.00037335877704258366, "补充知识点10477": 0.00037335877704258366, "补充知识点10478": 0.00037335877704258366, "计数器表示数(1000以内)": 0.000269648005641866, "已知珠子个数，求可以表示的数": 0.000269648005641866, "补充知识点616": 0.000269648005641866, "组数问题(组三位数)": 0.0002903901599220095, "三个数字组三位数(含0)": 0.0002903901599220095, "补充知识点14427": 0.0002903901599220095, "补充知识点14428": 0.0002903901599220095, "去尾法应用题": 0.00170085665097177, "补充知识点5937": 0.000850428325485885, "竖式计算": 0.0012237871025284686, "有余数除法竖式": 0.0011823027939681814, "有余数除法竖式各部分含义": 0.0011823027939681814, "补充知识点5898": 0.0011823027939681814, "不等式横式谜(乘法口诀)": 0.00014519507996100476, "算式比大小(不等式横式谜(乘法口诀))": 0.00014519507996100476, "补充知识点24556": 0.00014519507996100476, "补充知识点24557": 0.00014519507996100476, "补充知识点24558": 0.00014519507996100476, "100以内有余数的除法计算": 0.0011200763311277509, "有余数除法的计算与运用": 0.0011200763311277509, "有余数除法的应用": 0.0011200763311277509, "补充知识点5908": 0.0011200763311277509, "根据除余关系求余数和被除数": 0.00018667938852129183, "已知除数求最小余数、被除数": 0.00018667938852129183, "补充知识点5885": 0.00018667938852129183, "根据除余关系求余数": 0.0008089440169255979, "有除数找最大或最小余数": 0.0008089440169255979, "补充知识点5880": 0.0008089440169255979, "根据除余关系求除数和被除数": 0.0001659372342411483, "已知余数求最小除数": 0.0001659372342411483, "补充知识点5874": 0.0001659372342411483, "有余数除法的认识": 0.0009956234054468898, "认识余数": 0.0009956234054468898, "分一分，求每份数或份数和余数": 0.0009956234054468898, "补充知识点5850": 0.0009956234054468898, "简单的还原问题(一半型)": 4.1484308560287074e-05, "一半型(分一次)": 4.1484308560287074e-05, "补充知识点5714": 4.1484308560287074e-05, "含有小括号的表内混合运算": 0.0007467175540851673, "补充知识点6899": 0.0007467175540851673, "除号后添/去括号": 2.0742154280143537e-05, "添/去括号后算式结果是否相同": 0.0002281636970815789, "补充知识点24091": 2.0742154280143537e-05, "补充知识点24092": 2.0742154280143537e-05, "补充知识点24093": 2.0742154280143537e-05, "减号后添/去括号": 0.00020742154280143537, "补充知识点24118": 0.00020742154280143537, "补充知识点24119": 0.00020742154280143537, "补充知识点24120": 0.00020742154280143537, "7-9连减与除法": 0.00024890585136172244, "补充知识点5722": 0.00024890585136172244, "间隔问题的相关应用": 0.00041484308560287074, "爬楼问题求爬单层时间(已知楼层和总时间)": 0.00041484308560287074, "补充知识点15118": 0.00041484308560287074, "有隐藏条件的除法应用题(1-9)": 0.00020742154280143537, "人数需加1的除法应用题(隐藏条件)": 0.00020742154280143537, "补充知识点5837": 0.00020742154280143537, "剪纸的分辨": 0.000269648005641866, "分辨剪纸中的轴对称": 0.000269648005641866, "补充知识点20434": 0.000269648005641866, "补充知识点20435": 0.000269648005641866, "补充知识点20436": 0.000269648005641866, "钟表上分针的旋转": 0.0001659372342411483, "求钟表上分针旋转所经过的时间": 0.0001659372342411483, "补充知识点20557": 0.0001659372342411483, "补充知识点20558": 0.0001659372342411483, "补充知识点20559": 0.0001659372342411483, "对称轴的画法及数量": 0.00037335877704258366, "对折法数画对称轴": 0.00037335877704258366, "数(并画)对称轴": 0.00037335877704258366, "补充知识点20698": 0.00037335877704258366, "补充知识点20699": 0.00037335877704258366, "补充知识点20700": 0.00037335877704258366, "初步认识轴对称图形": 0.000580780319844019, "剪出的图形与剪剩的纸张相匹配": 0.000580780319844019, "补充知识点20326": 0.000580780319844019, "补充知识点20327": 0.000580780319844019, "补充知识点20328": 0.000580780319844019, "结合复式统计表提出问题": 4.1484308560287074e-05, "提出问题（不需解答）": 4.1484308560287074e-05, "补充知识点22725": 4.1484308560287074e-05, "补充知识点22726": 4.1484308560287074e-05, "补充知识点22727": 4.1484308560287074e-05, "隐藏的差倍问题（三位数除以一位数）": 0.00020742154280143537, "补充知识点25833": 0.00020742154280143537, "补充知识点25834": 0.00020742154280143537, "补充知识点25835": 0.00020742154280143537, "铺砖问题（不涉及单位换算）": 0.0002281636970815789, "选择那种铺砖方案": 0.0002281636970815789, "补充知识点16463": 0.0002281636970815789, "补充知识点16464": 0.0002281636970815789, "排列中的组数问题（卡片）": 0.00010371077140071768, "数字卡片组两位数(涉及旋转)": 0.00010371077140071768, "补充知识点14501": 0.00010371077140071768, "补充知识点14502": 0.00010371077140071768, "一位小数加减法的应用（有实际情境）": 0.0001659372342411483, "根据题意列式（不计算）": 0.0001659372342411483, "补充知识点7261": 0.0001659372342411483, "填数问题（一位小数）": 0.00037335877704258366, "填合适的数（两个数比较）": 0.00037335877704258366, "补充知识点2302": 0.00037335877704258366, "补充知识点2303": 0.00037335877704258366, "加法运算定律和减法运算性质的综合运用": 0.0004978117027234449, "运用加、减法运算定律/性质简算": 0.0004978117027234449, "补充知识点9501": 0.0004978117027234449, "补充知识点9502": 0.0004978117027234449, "两、三位数乘一位数的估算应用": 0.0003318744684822966, "大约多少钱？": 0.0003318744684822966, "补充知识点7055": 0.0003318744684822966, "估计与估算": 8.296861712057415e-05, "用不同的估算策略解决实际问题": 8.296861712057415e-05, "利用不同的乘法策略进行估算": 8.296861712057415e-05, "补充知识点24067": 8.296861712057415e-05, "补充知识点24068": 8.296861712057415e-05, "补充知识点24069": 8.296861712057415e-05, "两个因数都变化的规律(积变化的规律)": 0.00012445292568086122, "根据算式探索积变化的规律": 0.00012445292568086122, "补充知识点10218": 0.00012445292568086122, "补充知识点10219": 0.00012445292568086122, "补充知识点10220": 0.00012445292568086122, "数阵图（一位小数）": 4.1484308560287074e-05, "由行和列的和推理某图形表示的数": 4.1484308560287074e-05, "补充知识点14067": 4.1484308560287074e-05, "补充知识点14068": 4.1484308560287074e-05, "正数与负数的概念、意义": 0.0001659372342411483, "新定义中的运用-": 0.0001659372342411483, "补充知识点3445": 0.0001659372342411483, "补充知识点3446": 0.0001659372342411483, "除数是小数的算式比大小9": 0.0001659372342411483, "选出结果最大/最小的算式（不含字母/符号）9": 0.0001659372342411483, "补充知识点24373": 0.0001659372342411483, "补充知识点24374": 0.0001659372342411483, "补充知识点24375": 0.0001659372342411483, "列简易方程": 6.222646284043061e-05, "运用自然数间的关系列方程": 2.0742154280143537e-05, "多个相邻自然数间的关系": 2.0742154280143537e-05, "补充知识点10987": 2.0742154280143537e-05, "补充知识点10988": 2.0742154280143537e-05, "除数是整十数的口算除法": 0.000269648005641866, "口算除数是整十数的除法": 2.0742154280143537e-05, "整十数除以整十数": 2.0742154280143537e-05, "补充知识点6696": 2.0742154280143537e-05, "解方程：a-x=b这种类型": 0.00010371077140071768, "解形如a－x＝b\"这种类型方程\"": 0.00010371077140071768, "补充知识点11071": 0.00010371077140071768, "补充知识点11072": 0.00010371077140071768, "已知比一个数多/少百分之几是多少，求这个数": 0.00014519507996100476, "已知比一个数多(或少)百分之几的数是多少，求这个数": 0.00014519507996100476, "已知比一个数多百分之几的数是多少，求这个数（直接算）": 0.00014519507996100476, "补充知识点8622": 0.00014519507996100476, "补充知识点8623": 0.00014519507996100476, "敲钟问题中的推理": 2.0742154280143537e-05, "由敲钟的规律推理开始时刻": 2.0742154280143537e-05, "补充知识点13248": 2.0742154280143537e-05, "钟表快慢与时间计算": 6.222646284043061e-05, "由有误差的钟表推理约定时间": 6.222646284043061e-05, "补充知识点13246": 6.222646284043061e-05, "比例的基本性质描述": 0.0008089440169255979, "补充知识点12235": 0.0008089440169255979, "补充知识点12236": 0.0008089440169255979, "古代计时方法与现代时间对照": 8.296861712057415e-05, "古代时间与现代时间对应": 8.296861712057415e-05, "补充知识点13242": 8.296861712057415e-05, "整点时间与钟面指针的位置": 6.222646284043061e-05, "认识整时": 6.222646284043061e-05, "判断时间(认识整时)": 6.222646284043061e-05, "补充知识点12972": 6.222646284043061e-05, "图与比例(一)": 0.0003318744684822966, "两正方形比、比例问题-": 0.0003318744684822966, "补充知识点12181": 0.0003318744684822966, "补充知识点12182": 0.0003318744684822966, "十几减9的应用": 0.00012445292568086122, "由图形/文字表示的计算过程选算式": 0.00012445292568086122, "补充知识点4144": 0.00012445292568086122, "十几减5、4、3、2的应用": 0.00020742154280143537, "补充知识点4258": 0.00020742154280143537, "100以内数的估计": 0.0001659372342411483, "估数(100以内)": 0.0001659372342411483, "先圈出10个，再估": 0.0001659372342411483, "补充知识点496": 0.0001659372342411483, "画指定周长的长方形、正方形": 0.0003318744684822966, "在方格纸中画长、正方形": 0.0003318744684822966, "画指定周长的长方形和正方形": 0.0003318744684822966, "补充知识点16161": 0.0003318744684822966, "补充知识点16162": 0.0003318744684822966, "结合题意编题/故事": 6.222646284043061e-05, "由所给算式编题/故事": 6.222646284043061e-05, "补充知识点4915": 6.222646284043061e-05, "看图列式（谁比谁多/少几）": 0.00012445292568086122, "补充知识点4912": 0.00012445292568086122, "笔算两位数减两位数(退位减)解决实际问题": 0.00035261662276244013, "两位数减两位数（退位）的简单实际应用": 0.00035261662276244013, "补充知识点4747": 0.00035261662276244013, "等式横式谜（100以内笔算加减法）": 0.00014519507996100476, "填数使等式成立": 0.00014519507996100476, "补充知识点24799": 0.00014519507996100476, "补充知识点24800": 0.00014519507996100476, "补充知识点24801": 0.00014519507996100476, "找规律填数（两位数加减两位数）": 2.0742154280143537e-05, "由图形中数字规律填数": 2.0742154280143537e-05, "补充知识点23225": 2.0742154280143537e-05, "补充知识点23226": 2.0742154280143537e-05, "补充知识点23227": 2.0742154280143537e-05, "口算两位数减两位数(退位减)": 6.222646284043061e-05, "口算(退位减)": 6.222646284043061e-05, "补充知识点4720": 6.222646284043061e-05, "求税率，收入额，应纳税部分": 0.00018667938852129183, "求税率": 0.00014519507996100476, "求营业税率-": 0.00014519507996100476, "补充知识点8714": 0.00014519507996100476, "估一估（有关面积和长度）": 0.00010371077140071768, "估算长方形的面积": 0.00010371077140071768, "补充知识点16473": 0.00010371077140071768, "补充知识点16474": 0.00010371077140071768, "错题改正(两位数加一位数、整十数)": 8.296861712057415e-05, "错题改正(两位数加一位数)": 8.296861712057415e-05, "补充知识点4444": 8.296861712057415e-05, "整十数加、减整十数的应用": 0.00020742154280143537, "补充知识点4412": 0.00020742154280143537, "解决有关2、5的倍数的实际问题": 0.00014519507996100476, "已知范围求至多至少": 0.00014519507996100476, "补充知识点1121": 0.00014519507996100476, "面积的意义": 0.0003941009313227272, "画图形的面积": 0.0003941009313227272, "补充知识点16243": 0.0003941009313227272, "补充知识点16244": 0.0003941009313227272, "圆柱体积有关的复杂应用（两容器）-": 2.0742154280143537e-05, "求某容器的底面积（知一容器有水一容器空+流速+两水面同高的时间+空容器半径）": 2.0742154280143537e-05, "补充知识点19168": 2.0742154280143537e-05, "补充知识点19169": 2.0742154280143537e-05, "利用加减混合运算解决实际问题": 8.296861712057415e-05, "补充知识点4360": 4.1484308560287074e-05, "优惠问题（两位数乘两位数）": 0.00012445292568086122, "满减": 0.00012445292568086122, "补充知识点14267": 0.00012445292568086122, "补充知识点14268": 0.00012445292568086122, "倍数的运用-": 8.296861712057415e-05, "一个数是未知数的倍数的求值问题": 8.296861712057415e-05, "补充知识点1014": 8.296861712057415e-05, "逻辑推理": 4.1484308560287074e-05, "简单推理": 2.0742154280143537e-05, "老师教哪门课程的推理问题-": 2.0742154280143537e-05, "补充知识点26423": 2.0742154280143537e-05, "补充知识点26424": 2.0742154280143537e-05, "运用观察法解决组合立体图形的问题": 0.0001659372342411483, "求几何组合体中有一/二/三/四/五个面涂色的小正方体个数": 0.0001659372342411483, "补充知识点17984": 0.0001659372342411483, "补充知识点17985": 0.0001659372342411483, "直条统计图": 0.0008711704797660285, "根据直条统计图解决综合问题": 0.0008711704797660285, "补充知识点22269": 0.0008711704797660285, "补充知识点22270": 0.0008711704797660285, "补充知识点22271": 0.0008711704797660285, "根据条件写数(10000以内)": 0.00018667938852129183, "补充知识点654": 0.00018667938852129183, "钟表中的对称问题": 8.296861712057415e-05, "时钟对称问题": 8.296861712057415e-05, "补充知识点20416": 8.296861712057415e-05, "补充知识点20417": 8.296861712057415e-05, "补充知识点20418": 8.296861712057415e-05, "年、月、日的应用": 8.296861712057415e-05, "月份天数的的应用": 8.296861712057415e-05, "补充知识点13117": 8.296861712057415e-05, "补充知识点13118": 8.296861712057415e-05, "推理前几天或后几天是几月几日": 0.00024890585136172244, "前一天或后一天是几月几日": 0.00024890585136172244, "补充知识点13276": 0.00024890585136172244, "一天时间的理解": 0.00037335877704258366, "明确一天的时间": 0.00037335877704258366, "补充知识点13249": 0.00037335877704258366, "在钟面上画相应的时间": 0.00012445292568086122, "钟面上画相应的时间（24时计时法）": 0.00012445292568086122, "补充知识点12986": 0.00012445292568086122, "根据日历表解决问题": 0.0001659372342411483, "根据日历表简单计算天数": 0.0001659372342411483, "补充知识点13193": 0.0001659372342411483, "两位数乘两位数积的规律": 8.296861712057415e-05, "两数和一定时，差越小，积越大": 8.296861712057415e-05, "补充知识点23216": 8.296861712057415e-05, "补充知识点23217": 8.296861712057415e-05, "补充知识点23218": 8.296861712057415e-05, "几百几十数乘整十数的口算": 0.00012445292568086122, "找计算结果相同（不同）的算式": 0.00012445292568086122, "补充知识点6627": 0.00012445292568086122, "三位数乘两位数的实际问题": 0.0004978117027234449, "三位数乘两位数口算解决实际问题": 0.00010371077140071768, "两位数乘整百数的应用": 0.00010371077140071768, "补充知识点6683": 0.00010371077140071768, "多位数乘一位数的错中求解问题": 6.222646284043061e-05, "看错一位数": 6.222646284043061e-05, "补充知识点23655": 6.222646284043061e-05, "补充知识点23656": 6.222646284043061e-05, "补充知识点23657": 6.222646284043061e-05, "列表法解决推理问题": 0.00014519507996100476, "根据两个人的条件，推理其中一人的结论": 0.00014519507996100476, "补充知识点14843": 0.00014519507996100476, "补充知识点14844": 0.00014519507996100476, "跨学科知识（新加知识点）": 0.0003318744684822966, "数学中的语文（与时间有关）": 0.00014519507996100476, "时间与对应的景象": 0.00014519507996100476, "补充知识点26260": 0.00014519507996100476, "补充知识点26261": 0.00014519507996100476, "补充知识点26262": 0.00014519507996100476, "常见时间单位": 0.00018667938852129183, "常见的时间单位": 0.00018667938852129183, "补充知识点13115": 0.00018667938852129183, "补充知识点13116": 0.00018667938852129183, "探索面积与周长的关系（方格纸）": 0.00014519507996100476, "探索面积相等的图形周长的关系": 0.00014519507996100476, "补充知识点16189": 0.00014519507996100476, "补充知识点16190": 0.00014519507996100476, "差不变原理计算面积": 4.1484308560287074e-05, "差不变原理比较图形面积的大小": 4.1484308560287074e-05, "补充知识点17806": 4.1484308560287074e-05, "补充知识点17807": 4.1484308560287074e-05, "补充知识点17808": 4.1484308560287074e-05, "正方形面积反求": 0.00018667938852129183, "已知面积，求边长": 0.00018667938852129183, "补充知识点16367": 0.00018667938852129183, "补充知识点16368": 0.00018667938852129183, "三位数除以一位数的应用（有余数）": 0.00012445292568086122, "由除数和余数找被除数": 0.00012445292568086122, "补充知识点5980": 0.00012445292568086122, "判断乘积末尾0的个数": 0.00018667938852129183, "积末尾或中间0的个数的问题": 0.00018667938852129183, "末尾0数量的辨析": 0.00018667938852129183, "补充知识点6187": 0.00018667938852129183, "积末尾0的个数（两位数乘两位数笔算）": 6.222646284043061e-05, "积末尾0的个数": 6.222646284043061e-05, "补充知识点7068": 6.222646284043061e-05, "解决三位数乘一位数(不连续进位)的实际问题": 8.296861712057415e-05, "三位数乘一位数（不连续进位）笔算应用": 8.296861712057415e-05, "补充知识点6073": 8.296861712057415e-05, "容斥原理": 6.222646284043061e-05, "重叠问题（100以内）": 6.222646284043061e-05, "重叠部分的数": 6.222646284043061e-05, "补充知识点25412": 6.222646284043061e-05, "补充知识点25413": 6.222646284043061e-05, "补充知识点25414": 6.222646284043061e-05, "关于几百几十数(几千几百数)除以一位数末尾0的个数": 4.1484308560287074e-05, "几百几十数除以一位数末尾0的个数": 4.1484308560287074e-05, "补充知识点6226": 4.1484308560287074e-05, "100以内数的读法": 8.296861712057415e-05, "由写法读数": 8.296861712057415e-05, "补充知识点454": 8.296861712057415e-05, "利润与折扣的综合问题": 0.00010371077140071768, "知进价、标价、折扣，求利润-": 0.00010371077140071768, "补充知识点8819": 0.00010371077140071768, "补充知识点8820": 0.00010371077140071768, "横式谜(20以内)": 0.00010371077140071768, "从所给数字中选数字写算式": 0.00010371077140071768, "补充知识点24682": 0.00010371077140071768, "补充知识点24683": 0.00010371077140071768, "补充知识点24684": 0.00010371077140071768, "估算够不够问题(整百、整千数加减)": 0.00024890585136172244, "给出数量判断估算哪种方案够": 0.00024890585136172244, "补充知识点7020": 0.00024890585136172244, "列表法解鸡兔同笼": 8.296861712057415e-05, "列表法解决鸡兔同笼": 8.296861712057415e-05, "列表法解决鸡兔同笼问题(变型题)": 8.296861712057415e-05, "补充知识点15023": 8.296861712057415e-05, "横式谜、算式谜(整百、整千数加减法)": 0.0001659372342411483, "利用等量代换解决算式谜": 0.0001659372342411483, "补充知识点24817": 0.0001659372342411483, "补充知识点24818": 0.0001659372342411483, "补充知识点24819": 0.0001659372342411483, "移多补少（十几减几）": 6.222646284043061e-05, "移多补少变相等，求原来": 6.222646284043061e-05, "补充知识点4254": 6.222646284043061e-05, "看图列式(十几减9)": 6.222646284043061e-05, "看图列式求还剩多少个": 6.222646284043061e-05, "补充知识点4111": 6.222646284043061e-05, "十几减9的计算方法": 0.00012445292568086122, "圈一圈，算一算": 0.00024890585136172244, "补充知识点4136": 0.00012445292568086122, "错中求解（十几减几）": 4.1484308560287074e-05, "看错减数": 4.1484308560287074e-05, "补充知识点4252": 4.1484308560287074e-05, "考试成绩中的实际问题（正负数）-": 0.0001659372342411483, "由答对答错情况，计算得分-": 0.0001659372342411483, "补充知识点10039": 0.0001659372342411483, "补充知识点10040": 0.0001659372342411483, "补充知识点10041": 0.0001659372342411483, "体育运动中的实际问题（正负数）": 0.00018667938852129183, "由所记多个数值，计算实际结果-": 0.00018667938852129183, "补充知识点10102": 0.00018667938852129183, "补充知识点10103": 0.00018667938852129183, "补充知识点10104": 0.00018667938852129183, "图形算式问题": 0.00020742154280143537, "直接带入计算": 0.00020742154280143537, "补充知识点4247": 0.00020742154280143537, "破十法(十几减5、4、3、2)": 4.1484308560287074e-05, "拆分法理解破十法": 4.1484308560287074e-05, "补充知识点4243": 4.1484308560287074e-05, "十几减7、6的应用": 8.296861712057415e-05, "根据计算过程选图示": 8.296861712057415e-05, "补充知识点4188": 8.296861712057415e-05, "6的乘法口诀及应用": 0.0008296861712057415, "6的乘法口诀应用题": 0.0003941009313227272, "6的乘法的意义应用问题": 0.0003941009313227272, "补充知识点5270": 0.0003941009313227272, "8的乘法口诀及应用": 0.000850428325485885, "8的乘法应用题（移后删）": 0.0006015224741241625, "看图列乘法算式并用口诀求结果(8乘几)": 0.0006015224741241625, "补充知识点5321": 0.0006015224741241625, "根据所给数字组数写满足要求的算式": 0.00031113231420215305, "不包含0的情况": 0.00031113231420215305, "补充知识点7093": 0.00031113231420215305, "算式比大小（十几减8）": 6.222646284043061e-05, "补充知识点4157": 6.222646284043061e-05, "平十法（十几减8）": 2.0742154280143537e-05, "用平十法直接计算": 2.0742154280143537e-05, "补充知识点4158": 2.0742154280143537e-05, "破十法（十几减8）": 6.222646284043061e-05, "补充知识点4161": 6.222646284043061e-05, "想加算减(十几减8)": 2.0742154280143537e-05, "想加算减计算": 4.1484308560287074e-05, "补充知识点9067": 2.0742154280143537e-05, "补充知识点9068": 2.0742154280143537e-05, "简单间隔、周期规律": 0.00010371077140071768, "间隔问题(20以内)": 0.00010371077140071768, "问插空的物品的总数量": 0.00010371077140071768, "补充知识点23453": 0.00010371077140071768, "补充知识点23454": 0.00010371077140071768, "补充知识点23455": 0.00010371077140071768, "补充知识点23456": 0.00010371077140071768, "求原来(20以内)": 2.0742154280143537e-05, "补充知识点13979": 2.0742154280143537e-05, "补充知识点13980": 2.0742154280143537e-05, "1000以内数的比大小": 0.00024890585136172244, "数轴上表示数(1000以内)": 0.00020742154280143537, "根据数轴填数": 0.00020742154280143537, "补充知识点631": 0.00020742154280143537, "小数连加应用题": 0.00037335877704258366, "根据数量关系先求一个量，再求两个数量的和": 0.00037335877704258366, "补充知识点7278": 0.00037335877704258366, "用计算器计算小数加减法": 0.00024890585136172244, "用计算器计算小数加、减法": 0.00024890585136172244, "用计算器计算小数加减法的方法": 0.00024890585136172244, "补充知识点7336": 0.00024890585136172244, "包含整数的小数加减法": 0.00018667938852129183, "整数和小数的加法计算": 0.00018667938852129183, "补充知识点7209": 0.00018667938852129183, "找规律填数(1000以内)": 0.00035261662276244013, "连减(1000以内)": 0.00035261662276244013, "补充知识点23310": 0.00035261662276244013, "补充知识点23311": 0.00035261662276244013, "补充知识点23312": 0.00035261662276244013, "几百几十数乘一位数的口算(有进位)的应用": 0.0002903901599220095, "根据文字描述列式计算": 0.0008296861712057415, "补充知识点6140": 0.0002903901599220095, "两位数乘一位数的口算(有进位)的应用": 0.00012445292568086122, "补充知识点6044": 0.00012445292568086122, "猜猜我是谁（一位小数）": 4.1484308560287074e-05, "根据小数的组成猜小数": 4.1484308560287074e-05, "补充知识点2172": 4.1484308560287074e-05, "补充知识点2173": 4.1484308560287074e-05, "小小设计师": 0.0004563273941631578, "在格子图中设计轴对称图形": 0.0004563273941631578, "补充知识点20929": 0.0004563273941631578, "补充知识点20930": 0.0004563273941631578, "补充知识点20931": 0.0004563273941631578, "简单的数字找规律": 0.00010371077140071768, "找规律填数（复杂）-": 0.00010371077140071768, "含有百分数、分数、小数的找规律填数": 0.00010371077140071768, "补充知识点23922": 0.00010371077140071768, "补充知识点23923": 0.00010371077140071768, "补充知识点23924": 0.00010371077140071768, "等比数列": 6.222646284043061e-05, "极限思想": 6.222646284043061e-05, "利用极限思想求1与1/2开始的等比数列的差": 6.222646284043061e-05, "补充知识点23907": 6.222646284043061e-05, "补充知识点23908": 6.222646284043061e-05, "补充知识点23909": 6.222646284043061e-05, "从不同位置观察单个物体": 0.0004770695484433013, "从不同方向观察同一几何体(正、长方体)": 0.0001659372342411483, "从某个方向观察长/正方体，最多/少能看到几个面": 0.0001659372342411483, "补充知识点17920": 0.0001659372342411483, "补充知识点17921": 0.0001659372342411483, "需要添加几个小正方体": 0.0001659372342411483, "通过移动方块使从某个方向能看到正方形": 0.0001659372342411483, "补充知识点20033": 0.0001659372342411483, "补充知识点20034": 0.0001659372342411483, "用转化法解决分数计算问题（数形结合）": 4.1484308560287074e-05, "转化法": 2.0742154280143537e-05, "转化思想在几何图形公式推导中的应用": 2.0742154280143537e-05, "补充知识点8044": 2.0742154280143537e-05, "平面图上某点运动结合s-t图形求面积-": 2.0742154280143537e-05, "分析图像，描述过程": 2.0742154280143537e-05, "补充知识点22833": 2.0742154280143537e-05, "补充知识点22834": 2.0742154280143537e-05, "补充知识点22835": 2.0742154280143537e-05, "组数问题（除数是一位数的除法）": 0.0002903901599220095, "根据商的位数组数写除法算式": 0.0002903901599220095, "补充知识点24547": 0.0002903901599220095, "补充知识点24548": 0.0002903901599220095, "补充知识点24549": 0.0002903901599220095, "图形与位置": 8.296861712057415e-05, "用方向和距离描述物体位買和行走路线": 8.296861712057415e-05, "补充知识点21579": 8.296861712057415e-05, "补充知识点21580": 8.296861712057415e-05, "补充知识点21581": 8.296861712057415e-05, "方格图中的数对表示位置-": 8.296861712057415e-05, "用数对表示方格图中的点": 8.296861712057415e-05, "补充知识点21408": 8.296861712057415e-05, "补充知识点21409": 8.296861712057415e-05, "三角形面积公式的多种应用": 4.1484308560287074e-05, "运用面积公式直接计算": 4.1484308560287074e-05, "补充知识点17104": 4.1484308560287074e-05, "立体图形的体积": 2.0742154280143537e-05, "圆柱、圆锥的体积(含组合体)": 2.0742154280143537e-05, "补充知识点19520": 2.0742154280143537e-05, "补充知识点19521": 2.0742154280143537e-05, "根据从三个方向看到的图形推测几何组合体": 4.1484308560287074e-05, "根据从三个方向看到的图形，判断符合条件的几何体": 4.1484308560287074e-05, "补充知识点18117": 4.1484308560287074e-05, "加减混合运算解决实际问题（100以内）": 6.222646284043061e-05, "混合运算解决实际问题": 6.222646284043061e-05, "补充知识点4901": 6.222646284043061e-05, "加减法横式谜(100以内)": 0.0001659372342411483, "填数使连等式成立": 0.0001659372342411483, "补充知识点24706": 0.0001659372342411483, "补充知识点24707": 0.0001659372342411483, "补充知识点24708": 0.0001659372342411483, "图形中的等量代换": 0.00020742154280143537, "先求出一个图形，再代入求解": 0.00020742154280143537, "补充知识点10949": 0.00020742154280143537, "补充知识点10950": 0.00020742154280143537, "补充知识点10951": 0.00020742154280143537, "一面靠墙的梯形的面积问题": 0.00020742154280143537, "已知总长和高，求面积": 0.00020742154280143537, "补充知识点16715": 0.00020742154280143537, "运用多种方法求平行四边形的面积": 4.1484308560287074e-05, "运用平行四边形面积公式直接计算": 4.1484308560287074e-05, "补充知识点16560": 4.1484308560287074e-05, "认识常见的人民币": 0.0001659372342411483, "认识人民币的单位": 0.0001659372342411483, "补充知识点13514": 0.0001659372342411483, "补充知识点13515": 0.0001659372342411483, "植树问题（两端都栽）": 0.00035261662276244013, "植树问题(两端种)": 0.00035261662276244013, "两端都种求棵树(不限情境)": 0.00035261662276244013, "补充知识点15089": 0.00035261662276244013, "钟面上角的计算问题": 0.00031113231420215305, "钟面上角度的认识": 0.00031113231420215305, "补充知识点25985": 0.00031113231420215305, "补充知识点25986": 0.00031113231420215305, "补充知识点25987": 0.00031113231420215305, "角的大小比较(涉及角的度数)": 0.00010371077140071768, "角的大小规律": 0.00010371077140071768, "补充知识点15821": 0.00010371077140071768, "补充知识点15822": 0.00010371077140071768, "四边形的分类及关系": 0.00014519507996100476, "四边形之间的关系": 0.00014519507996100476, "考察梯形与平行四边形的关系": 0.00014519507996100476, "补充知识点15499": 0.00014519507996100476, "补充知识点15500": 0.00014519507996100476, "垂直的特征及性质": 0.0004563273941631578, "平行与垂直的综合应用": 2.0742154280143537e-05, "一条直线与一组平行线相交形成的角的关系": 2.0742154280143537e-05, "补充知识点15625": 2.0742154280143537e-05, "补充知识点15626": 2.0742154280143537e-05, "和差问题": 6.222646284043061e-05, "和差问题（100以内数）": 6.222646284043061e-05, "由和差解决实际问题": 6.222646284043061e-05, "补充知识点25779": 6.222646284043061e-05, "补充知识点25780": 6.222646284043061e-05, "补充知识点25781": 6.222646284043061e-05, "求比中的未知项": 4.1484308560287074e-05, "求比的前项或后项中未知数的值": 4.1484308560287074e-05, "补充知识点11845": 4.1484308560287074e-05, "补充知识点11846": 4.1484308560287074e-05, "整百数乘整十数的口算": 2.0742154280143537e-05, "整百数乘整十数的口算算理": 2.0742154280143537e-05, "补充知识点6624": 2.0742154280143537e-05, "笔算两位数减一位数(退位)": 4.1484308560287074e-05, "探究竖式算法，理解算理": 4.1484308560287074e-05, "补充知识点4632": 4.1484308560287074e-05, "笔算两位数加两位数(不进位加)的应用": 0.00020742154280143537, "看图列竖式": 0.00020742154280143537, "补充知识点4530": 0.00020742154280143537, "点子图与乘法算式": 2.0742154280143537e-05, "用点子图表示乘法": 2.0742154280143537e-05, "几个几相加是多少": 2.0742154280143537e-05, "补充知识点5175": 2.0742154280143537e-05, "已知售价和利润率，求成本": 8.296861712057415e-05, "已知利润和利润率，求成本": 8.296861712057415e-05, "补充知识点8811": 8.296861712057415e-05, "补充知识点8812": 8.296861712057415e-05, "解决增减幅度一致的问题(百分数)": 4.1484308560287074e-05, "一个量的增减分率一致，判断结果(先增后减)": 4.1484308560287074e-05, "补充知识点8576": 4.1484308560287074e-05, "补充知识点8577": 4.1484308560287074e-05, "运用比的基本性质解决比值变化的问题": 4.1484308560287074e-05, "比的前项变，后项不变": 4.1484308560287074e-05, "补充知识点11757": 4.1484308560287074e-05, "补充知识点11758": 4.1484308560287074e-05, "谁比谁多/少几分之几的理解-": 8.296861712057415e-05, "找出单位1，求两量关系-": 8.296861712057415e-05, "补充知识点2526": 8.296861712057415e-05, "看图求多倍量(2~5)": 2.0742154280143537e-05, "补充知识点10001": 2.0742154280143537e-05, "补充知识点10002": 2.0742154280143537e-05, "运算律与简便运算": 6.222646284043061e-05, "加减运算的简便运算": 6.222646284043061e-05, "补充知识点9879": 6.222646284043061e-05, "补充知识点9880": 6.222646284043061e-05, "四则运算的意义和计算方法": 0.0009126547883263156, "整数四则混合计算": 0.0009126547883263156, "补充知识点8412": 0.0009126547883263156, "补充知识点8413": 0.0009126547883263156, "含有小括号的表内混合运算的运算顺序": 0.00035261662276244013, "补充知识点9303": 0.00035261662276244013, "补充知识点9304": 0.00035261662276244013, "根据分步运算列综合算式(含括号)(表内混合运算)": 0.0003318744684822966, "将两个算式合并为综合算式(含括号)": 0.0003318744684822966, "补充知识点6919": 0.0003318744684822966, "比较型推理问题(有数量)": 4.1484308560287074e-05, "根据信息判断准确数值(有数量)": 4.1484308560287074e-05, "补充知识点14811": 4.1484308560287074e-05, "补充知识点14812": 4.1484308560287074e-05, "两位数除以一位数的有余数除法": 0.00012445292568086122, "两位数除以一位数的笔算(有余数)的应用": 2.0742154280143537e-05, "余数相同的填数问题": 2.0742154280143537e-05, "补充知识点5953": 2.0742154280143537e-05, "除数是两位数的估算": 0.00018667938852129183, "两位数除三位数的估算方法": 0.00010371077140071768, "直接估算": 0.00010371077140071768, "补充知识点7153": 0.00010371077140071768, "口算两位数减一位数(不退位)的应用": 0.00018667938852129183, "补充知识点4478": 0.00018667938852129183, "根据分数基本性质还原分数": 0.00020742154280143537, "已知分子分母和及分数值，求原分数": 0.00020742154280143537, "补充知识点2977": 0.00020742154280143537, "补充知识点2978": 0.00020742154280143537, "认读时间(几时几分)": 0.00041484308560287074, "看钟面，写时间(几时几分)": 0.00041484308560287074, "补充知识点12997": 0.00041484308560287074, "等式的认识及列等量关系式": 2.0742154280143537e-05, "等量关系与方程9": 2.0742154280143537e-05, "根据题意写等量关系9": 2.0742154280143537e-05, "补充知识点10871": 2.0742154280143537e-05, "补充知识点10872": 2.0742154280143537e-05, "补充知识点10873": 2.0742154280143537e-05, "数轴与分数-": 0.00014519507996100476, "将分数标到数轴上-": 0.00014519507996100476, "补充知识点2524": 0.00014519507996100476, "同分子分数的大小比较": 0.00014519507996100476, "分数比大小综合": 0.00014519507996100476, "分母相同分数比大小时分子填空": 0.00014519507996100476, "补充知识点2605": 0.00014519507996100476, "单位1\"的意义\"": 0.0001659372342411483, "认识单位1\"\"": 0.0001659372342411483, "补充知识点2562": 0.0001659372342411483, "两位数除以一位数(被除数首位能被整除)的应用": 0.00018667938852129183, "竖式中某一部分表示的含义": 0.00018667938852129183, "补充知识点6291": 0.00018667938852129183, "分数、小数综合推理问题-": 0.00014519507996100476, "求分数（知分子与分母和，化成的小数值）-": 0.00014519507996100476, "补充知识点3241": 0.00014519507996100476, "补充知识点3242": 0.00014519507996100476, "含字母的分数比大小-": 6.222646284043061e-05, "比较字母组成分数的大小（知字母大小关系）": 6.222646284043061e-05, "补充知识点2670": 6.222646284043061e-05, "含字母的分数通分中的推理问题-": 0.00018667938852129183, "知两分数通分后的分数，求字母间关系-": 0.00018667938852129183, "补充知识点3089": 0.00018667938852129183, "补充知识点3090": 0.00018667938852129183, "约分后比较分数大小-": 0.00020742154280143537, "先约分再比大小-": 0.00020742154280143537, "补充知识点2667": 0.00020742154280143537, "与公因数/最大公因数有关的推理-": 0.0001659372342411483, "求最大公因数（知两数和、两数倍数）-": 0.0001659372342411483, "补充知识点1483": 0.0001659372342411483, "求分数与除法的实际问题（结果用假/带分数表示）-": 0.00024890585136172244, "求每份数-": 0.00024890585136172244, "补充知识点2881": 0.00024890585136172244, "补充知识点2882": 0.00024890585136172244, "用所给数字组真、假、带分数-": 0.00010371077140071768, "组成最大、最小分数（真、假分数）-": 0.00010371077140071768, "补充知识点2887": 0.00010371077140071768, "补充知识点2888": 0.00010371077140071768, "分解质因数及其应用": 0.00010371077140071768, "分解质因数的意义": 0.00010371077140071768, "补充知识点1441": 0.00010371077140071768, "用最小公倍数解决拼瓷砖问题": 0.00018667938852129183, "按要求拼成正方形，求边长": 0.00018667938852129183, "补充知识点1592": 0.00018667938852129183, "运用四舍五入\"法解决问题\"": 4.1484308560287074e-05, "省略其他数位后面的尾数求近似数(一)": 4.1484308560287074e-05, "补充知识点966": 4.1484308560287074e-05, "数的组成(万以上)": 2.0742154280143537e-05, "补充知识点750": 2.0742154280143537e-05, "封闭型数阵图(100以内)": 4.1484308560287074e-05, "一条直线": 4.1484308560287074e-05, "补充知识点24844": 4.1484308560287074e-05, "补充知识点24845": 4.1484308560287074e-05, "补充知识点24846": 4.1484308560287074e-05, "口算整十数加、减整十数": 6.222646284043061e-05, "补充知识点4408": 6.222646284043061e-05, "先写数再比较大小(100以内)": 0.00014519507996100476, "看图先写数，再比较大小": 0.00014519507996100476, "补充知识点459": 0.00014519507996100476, "加小括号的综合问题(表内混合运算)": 0.0003318744684822966, "根据给定运算顺序加小括号": 0.0003318744684822966, "补充知识点9341": 0.0003318744684822966, "补充知识点9342": 0.0003318744684822966, "自然数的运用": 0.00014519507996100476, "补充知识点1019": 0.00014519507996100476, "按数的特点分类(100以内)": 0.00014519507996100476, "数的组成": 0.00014519507996100476, "补充知识点509": 0.00014519507996100476, "求一共（20以内）": 0.0001659372342411483, "两种思路求一共": 0.0001659372342411483, "补充知识点13955": 0.0001659372342411483, "补充知识点13956": 0.0001659372342411483, "填算符": 4.1484308560287074e-05, "根据结果填算符(10以内)": 2.0742154280143537e-05, "补充知识点24178": 2.0742154280143537e-05, "补充知识点24179": 2.0742154280143537e-05, "补充知识点24180": 2.0742154280143537e-05, "按要求分图形": 0.0001659372342411483, "分成两个相同的图形": 0.0001659372342411483, "补充知识点19786": 0.0001659372342411483, "补充知识点19787": 0.0001659372342411483, "补充知识点19788": 0.0001659372342411483, "七巧板的变形问题（如四巧板）": 2.0742154280143537e-05, "已知板块，找拼成的图形": 2.0742154280143537e-05, "补充知识点25575": 2.0742154280143537e-05, "补充知识点25576": 2.0742154280143537e-05, "加小括号改变运算顺序问题": 8.296861712057415e-05, "直接添加小括号不需要调位置": 8.296861712057415e-05, "补充知识点9349": 8.296861712057415e-05, "补充知识点9350": 8.296861712057415e-05, "归一问题": 6.222646284043061e-05, "运用数形结合法解决简单的归一问题": 6.222646284043061e-05, "被除数是两位数的归一问题": 6.222646284043061e-05, "补充知识点14083": 6.222646284043061e-05, "补充知识点14084": 6.222646284043061e-05, "从不同方向观察同一物体": 2.0742154280143537e-05, "辨认从不同方向看到的物体形状(一)": 2.0742154280143537e-05, "补充知识点17928": 2.0742154280143537e-05, "补充知识点17929": 2.0742154280143537e-05, "含字母的同分母分数加减推理问题-": 0.00012445292568086122, "求分母中的字母值（知运算结果）": 0.00012445292568086122, "补充知识点7860": 0.00012445292568086122, "分数基本性质解决含字母的分数问题-": 8.296861712057415e-05, "求字母的数值-": 8.296861712057415e-05, "补充知识点3009": 8.296861712057415e-05, "补充知识点3010": 8.296861712057415e-05, "结果需约分的实际应用-": 0.00035261662276244013, "求分率（直接计算）-": 0.00035261662276244013, "补充知识点3055": 0.00035261662276244013, "补充知识点3056": 0.00035261662276244013, "错中求解推理分数-": 6.222646284043061e-05, "求原分数（错将加看成减）-": 6.222646284043061e-05, "补充知识点3149": 6.222646284043061e-05, "补充知识点3150": 6.222646284043061e-05, "推理法求假分数-": 4.1484308560287074e-05, "求假分数（知假分数分子+所化带分数三部分是连续自然数）": 4.1484308560287074e-05, "补充知识点2903": 4.1484308560287074e-05, "补充知识点2904": 4.1484308560287074e-05, "百僧分馍\"问题\"": 2.0742154280143537e-05, "基础百僧分馍\"问题\"": 2.0742154280143537e-05, "补充知识点15048": 2.0742154280143537e-05, "两位数除以一位数的口算除法的应用": 0.00041484308560287074, "补充知识点6253": 0.00041484308560287074, "平均分的综合理解与应用": 0.0004563273941631578, "类似碗和筷子成套配对问题": 0.0004563273941631578, "补充知识点5516": 0.0004563273941631578, "减法应用题(不退位)(100以内)": 4.1484308560287074e-05, "看图列竖式计算": 0.00012445292568086122, "补充知识点4573": 4.1484308560287074e-05, "解决两位数乘一位数的实际问题": 2.0742154280143537e-05, "两位数乘一位数不进位简单应用": 2.0742154280143537e-05, "补充知识点6026": 2.0742154280143537e-05, "稍复杂行程问题": 4.1484308560287074e-05, "火车过桥问题": 2.0742154280143537e-05, "两量未知的火车过桥问题（知桥长、过桥时间）-": 2.0742154280143537e-05, "补充知识点14367": 2.0742154280143537e-05, "认识小数点移动的规律": 0.00024890585136172244, "探究小数点的移动引起小数大小的变化规律": 0.00024890585136172244, "补充知识点2194": 0.00024890585136172244, "补充知识点2195": 0.00024890585136172244, "根据小数的意义进行单位换算(元角分/长度/质量)": 0.0002903901599220095, "根据小数的意义进行单位换算(填小数)": 0.0002903901599220095, "补充知识点1840": 0.0002903901599220095, "补充知识点1841": 0.0002903901599220095, "平面图形的旋转（圆柱、圆锥、组合体...）": 4.1484308560287074e-05, "平面图形旋转成体（连线）": 4.1484308560287074e-05, "补充知识点19214": 4.1484308560287074e-05, "补充知识点19215": 4.1484308560287074e-05, "圆柱的容积": 4.1484308560287074e-05, "认识圆柱的容积、体积": 4.1484308560287074e-05, "认识容积-": 4.1484308560287074e-05, "补充知识点19204": 4.1484308560287074e-05, "补充知识点19205": 4.1484308560287074e-05, "利率、税率综合的实际应用-": 2.0742154280143537e-05, "利率、税率综合问题-": 2.0742154280143537e-05, "求最低营业额（知贷款额、利率、时间+营业额分配情况）-": 2.0742154280143537e-05, "补充知识点8758": 2.0742154280143537e-05, "求本金": 8.296861712057415e-05, "购房贷款问题中，求实际每平米价格-": 8.296861712057415e-05, "补充知识点8750": 8.296861712057415e-05, "免税额度-": 2.0742154280143537e-05, "求免税额度-": 2.0742154280143537e-05, "求免税额度（知总费用、税率、纳税额）-": 2.0742154280143537e-05, "补充知识点8733": 2.0742154280143537e-05, "利润和成数问题-": 2.0742154280143537e-05, "利润和成数综合求定价-": 2.0742154280143537e-05, "求每个定价（知进价+量及减少成数+利润率）": 2.0742154280143537e-05, "补充知识点8793": 2.0742154280143537e-05, "表内乘法、除法解决问题（1-6）": 0.00024890585136172244, "根据算式选题目": 0.00024890585136172244, "补充知识点5543": 0.00024890585136172244, "推理解决与正负数有关的复杂问题-": 4.1484308560287074e-05, "推理破译密码中的问题-": 4.1484308560287074e-05, "补充知识点10090": 4.1484308560287074e-05, "补充知识点10091": 4.1484308560287074e-05, "补充知识点10092": 4.1484308560287074e-05, "加法、乘法交换律和结合律的综合应用": 0.00020742154280143537, "加法交换律与乘法交换律的对比探究": 0.00020742154280143537, "补充知识点9569": 0.00020742154280143537, "补充知识点9570": 0.00020742154280143537, "巧求尾同头合十\"的两位数乘两位数\"": 2.0742154280143537e-05, "尾同头合十巧算(不需要0占位)": 2.0742154280143537e-05, "补充知识点23530": 2.0742154280143537e-05, "补充知识点23531": 2.0742154280143537e-05, "补充知识点23532": 2.0742154280143537e-05, "笔算(连减)解决实际问题": 0.00010371077140071768, "根据题意只列式不计算": 0.00010371077140071768, "补充知识点4813": 0.00010371077140071768, "等量代换比轻重": 2.0742154280143537e-05, "多个物体比轻重": 2.0742154280143537e-05, "补充知识点105": 2.0742154280143537e-05, "带括号的四则混合运算中的趣味题目": 0.00018667938852129183, "添加运算符号和括号满足结果": 0.00018667938852129183, "补充知识点24106": 0.00018667938852129183, "补充知识点24107": 0.00018667938852129183, "补充知识点24108": 0.00018667938852129183, "笔算两位数加一位数(进位)的应用": 4.1484308560287074e-05, "填十位上的数": 4.1484308560287074e-05, "补充知识点4612": 4.1484308560287074e-05, "乘、除法各部分间的关系的最值问题": 0.00012445292568086122, "被除数最小是多少": 0.00012445292568086122, "补充知识点9115": 0.00012445292568086122, "补充知识点9116": 0.00012445292568086122, "购物问题(100以内)": 8.296861712057415e-05, "补充知识点4920": 4.1484308560287074e-05, "两位数减两位数(不退位)解决实际问题": 0.00012445292568086122, "两位数减两位数（不退位）的简单实际应用": 0.00012445292568086122, "补充知识点4561": 0.00012445292568086122, "运用最小公倍数解决植树问题": 0.00018667938852129183, "已知总长和间距，求不需要移动路灯数量": 0.00018667938852129183, "补充知识点1597": 0.00018667938852129183, "没有涂色": 2.0742154280143537e-05, "求没有涂色的小正方体的数量(几何体靠墙)": 2.0742154280143537e-05, "补充知识点18770": 2.0742154280143537e-05, "补充知识点18771": 2.0742154280143537e-05, "一面涂色": 2.0742154280143537e-05, "挖掉若干小正方体后，求一面涂色的小正方体的数量": 2.0742154280143537e-05, "补充知识点18780": 2.0742154280143537e-05, "补充知识点18781": 2.0742154280143537e-05, "三面涂色": 2.0742154280143537e-05, "挖掉若干小正方体后，求三面涂色的小正方体的数量": 2.0742154280143537e-05, "补充知识点18774": 2.0742154280143537e-05, "补充知识点18775": 2.0742154280143537e-05, "估算不规则图形的面积": 0.0001659372342411483, "比较格子图中不规则图形面积大小": 0.0001659372342411483, "补充知识点17848": 0.0001659372342411483, "补充知识点17849": 0.0001659372342411483, "补充知识点17850": 0.0001659372342411483, "除数是整数，商小于1的小数除法": 2.0742154280143537e-05, "整数除以整数商是小数的除法9": 2.0742154280143537e-05, "列竖式计算除数是整数的小数除法": 2.0742154280143537e-05, "补充知识点7530": 2.0742154280143537e-05, "长方体体积的复杂综合应用-": 4.1484308560287074e-05, "求容器侧面出现正方形次数及容器中水的体积（向棱长已知的长方体倒水过程）-": 4.1484308560287074e-05, "补充知识点18638": 4.1484308560287074e-05, "补充知识点18639": 4.1484308560287074e-05, "两数求和(100以內)": 6.222646284043061e-05, "3个数中选两个数求和(含0)": 6.222646284043061e-05, "补充知识点14675": 6.222646284043061e-05, "补充知识点14676": 6.222646284043061e-05, "方案问题(最优解)": 0.00012445292568086122, "购物价钱最优解问题": 0.00012445292568086122, "补充知识点14995": 0.00012445292568086122, "方案问题(枚举全部方案)": 0.00020742154280143537, "租船、租车枚举方案": 0.00020742154280143537, "补充知识点14620": 0.00020742154280143537, "与量的交换、添加、减少有关的求比问题-": 4.1484308560287074e-05, "不等化等问题中求原来量的比": 4.1484308560287074e-05, "补充知识点12107": 4.1484308560287074e-05, "补充知识点12108": 4.1484308560287074e-05, "求图形放大/缩小后量之比或倍数（边长/周长/面积/体积）-": 0.00018667938852129183, "求变化前后的量之比综合（知比）-": 0.00018667938852129183, "补充知识点12888": 0.00018667938852129183, "补充知识点12889": 0.00018667938852129183, "用比例方程解决行程问题-": 2.0742154280143537e-05, "求两地相距距离（知相遇时两车路程比+甲车行全程时间+乙车速度）": 2.0742154280143537e-05, "补充知识点12363": 2.0742154280143537e-05, "补充知识点12364": 2.0742154280143537e-05, "列方程解决差倍问题\"（ax-bx=c）\"": 0.00018667938852129183, "解决几倍多几的差倍问题": 0.0002281636970815789, "补充知识点11426": 0.00018667938852129183, "补充知识点11427": 0.00018667938852129183, "补充知识点11428": 0.00018667938852129183, "看图列方程-": 2.0742154280143537e-05, "列与平面图形面积/周长有关的方程": 2.0742154280143537e-05, "补充知识点10999": 2.0742154280143537e-05, "补充知识点11000": 2.0742154280143537e-05, "方程的解": 4.1484308560287074e-05, "方程解有关的问题-": 4.1484308560287074e-05, "判断方程解的说法": 4.1484308560287074e-05, "补充知识点11275": 4.1484308560287074e-05, "补充知识点11276": 4.1484308560287074e-05, "列方程解百分数应用题": 0.00020742154280143537, "用方程解简单的百分数应用题": 0.00020742154280143537, "补充知识点11255": 0.00020742154280143537, "补充知识点11256": 0.00020742154280143537, "梯形面积的计算": 8.296861712057415e-05, "梯形面积公式逆用-": 4.1484308560287074e-05, "求梯形高（知上下底和面积）": 4.1484308560287074e-05, "补充知识点16678": 4.1484308560287074e-05, "比较型推理问题(三量)": 0.00014519507996100476, "三者比较(匹配一个结论)": 0.00014519507996100476, "补充知识点14795": 0.00014519507996100476, "补充知识点14796": 0.00014519507996100476, "两位数乘整百数的口算及应用": 0.00014519507996100476, "两位数乘整百数的口算": 0.00014519507996100476, "补充知识点6521": 0.00014519507996100476, "两位数乘一位数的口算（不进位）的应用": 6.222646284043061e-05, "实际情境竖式中每一步的含义": 6.222646284043061e-05, "补充知识点6050": 6.222646284043061e-05, "比大小（一位小数加减法）": 2.0742154280143537e-05, "算式中有图文的大小比较": 2.0742154280143537e-05, "补充知识点14055": 2.0742154280143537e-05, "补充知识点14056": 2.0742154280143537e-05, "由算式填合适的数（一位小数）": 2.0742154280143537e-05, "由算式填合适的数": 2.0742154280143537e-05, "补充知识点24814": 2.0742154280143537e-05, "补充知识点24815": 2.0742154280143537e-05, "补充知识点24816": 2.0742154280143537e-05, "数轴上表示数(万以内)": 0.0002281636970815789, "猜数": 0.0002281636970815789, "补充知识点695": 0.0002281636970815789, "用字母表示数量关系（a±b）÷X或a÷x±b÷x": 4.1484308560287074e-05, "用字母表示减除相关的问题-": 4.1484308560287074e-05, "补充知识点10798": 4.1484308560287074e-05, "补充知识点10799": 4.1484308560287074e-05, "用字母表示立体图形中的量-": 0.00010371077140071768, "用字母表示长/正方体中的量（简单）-": 0.00010371077140071768, "补充知识点10792": 0.00010371077140071768, "补充知识点10793": 0.00010371077140071768, "根据时间表解决简单的实际问题": 0.00010371077140071768, "根据作息时间表求经过的时间": 0.00010371077140071768, "补充知识点13224": 0.00010371077140071768, "表内除法竖式": 4.1484308560287074e-05, "表内除法竖式(有竖式图)": 4.1484308560287074e-05, "补充知识点5905": 4.1484308560287074e-05, "与百分数有关的求总量问题-": 4.1484308560287074e-05, "百分数有关的求总量问题-": 4.1484308560287074e-05, "求总量（知某次所占分率+另一次量+两次所占总百分率）": 4.1484308560287074e-05, "补充知识点8630": 4.1484308560287074e-05, "补充知识点8631": 4.1484308560287074e-05, "补充知识点25839": 4.1484308560287074e-05, "补充知识点25840": 4.1484308560287074e-05, "补充知识点25841": 4.1484308560287074e-05, "除数是两位数的笔算除法": 0.0011823027939681814, "用三位数除以两位数(商是两位数)解决简单的实际问题": 0.0009748812511667462, "三位数除以两位数计算并比较问题": 0.0009748812511667462, "补充知识点6754": 0.0009748812511667462, "解决三位数乘两位数(因数末尾有0)实际问题": 0.00012445292568086122, "解决需要转化数量的实际问题": 0.0002903901599220095, "补充知识点6672": 0.00012445292568086122, "与百分数有关的补充条件/问题": 8.296861712057415e-05, "补充条件列算式（知其中一量+求另一量）": 8.296861712057415e-05, "补充知识点8891": 8.296861712057415e-05, "补充知识点8892": 8.296861712057415e-05, "应用商不变的规律进行简便计算": 8.296861712057415e-05, "运用商不变的规律计算整除的除法": 8.296861712057415e-05, "补充知识点10359": 8.296861712057415e-05, "补充知识点10360": 8.296861712057415e-05, "补充知识点10361": 8.296861712057415e-05, "一个因素变化的规律的应用": 8.296861712057415e-05, "根据规律填一填": 8.296861712057415e-05, "补充知识点10254": 8.296861712057415e-05, "补充知识点10255": 8.296861712057415e-05, "补充知识点10256": 8.296861712057415e-05, "小数乘法的估算-": 2.0742154280143537e-05, "找出与所给算式结果相近的算式": 2.0742154280143537e-05, "补充知识点7686": 2.0742154280143537e-05, "补充知识点7687": 2.0742154280143537e-05, "三位数除以两位数的笔算": 4.1484308560287074e-05, "三位数除以两位数的计算方法(一)": 4.1484308560287074e-05, "补充知识点6747": 4.1484308560287074e-05, "趣题巧解": 4.1484308560287074e-05, "蜗牛爬井": 2.0742154280143537e-05, "周期性运动中的临界分析": 2.0742154280143537e-05, "补充知识点26314": 2.0742154280143537e-05, "补充知识点26315": 2.0742154280143537e-05, "补充知识点26316": 2.0742154280143537e-05, "运用三位数除以一位数的笔算(每一位都能整除)解决问题": 0.00010371077140071768, "搭配组合中的最多": 0.00010371077140071768, "补充知识点6327": 0.00010371077140071768, "够不够(一步)": 0.00010371077140071768, "补充知识点6951": 0.00010371077140071768, "百分数化小数": 4.1484308560287074e-05, "补充知识点3363": 2.0742154280143537e-05, "补充知识点3364": 2.0742154280143537e-05, "画垂线": 0.0002281636970815789, "画垂线的特点/垂线的基本性质": 0.0002281636970815789, "补充知识点15681": 0.0002281636970815789, "补充知识点15682": 0.0002281636970815789, "运用三位数除以一位数的笔算解决问题（有余数）": 0.00018667938852129183, "已知余多少，求每份是多少": 0.00018667938852129183, "补充知识点5964": 0.00018667938852129183, "两位数除以一位数的笔算(有余数)解决实际问题": 6.222646284043061e-05, "还需多少可以平均分": 6.222646284043061e-05, "补充知识点5956": 6.222646284043061e-05, "算盘上拨数(10000以内)": 6.222646284043061e-05, "补充知识点10527": 6.222646284043061e-05, "补充知识点10528": 6.222646284043061e-05, "补充知识点10529": 6.222646284043061e-05, "算盘表示数(10000以内)": 2.0742154280143537e-05, "补充知识点10515": 2.0742154280143537e-05, "补充知识点10516": 2.0742154280143537e-05, "补充知识点10517": 2.0742154280143537e-05, "先组数，再分解质因数": 6.222646284043061e-05, "补充知识点1435": 6.222646284043061e-05, "倍的含义": 0.00012445292568086122, "在圈一圈中认识倍": 0.00012445292568086122, "补充知识点1610": 0.00012445292568086122, "补充知识点1611": 0.00012445292568086122, "补充知识点1612": 0.00012445292568086122, "与三位数乘一位数有关的填数问题": 8.296861712057415e-05, "三位数乘一位数填一位数": 8.296861712057415e-05, "补充知识点24502": 8.296861712057415e-05, "补充知识点24503": 8.296861712057415e-05, "补充知识点24504": 8.296861712057415e-05, "生活中的旋转": 0.0001659372342411483, "还原图片旋转前的样子": 0.0001659372342411483, "补充知识点20566": 0.0001659372342411483, "补充知识点20567": 0.0001659372342411483, "补充知识点20568": 0.0001659372342411483, "旋转中转盘问题": 0.00014519507996100476, "指针顺/逆时针转动1次，找最终位置": 0.00014519507996100476, "补充知识点20680": 0.00014519507996100476, "补充知识点20681": 0.00014519507996100476, "补充知识点20682": 0.00014519507996100476, "求所剩图形的面积与周长问题": 8.296861712057415e-05, "剪掉一部分后剩下部分的面积": 8.296861712057415e-05, "补充知识点17695": 8.296861712057415e-05, "补充知识点17696": 8.296861712057415e-05, "补充知识点17697": 8.296861712057415e-05, "不规则图形的拼接问题": 2.0742154280143537e-05, "拼接后，周长和面积的变化": 2.0742154280143537e-05, "补充知识点19921": 2.0742154280143537e-05, "补充知识点19922": 2.0742154280143537e-05, "补充知识点19923": 2.0742154280143537e-05, "两数比大小(1000以内)": 4.1484308560287074e-05, "比大小(1000以内)": 4.1484308560287074e-05, "补充知识点634": 4.1484308560287074e-05, "表中两量的不同比例关系-": 0.0001659372342411483, "由两量不同比例关系，求值": 0.0001659372342411483, "补充知识点12321": 0.0001659372342411483, "补充知识点12322": 0.0001659372342411483, "由题中数据探究比例关系（正比例）-": 0.0001659372342411483, "探究比例关系、并列式（填空）-": 0.00020742154280143537, "补充知识点12450": 0.0001659372342411483, "补充知识点12451": 0.0001659372342411483, "比例尺更该后图上/实际距离的计算-": 0.00010371077140071768, "知物在一个比例尺中的长度，求其在另一比例尺中的长度-": 0.00010371077140071768, "补充知识点12836": 0.00010371077140071768, "补充知识点12837": 0.00010371077140071768, "用所给数字组相等的分数-": 4.1484308560287074e-05, "所给范围内选数组相等分数-": 4.1484308560287074e-05, "补充知识点3029": 4.1484308560287074e-05, "补充知识点3030": 4.1484308560287074e-05, "反比例图象的特点": 4.1484308560287074e-05, "补充知识点12409": 4.1484308560287074e-05, "补充知识点12410": 4.1484308560287074e-05, "正方体侧面积": 2.0742154280143537e-05, "求侧面积（知棱长）-": 2.0742154280143537e-05, "补充知识点18531": 2.0742154280143537e-05, "三位数除以两位数(商是两位数)的笔算除法": 0.0001659372342411483, "商是两位数的笔算除法竖式(商的个位非0)": 0.0001659372342411483, "补充知识点6742": 0.0001659372342411483, "乘法表": 0.00012445292568086122, "正方形数": 6.222646284043061e-05, "数形结合求正方形数": 6.222646284043061e-05, "补充知识点5468": 6.222646284043061e-05, "8的乘法口诀（移后删）": 0.00024890585136172244, "根据8的乘法口诀计算": 0.00024890585136172244, "补充知识点5328": 0.00024890585136172244, "逻辑推理题": 6.222646284043061e-05, "根据相对位置推理具体的位置": 6.222646284043061e-05, "补充知识点21200": 6.222646284043061e-05, "补充知识点21201": 6.222646284043061e-05, "两位数除以一位数(有余数)的笔算": 4.1484308560287074e-05, "两位数除以一位数的计算算理": 4.1484308560287074e-05, "补充知识点5948": 4.1484308560287074e-05, "平行四边形的概念及特点": 0.00031113231420215305, "生活中的平行四边形": 4.1484308560287074e-05, "判断平行四边形": 0.00018667938852129183, "补充知识点16499": 4.1484308560287074e-05, "补充知识点16500": 4.1484308560287074e-05, "人民币中的移多补少": 4.1484308560287074e-05, "给多少后一样多": 4.1484308560287074e-05, "补充知识点13510": 4.1484308560287074e-05, "补充知识点13511": 4.1484308560287074e-05, "移多补少(100以内退位)": 6.222646284043061e-05, "补充知识点24061": 6.222646284043061e-05, "补充知识点24062": 6.222646284043061e-05, "补充知识点24063": 6.222646284043061e-05, "十几减几的不退位减法解决实际问题": 4.1484308560287074e-05, "已知一共和部分，求另一部分": 4.1484308560287074e-05, "补充知识点4095": 4.1484308560287074e-05, "被减数、减数和差中的规律": 0.00014519507996100476, "被减数一定，减数变化的规律": 0.00014519507996100476, "补充知识点8921": 0.00014519507996100476, "补充知识点8922": 0.00014519507996100476, "十几减8的计算方法": 0.00012445292568086122, "补充知识点4155": 0.00012445292568086122, "图形找规律(形状、颜色)": 8.296861712057415e-05, "图形成组重复出现(继续填图形)": 8.296861712057415e-05, "补充知识点23372": 8.296861712057415e-05, "补充知识点23373": 8.296861712057415e-05, "补充知识点23374": 8.296861712057415e-05, "化简比(小数)": 0.00010371077140071768, "直接化简小数比": 0.00010371077140071768, "补充知识点11789": 0.00010371077140071768, "补充知识点11790": 0.00010371077140071768, "正方形的剪拼": 4.1484308560287074e-05, "用小正方形拼大正方形": 4.1484308560287074e-05, "补充知识点19909": 4.1484308560287074e-05, "补充知识点19910": 4.1484308560287074e-05, "补充知识点19911": 4.1484308560287074e-05, "根据反比例关系计算": 4.1484308560287074e-05, "已有数据判断反比例关系，再计算（删不了）": 4.1484308560287074e-05, "补充知识点12285": 4.1484308560287074e-05, "补充知识点12286": 4.1484308560287074e-05, "画扇形": 2.0742154280143537e-05, "画扇形-": 2.0742154280143537e-05, "先画圆再画扇形（知圆的周长+扇形圆心角）-": 2.0742154280143537e-05, "补充知识点17583": 2.0742154280143537e-05, "三角形面积计算公式的推导": 4.1484308560287074e-05, "通过割补法计算三角形的面积基础": 4.1484308560287074e-05, "补充知识点17075": 4.1484308560287074e-05, "梯形面积计算公式的推导": 4.1484308560287074e-05, "通过割补法推导梯形面积的方法": 4.1484308560287074e-05, "补充知识点16669": 4.1484308560287074e-05, "平行四边形面积计算公式的推导": 6.222646284043061e-05, "利用割补法推算平行四边形面积": 6.222646284043061e-05, "补充知识点16552": 6.222646284043061e-05, "单式折线、单式条形统计图对比-": 0.00012445292568086122, "完善折线、条形统计图": 0.00012445292568086122, "补充知识点22426": 0.00012445292568086122, "补充知识点22427": 0.00012445292568086122, "解小数方程": 8.296861712057415e-05, "等式性质1和等式性质2解小数方程": 4.1484308560287074e-05, "补充知识点11169": 4.1484308560287074e-05, "补充知识点11170": 4.1484308560287074e-05, "推理法求真分数-": 0.0001659372342411483, "求真分数（知真分数加减它的一个分数单位后的值）": 0.0001659372342411483, "补充知识点2907": 0.0001659372342411483, "补充知识点2908": 0.0001659372342411483, "分数与单位换算（假/带）-": 0.00010371077140071768, "小单位化大单位，填带分数-": 0.00010371077140071768, "补充知识点2849": 0.00010371077140071768, "补充知识点2850": 0.00010371077140071768, "求税前额": 0.00010371077140071768, "税前收入（税款+每段税率已知）": 0.00010371077140071768, "补充知识点8728": 0.00010371077140071768, "求收入额-": 4.1484308560287074e-05, "求收入额(税率，税款已知)": 4.1484308560287074e-05, "补充知识点8711": 4.1484308560287074e-05, "数图结合解决负数的实际问题": 0.00010371077140071768, "数图结合，解决位置和距离问题--": 0.00010371077140071768, "补充知识点10081": 0.00010371077140071768, "补充知识点10082": 0.00010371077140071768, "补充知识点10083": 0.00010371077140071768, "含字母的公因数、最大公因数问题-": 8.296861712057415e-05, "求字母（知两数质因数乘积和最大公因数）-": 8.296861712057415e-05, "补充知识点1487": 8.296861712057415e-05, "从不同方向观察简单立体图形": 0.0001659372342411483, "从不同位置观察简单立体图形说理题": 0.0001659372342411483, "补充知识点17968": 0.0001659372342411483, "补充知识点17969": 0.0001659372342411483, "有关0的加减法": 0.00031113231420215305, "得数为0的减法": 0.00018667938852129183, "得数为0的计算": 0.00018667938852129183, "补充知识点3740": 0.00018667938852129183, "完全平方和": 6.222646284043061e-05, "完全平方和公式正向应用": 6.222646284043061e-05, "补充知识点15244": 6.222646284043061e-05, "三位数除以两位数的有余数除法": 8.296861712057415e-05, "辨别周期现象": 8.296861712057415e-05, "补充知识点5994": 8.296861712057415e-05, "分类枚举法数角(角的认识)": 0.00010371077140071768, "补充知识点25276": 0.00010371077140071768, "补充知识点25277": 0.00010371077140071768, "补充知识点25278": 0.00010371077140071768, "数平行线和垂线": 0.00018667938852129183, "数相互垂直的线段的数量": 0.00018667938852129183, "补充知识点25138": 0.00018667938852129183, "补充知识点25139": 0.00018667938852129183, "补充知识点25140": 0.00018667938852129183, "认识直角、锐角、钝角": 0.00010371077140071768, "生活中的直角、锐角、钝角": 0.00010371077140071768, "补充知识点15751": 0.00010371077140071768, "补充知识点15752": 0.00010371077140071768, "角度的简单计算--折叠": 0.0001659372342411483, "求非折叠角": 0.0001659372342411483, "补充知识点19986": 0.0001659372342411483, "补充知识点19987": 0.0001659372342411483, "补充知识点19988": 0.0001659372342411483, "百分数、分数、小数和比的互化": 0.00014519507996100476, "百分数、分数、小数、比之间的互化": 0.00014519507996100476, "计算后比大小": 0.00014519507996100476, "补充知识点11871": 0.00014519507996100476, "补充知识点11872": 0.00014519507996100476, "数形结合找规律": 8.296861712057415e-05, "两组图形数量不变重复(只填数)": 8.296861712057415e-05, "补充知识点15238": 8.296861712057415e-05, "多边形周长的简单计算": 6.222646284043061e-05, "三角形的周长": 6.222646284043061e-05, "补充知识点16025": 6.222646284043061e-05, "补充知识点16026": 6.222646284043061e-05, "商不变规律中余数的变化": 0.0001659372342411483, "被除数和除数同时缩小后求余数": 0.0001659372342411483, "补充知识点10380": 0.0001659372342411483, "补充知识点10381": 0.0001659372342411483, "补充知识点10382": 0.0001659372342411483, "含字母的分数与整数相乘的推理": 6.222646284043061e-05, "由所给算式计算规律，求分数中分子分母和-": 6.222646284043061e-05, "由实际/图上长度，求体积问题-": 4.1484308560287074e-05, "求物体实际体积（知三视图、比例、每格长）-": 4.1484308560287074e-05, "补充知识点12944": 4.1484308560287074e-05, "补充知识点12945": 4.1484308560287074e-05, "比例尺与百分数（复印问题）-": 0.00012445292568086122, "由所给百分数求比例尺（复印问题）-": 0.00012445292568086122, "补充知识点12790": 0.00012445292568086122, "补充知识点12791": 0.00012445292568086122, "折扣和成数综合": 6.222646284043061e-05, "同一件商品打折、让利的辨析-": 6.222646284043061e-05, "补充知识点8835": 6.222646284043061e-05, "补充知识点8836": 6.222646284043061e-05, "数1000以内的数": 2.0742154280143537e-05, "数数(数到1000)": 2.0742154280143537e-05, "综合数数(数到1000)": 2.0742154280143537e-05, "补充知识点589": 2.0742154280143537e-05, "简单的还原问题(一半还多/少)": 2.0742154280143537e-05, "一半还多": 2.0742154280143537e-05, "补充知识点5711": 2.0742154280143537e-05, "不等式横式谜（100以内加减法）": 6.222646284043061e-05, "填数使不等式成立": 6.222646284043061e-05, "补充知识点24793": 6.222646284043061e-05, "补充知识点24794": 6.222646284043061e-05, "补充知识点24795": 6.222646284043061e-05, "口算(小括号)": 0.0001659372342411483, "运算顺序": 0.0001659372342411483, "补充知识点9371": 0.0001659372342411483, "补充知识点9372": 0.0001659372342411483, "画平面图": 4.1484308560287074e-05, "根据某标志所在方向画学校的平面图": 4.1484308560287074e-05, "补充知识点21202": 4.1484308560287074e-05, "补充知识点21203": 4.1484308560287074e-05, "购物问题(2-6)": 0.00014519507996100476, "分别求出单价并比大小": 0.00014519507996100476, "补充知识点14293": 0.00014519507996100476, "补充知识点14294": 0.00014519507996100476, "火柴棒游戏": 2.0742154280143537e-05, "火柴棒游戏（20以内）": 2.0742154280143537e-05, "移动火柴棒使等式成立": 2.0742154280143537e-05, "补充知识点26271": 2.0742154280143537e-05, "补充知识点26272": 2.0742154280143537e-05, "补充知识点26273": 2.0742154280143537e-05, "分类枚举法数三角形": 4.1484308560287074e-05, "数三角形(单层)": 4.1484308560287074e-05, "补充知识点25102": 4.1484308560287074e-05, "补充知识点25103": 4.1484308560287074e-05, "补充知识点25104": 4.1484308560287074e-05, "统计表综合问题": 6.222646284043061e-05, "统计汉字解决综合问题": 6.222646284043061e-05, "补充知识点22614": 6.222646284043061e-05, "补充知识点22615": 6.222646284043061e-05, "补充知识点22616": 6.222646284043061e-05, "求促销中的折扣-": 0.00010371077140071768, "求折扣（买一送一按高价付款）": 0.00010371077140071768, "补充知识点8675": 0.00010371077140071768, "角度的简单计算--重叠": 0.00010371077140071768, "在长/正方形的顶点处重叠比大小": 0.00010371077140071768, "补充知识点19995": 0.00010371077140071768, "补充知识点19996": 0.00010371077140071768, "补充知识点19997": 0.00010371077140071768, "等差数列": 0.00035261662276244013, "数形结合正方形数": 0.0003318744684822966, "数形结合求1开始的连续奇数之和": 0.0003318744684822966, "补充知识点23859": 0.0003318744684822966, "补充知识点23860": 0.0003318744684822966, "补充知识点23861": 0.0003318744684822966, "四边形、五边形及多边形的初步认识": 0.00018667938852129183, "多边形的切分": 2.0742154280143537e-05, "多边形初认识": 2.0742154280143537e-05, "补充知识点15479": 2.0742154280143537e-05, "补充知识点15480": 2.0742154280143537e-05, "抛硬币问题": 0.0001659372342411483, "判断事件发生结果的可能性大小": 0.0001659372342411483, "补充知识点22956": 0.0001659372342411483, "补充知识点22957": 0.0001659372342411483, "补充知识点22958": 0.0001659372342411483, "分数乘整数的运用": 4.1484308560287074e-05, "分数乘整数进行单位换算": 4.1484308560287074e-05, "分组法解鸡兔同笼": 4.1484308560287074e-05, "分组法解决鸡兔同笼": 4.1484308560287074e-05, "已知腿和、头倍": 4.1484308560287074e-05, "补充知识点15043": 4.1484308560287074e-05, "解决几倍多几和少几的问题": 0.00010371077140071768, "列式计算": 0.00010371077140071768, "补充知识点1628": 0.00010371077140071768, "补充知识点1629": 0.00010371077140071768, "补充知识点1630": 0.00010371077140071768, "与倍相关的应用": 0.0001659372342411483, "等倍变化问题": 0.0001659372342411483, "补充知识点1649": 0.0001659372342411483, "补充知识点1650": 0.0001659372342411483, "补充知识点1651": 0.0001659372342411483, "移多补少(结合倍)": 6.222646284043061e-05, "移多补少变相等": 6.222646284043061e-05, "补充知识点23980": 6.222646284043061e-05, "补充知识点23981": 6.222646284043061e-05, "补充知识点23982": 6.222646284043061e-05, "绳长对折问题": 0.00014519507996100476, "已知对折次数和每段长度，求绳长": 0.00014519507996100476, "补充知识点20007": 0.00014519507996100476, "补充知识点20008": 0.00014519507996100476, "补充知识点20009": 0.00014519507996100476, "方格纸上问题综合-": 2.0742154280143537e-05, "在方格纸中画与指定图形面积相等的图形": 2.0742154280143537e-05, "补充知识点12900": 2.0742154280143537e-05, "补充知识点12901": 2.0742154280143537e-05, "运用分数约分比大小解决实际问题-": 6.222646284043061e-05, "约分后找相同（不计算）-": 6.222646284043061e-05, "补充知识点2666": 6.222646284043061e-05, "统计表的选择": 0.00010371077140071768, "选择哪一类统计表": 0.00010371077140071768, "补充知识点22300": 0.00010371077140071768, "补充知识点22301": 0.00010371077140071768, "读数和写数(1000以内)": 0.0003318744684822966, "看图读数写数": 0.0003318744684822966, "补充知识点605": 0.0003318744684822966, "算式比大小（十几减9）": 4.1484308560287074e-05, "先计算，再比较": 4.1484308560287074e-05, "补充知识点4139": 4.1484308560287074e-05, "填算符（除数是一位数的除法）": 2.0742154280143537e-05, "根据结果填算符": 2.0742154280143537e-05, "补充知识点24088": 2.0742154280143537e-05, "补充知识点24089": 2.0742154280143537e-05, "补充知识点24090": 2.0742154280143537e-05, "0的认识和理解": 2.0742154280143537e-05, "对0的认识": 2.0742154280143537e-05, "补充知识点3433": 2.0742154280143537e-05, "补充知识点3434": 2.0742154280143537e-05, "计数器表示数(10000以内)": 0.0001659372342411483, "在计数器上画珠子表示出所给数": 0.0001659372342411483, "补充知识点658": 0.0001659372342411483, "单式统计表": 4.1484308560287074e-05, "两张统计表的相同点": 4.1484308560287074e-05, "补充知识点22306": 4.1484308560287074e-05, "补充知识点22307": 4.1484308560287074e-05, "相交": 4.1484308560287074e-05, "判断是否相交": 4.1484308560287074e-05, "补充知识点15615": 4.1484308560287074e-05, "补充知识点15616": 4.1484308560287074e-05, "认识垂直": 0.00024890585136172244, "识别相互垂直的两条线": 0.00024890585136172244, "补充知识点15609": 0.00024890585136172244, "补充知识点15610": 0.00024890585136172244, "列方程解决实际问题x±a=b": 0.00018667938852129183, "补充知识点11393": 0.00018667938852129183, "补充知识点11394": 0.00018667938852129183, "补充知识点11395": 0.00018667938852129183, "谚语中的数学": 0.00018667938852129183, "补充知识点26256": 0.00018667938852129183, "补充知识点26257": 0.00018667938852129183, "补充知识点26258": 0.00018667938852129183, "补充知识点26259": 0.00018667938852129183, "摸硬币游戏": 2.0742154280143537e-05, "补充知识点26310": 2.0742154280143537e-05, "补充知识点26311": 2.0742154280143537e-05, "补充知识点26312": 2.0742154280143537e-05, "补充知识点26313": 2.0742154280143537e-05, "想加算减（十几减5、4、3、2）": 2.0742154280143537e-05, "补充知识点8943": 2.0742154280143537e-05, "补充知识点8944": 2.0742154280143537e-05, "平十法(十几减9)": 2.0742154280143537e-05, "拆分法理解平十法": 2.0742154280143537e-05, "补充知识点4115": 2.0742154280143537e-05, "解决扇形统计图圆心角的度数问题": 4.1484308560287074e-05, "先求百分比，再求圆心角度数": 4.1484308560287074e-05, "补充知识点22466": 4.1484308560287074e-05, "补充知识点22467": 4.1484308560287074e-05, "等底等高的三角形面积": 0.00024890585136172244, "等底等高的三角形的面积大小判断": 0.000269648005641866, "补充知识点17097": 0.00024890585136172244, "长、正方形周长互化": 0.00012445292568086122, "利用正方形周长求对应长方形长或宽": 0.00012445292568086122, "补充知识点16145": 0.00012445292568086122, "补充知识点16146": 0.00012445292568086122, "解决添运算符号的问题": 4.1484308560287074e-05, "加上小括号使等式成立": 4.1484308560287074e-05, "补充知识点24145": 4.1484308560287074e-05, "补充知识点24146": 4.1484308560287074e-05, "补充知识点24147": 4.1484308560287074e-05, "画图法解鸡兔同笼": 6.222646284043061e-05, "补充知识点15042": 2.0742154280143537e-05, "含两个对象等量代换求质量": 6.222646284043061e-05, "算式代换": 6.222646284043061e-05, "补充知识点10919": 6.222646284043061e-05, "补充知识点10920": 6.222646284043061e-05, "补充知识点10921": 6.222646284043061e-05, "四边形的特点": 0.0001659372342411483, "四边形的概念": 0.0001659372342411483, "补充知识点15473": 0.0001659372342411483, "补充知识点15474": 0.0001659372342411483, "五日游中路程时间速度问题-": 2.0742154280143537e-05, "由表格中的行程安排，求某时两地距离": 2.0742154280143537e-05, "补充知识点14373": 2.0742154280143537e-05, "分数连乘的实际应用-": 0.00012445292568086122, "连乘解决实际问题（含单位换算）": 0.00012445292568086122, "补充知识点8150": 0.00012445292568086122, "连乘运算应用": 2.0742154280143537e-05, "连乘分步意义": 2.0742154280143537e-05, "补充知识点6613": 2.0742154280143537e-05, "万以内加法竖式谜": 6.222646284043061e-05, "加法竖式谜(不连续进位)": 6.222646284043061e-05, "补充知识点24433": 6.222646284043061e-05, "补充知识点24434": 6.222646284043061e-05, "补充知识点24435": 6.222646284043061e-05, "搭配(2种物品)": 0.0001659372342411483, "4种以下搭配": 0.0001659372342411483, "补充知识点14659": 0.0001659372342411483, "补充知识点14660": 0.0001659372342411483, "用数与形探索规律-": 2.0742154280143537e-05, "点连成线段条数探索填空-": 2.0742154280143537e-05, "补充知识点15260": 2.0742154280143537e-05, "神奇的9": 0.00010371077140071768, "通过计算器探索9的规律": 0.00010371077140071768, "补充知识点10576": 0.00010371077140071768, "补充知识点10577": 0.00010371077140071768, "用小棒摆立体图形-": 2.0742154280143537e-05, "搭建长方体": 2.0742154280143537e-05, "补充知识点15266": 2.0742154280143537e-05, "可能性大小的计算-": 0.00012445292568086122, "用所给数字组数，求满足要求数的可能性-": 0.00012445292568086122, "补充知识点23109": 0.00012445292568086122, "补充知识点23110": 0.00012445292568086122, "补充知识点23111": 0.00012445292568086122, "圆周长的意义和测量方式": 4.1484308560287074e-05, "圆周长的概念": 4.1484308560287074e-05, "补充知识点17214": 4.1484308560287074e-05, "平行四边形计数": 6.222646284043061e-05, "根据定义数平行四边形": 6.222646284043061e-05, "补充知识点25132": 6.222646284043061e-05, "补充知识点25133": 6.222646284043061e-05, "补充知识点25134": 6.222646284043061e-05, "关于垂直的相关判断": 0.00014519507996100476, "判断对折后的折痕是否垂直": 0.00014519507996100476, "补充知识点15603": 0.00014519507996100476, "补充知识点15604": 0.00014519507996100476, "等量代换在算式计算和实际问题中的运用-": 4.1484308560287074e-05, "较为简单的等量代换-直接代换": 4.1484308560287074e-05, "补充知识点10883": 4.1484308560287074e-05, "补充知识点10884": 4.1484308560287074e-05, "补充知识点10885": 4.1484308560287074e-05, "运用集合的知识解决较复杂问题（有圈外部分）": 0.00014519507996100476, "根据韦恩图两量重叠有圈外人求总量": 0.00014519507996100476, "补充知识点14939": 0.00014519507996100476, "补充知识点14940": 0.00014519507996100476, "等差数列计算-": 2.0742154280143537e-05, "等差数列求和": 2.0742154280143537e-05, "补充知识点23892": 2.0742154280143537e-05, "补充知识点23893": 2.0742154280143537e-05, "补充知识点23894": 2.0742154280143537e-05, "相遇问题": 8.296861712057415e-05, "相遇的行程问题": 8.296861712057415e-05, "一只狗在两人之间来回跑的相遇问题": 8.296861712057415e-05, "补充知识点14347": 8.296861712057415e-05, "有趣的找规律": 4.1484308560287074e-05, "找规律综合": 4.1484308560287074e-05, "尾数周期规律(简单)": 4.1484308560287074e-05, "补充知识点26274": 4.1484308560287074e-05, "补充知识点26275": 4.1484308560287074e-05, "补充知识点26276": 4.1484308560287074e-05, "角的度量": 0.00031113231420215305, "量指定角的度数(一边未与0刻度线重合)": 0.00012445292568086122, "量指定角的度数(观察量角器)": 0.00012445292568086122, "补充知识点15787": 0.00012445292568086122, "补充知识点15788": 0.00012445292568086122, "比赛问题": 0.00018667938852129183, "体育比赛单循环赛的场数问题": 6.222646284043061e-05, "单循环赛计算比赛总场数": 6.222646284043061e-05, "补充知识点14775": 6.222646284043061e-05, "补充知识点14776": 6.222646284043061e-05, "数对、位置与简单行程问题": 4.1484308560287074e-05, "求速度（知行驶时间+前后位置+每格长度）": 4.1484308560287074e-05, "补充知识点21486": 4.1484308560287074e-05, "补充知识点21487": 4.1484308560287074e-05, "购物方案(表内)": 2.0742154280143537e-05, "正好够买问题": 2.0742154280143537e-05, "补充知识点14139": 2.0742154280143537e-05, "补充知识点14140": 2.0742154280143537e-05, "一个因数变化的规律": 0.0001659372342411483, "根据算式探索一个因数变化的规律": 0.0001659372342411483, "补充知识点10236": 0.0001659372342411483, "补充知识点10237": 0.0001659372342411483, "补充知识点10238": 0.0001659372342411483, "画指定长、宽（边长）的长方形、正方形": 0.00024890585136172244, "画长方形": 0.00014519507996100476, "根据要求直接画长方形": 0.00014519507996100476, "补充知识点15975": 0.00014519507996100476, "补充知识点15976": 0.00014519507996100476, "画正方形": 6.222646284043061e-05, "格子图中画正方形": 6.222646284043061e-05, "补充知识点15987": 6.222646284043061e-05, "补充知识点15988": 6.222646284043061e-05, "认识扇形统计图": 0.0001659372342411483, "扇形统计图的概念": 0.0001659372342411483, "补充知识点22474": 0.0001659372342411483, "补充知识点22475": 0.0001659372342411483, "看线段图列方程-": 2.0742154280143537e-05, "列ax=b型方程": 2.0742154280143537e-05, "补充知识点10995": 2.0742154280143537e-05, "补充知识点10996": 2.0742154280143537e-05, "与图形相关的按比分配问题": 4.1484308560287074e-05, "按比分配分割图形": 4.1484308560287074e-05, "补充知识点11921": 4.1484308560287074e-05, "补充知识点11922": 4.1484308560287074e-05, "错中求解（分数乘除）-": 2.0742154280143537e-05, "乘除号看错": 2.0742154280143537e-05, "补充知识点8279": 2.0742154280143537e-05, "最优化问题": 0.0003941009313227272, "过河问题": 0.0001659372342411483, "一人三牛过河问题": 0.0001659372342411483, "补充知识点26229": 0.0001659372342411483, "补充知识点26230": 0.0001659372342411483, "补充知识点26231": 0.0001659372342411483, "三位数乘两位数的估算": 0.000580780319844019, "直接估算结果": 0.0002903901599220095, "补充知识点7117": 0.0002903901599220095, "分数化百分数": 4.1484308560287074e-05, "分数化百分数的方法": 4.1484308560287074e-05, "补充知识点3373": 4.1484308560287074e-05, "补充知识点3374": 4.1484308560287074e-05, "由题中数据探究比例关系（反比例）-": 4.1484308560287074e-05, "补充知识点12536": 4.1484308560287074e-05, "补充知识点12537": 4.1484308560287074e-05, "简单的天平代换": 8.296861712057415e-05, "三种物品间的代换": 8.296861712057415e-05, "补充知识点10937": 8.296861712057415e-05, "补充知识点10938": 8.296861712057415e-05, "补充知识点10939": 8.296861712057415e-05, "整数小数分数化简比": 0.00010371077140071768, "含有整数、小数的化简比": 0.00010371077140071768, "补充知识点11819": 0.00010371077140071768, "补充知识点11820": 0.00010371077140071768, "用字母表示含倍数关系的量9": 0.0001659372342411483, "和倍关系": 0.0001659372342411483, "补充知识点10780": 0.0001659372342411483, "补充知识点10781": 0.0001659372342411483, "知比原价便宜的钱数，求原价-": 2.0742154280143537e-05, "知原价和现价，另一件便宜钱数，求另一件原价": 2.0742154280143537e-05, "补充知识点8667": 2.0742154280143537e-05, "解决三位数乘两位数(因数中无0)的实际问题": 0.00010371077140071768, "已知每份数、份数，求总数": 0.00010371077140071768, "补充知识点6679": 0.00010371077140071768, "小数的速算与巧算": 4.1484308560287074e-05, "小数乘除简算": 4.1484308560287074e-05, "添括号改变运算符号的小数乘除简算": 4.1484308560287074e-05, "补充知识点23578": 4.1484308560287074e-05, "补充知识点23579": 4.1484308560287074e-05, "补充知识点23580": 4.1484308560287074e-05, "列方程解决班级人数变化问题-": 2.0742154280143537e-05, "求男/女生人数问题（全班人数不知+知转来男/女人数）-": 2.0742154280143537e-05, "补充知识点11657": 2.0742154280143537e-05, "补充知识点11658": 2.0742154280143537e-05, "补充知识点11659": 2.0742154280143537e-05, "列方程解决稍复杂实际问题-": 2.0742154280143537e-05, "求两书架分别取走书的本数（知两书架原来本数+书架剩下书的关系）-": 2.0742154280143537e-05, "补充知识点11660": 2.0742154280143537e-05, "补充知识点11661": 2.0742154280143537e-05, "补充知识点11662": 2.0742154280143537e-05, "方程法解鸡兔同笼": 0.00010371077140071768, "列方程解决鸡兔同笼问题\"\"": 0.00010371077140071768, "解决鸡兔同笼问题变形(主角变换)": 0.00010371077140071768, "补充知识点15027": 0.00010371077140071768, "工程中的百分数问题": 2.0742154280143537e-05, "求原来加工N个零件的时间，现在可加工多少个（改进效率）": 2.0742154280143537e-05, "补充知识点8881": 2.0742154280143537e-05, "补充知识点8882": 2.0742154280143537e-05, "圆锥展开图与表面积": 2.0742154280143537e-05, "从长方形中截图做圆锥，求剩余部分面积-": 2.0742154280143537e-05, "补充知识点18820": 2.0742154280143537e-05, "补充知识点18821": 2.0742154280143537e-05, "解决三位数乘两位数(因数中间有0)实际问题": 0.0001659372342411483, "补充知识点6661": 0.0001659372342411483, "分段整理数据": 4.1484308560287074e-05, "运用分段整理数据解决实际问题": 4.1484308560287074e-05, "直接根据分段统计表解决简单问题": 4.1484308560287074e-05, "补充知识点22806": 4.1484308560287074e-05, "补充知识点22807": 4.1484308560287074e-05, "补充知识点22808": 4.1484308560287074e-05, "平行四边形面积和周长的运用（求底/高/边长/周长/面积）": 8.296861712057415e-05, "利用部分周长和面积，计算底或高": 8.296861712057415e-05, "补充知识点16583": 8.296861712057415e-05, "圆中方问题（一圆一方）-": 2.0742154280143537e-05, "求圆、正方形面积的比/倍数": 2.0742154280143537e-05, "补充知识点17459": 2.0742154280143537e-05, "补充知识点17460": 2.0742154280143537e-05, "含扇形的组合图形中阴影部分周长/面积-": 2.0742154280143537e-05, "方中扇形，求扇形外阴影部分周长、面积（知正方形边长）": 2.0742154280143537e-05, "补充知识点17560": 2.0742154280143537e-05, "补充知识点17561": 2.0742154280143537e-05, "方格图中的数对找位置-": 4.1484308560287074e-05, "在方格图中找出数对表示的点": 4.1484308560287074e-05, "补充知识点21492": 4.1484308560287074e-05, "补充知识点21493": 4.1484308560287074e-05, "流水行船问题": 2.0742154280143537e-05, "求顺逆水行船的平均速度（知顺水速度+顺逆时间差+两地距离）": 2.0742154280143537e-05, "补充知识点14368": 2.0742154280143537e-05, "用量角器画角": 0.0002903901599220095, "用量角器画角的步骤": 0.00014519507996100476, "补充知识点15877": 0.00014519507996100476, "补充知识点15878": 0.00014519507996100476, "量指定角的度数(一边与0刻度线重合)": 6.222646284043061e-05, "用量角器量角(看外圈)": 6.222646284043061e-05, "补充知识点15791": 6.222646284043061e-05, "补充知识点15792": 6.222646284043061e-05, "分数问题中求总量-": 4.1484308560287074e-05, "已知部分量占总量的分率，求总量-": 4.1484308560287074e-05, "求总量（知第一部分数量+第二占总量分率）": 4.1484308560287074e-05, "补充知识点8329": 4.1484308560287074e-05, "百分数化分数": 8.296861712057415e-05, "补充知识点3357": 4.1484308560287074e-05, "补充知识点3358": 4.1484308560287074e-05, "小数加减混合简算": 8.296861712057415e-05, "加数写成整数-0.1/0.01形式的简便计算": 8.296861712057415e-05, "补充知识点9731": 8.296861712057415e-05, "长方形的剪拼": 4.1484308560287074e-05, "把大长方形剪成多个小长方形（无剩余）": 4.1484308560287074e-05, "补充知识点19900": 4.1484308560287074e-05, "补充知识点19901": 4.1484308560287074e-05, "补充知识点19902": 4.1484308560287074e-05, "巧填算符(不含中括号)": 0.00014519507996100476, "在合适的地方巧填算符": 0.00014519507996100476, "补充知识点24133": 0.00014519507996100476, "补充知识点24134": 0.00014519507996100476, "补充知识点24135": 0.00014519507996100476, "由积木上数字绘制几何体的平面图形（三）": 2.0742154280143537e-05, "由积木数字和某面平面图，画出另一面的平面图-": 2.0742154280143537e-05, "补充知识点18072": 2.0742154280143537e-05, "补充知识点18073": 2.0742154280143537e-05, "组合体中添/去掉小正方体的摆法或位置问题（三）": 4.1484308560287074e-05, "由两个方向看到的平面图，判断所添小正方体摆放的位置": 4.1484308560287074e-05, "补充知识点18107": 4.1484308560287074e-05, "千克的认识": 4.1484308560287074e-05, "认识千克": 4.1484308560287074e-05, "生活中以千克为单位的物体": 4.1484308560287074e-05, "补充知识点13573": 4.1484308560287074e-05, "只能算一个除法算式的口诀(2-6)": 4.1484308560287074e-05, "用同一句乘法口诀计算": 4.1484308560287074e-05, "补充知识点5634": 4.1484308560287074e-05, "同分母分数减法的应用": 0.00010371077140071768, "看图列同分母分数减法算式并计算": 0.00010371077140071768, "补充知识点7891": 0.00010371077140071768, "同分母分数加法的应用": 0.0004978117027234449, "补充知识点7888": 0.00024890585136172244, "看图比较同分母分数大小": 0.00020742154280143537, "几分之一的大小比较": 0.00020742154280143537, "两个分子为1分数比大小": 0.00020742154280143537, "补充知识点2595": 0.00020742154280143537, "长或宽改变求周长": 8.296861712057415e-05, "长方形长变化求周长": 8.296861712057415e-05, "补充知识点16111": 8.296861712057415e-05, "补充知识点16112": 8.296861712057415e-05, "正方形的概念及特点": 2.0742154280143537e-05, "认识正方形": 2.0742154280143537e-05, "正方形辨析": 2.0742154280143537e-05, "补充知识点15965": 2.0742154280143537e-05, "补充知识点15966": 2.0742154280143537e-05, "长方形的概念及特点": 0.00020742154280143537, "认识长方形": 0.00020742154280143537, "长方形的概念与特点": 0.00020742154280143537, "补充知识点15957": 0.00020742154280143537, "补充知识点15958": 0.00020742154280143537, "解决整倍的和倍问题进阶": 0.00020742154280143537, "补充知识点25812": 0.00020742154280143537, "补充知识点25813": 0.00020742154280143537, "补充知识点25814": 0.00020742154280143537, "三位数末尾有0(不进位)的乘法笔算": 2.0742154280143537e-05, "三位数末尾有0(不进位)的计算算理": 2.0742154280143537e-05, "补充知识点6166": 2.0742154280143537e-05, "加减混合连加连减(20以内进位)": 4.1484308560287074e-05, "连加": 4.1484308560287074e-05, "补充知识点4349": 4.1484308560287074e-05, "横式谜(20以内进位加)": 4.1484308560287074e-05, "填符号": 4.1484308560287074e-05, "补充知识点24781": 4.1484308560287074e-05, "补充知识点24782": 4.1484308560287074e-05, "补充知识点24783": 4.1484308560287074e-05, "间隔问题": 0.00020742154280143537, "排队问题单主角第\"\"第\"问题(20以内)\"": 6.222646284043061e-05, "第\"\"第\"问题求一共(20以内)\"": 6.222646284043061e-05, "补充知识点26003": 6.222646284043061e-05, "补充知识点26004": 6.222646284043061e-05, "移多补少(10以内)": 4.1484308560287074e-05, "比较多少": 4.1484308560287074e-05, "补充知识点23989": 4.1484308560287074e-05, "补充知识点23990": 4.1484308560287074e-05, "补充知识点23991": 4.1484308560287074e-05, "5的乘法口诀及应用": 0.0011408184854078945, "2-5的乘法应用题": 0.0001659372342411483, "看图列乘法算式并用口诀求结果(2-5)": 0.0001659372342411483, "补充知识点5202": 0.0001659372342411483, "2-5的乘加乘减应用题": 0.0003941009313227272, "看图列式(2-5乘加、乘减)（移后删）": 0.0003941009313227272, "补充知识点5403": 0.0003941009313227272, "7的乘法口诀（移后删）": 0.00041484308560287074, "根据文字描述计算(7乘几)": 0.00041484308560287074, "补充知识点5305": 0.00041484308560287074, "2、3、4的乘法口诀及应用-": 0.0010578498682873203, "2-4乘法口诀的应用题-": 0.0007259753998050237, "用乘法口诀解决问题(2-4)": 0.0007259753998050237, "补充知识点5236": 0.0007259753998050237, "计算经过时间(几时几分)": 0.00010371077140071768, "经过时间(数格法)": 0.00010371077140071768, "补充知识点13157": 0.00010371077140071768, "选择合适的时间单位(时、分)": 4.1484308560287074e-05, "补充知识点13346": 2.0742154280143537e-05, "补充知识点13347": 2.0742154280143537e-05, "时、分、秒时间的推算": 0.00037335877704258366, "时间推理(几时几分)": 0.00037335877704258366, "已知时间，判断此时在做什么(要删)": 0.00037335877704258366, "补充知识点13255": 0.00037335877704258366, "画钟表(几时几分)": 0.00010371077140071768, "根据时间画钟表": 0.00010371077140071768, "补充知识点12994": 0.00010371077140071768, "7的乘加、乘减计算": 0.00012445292568086122, "补充知识点5431": 0.00012445292568086122, "不退位/退位减法的计算运用": 4.1484308560287074e-05, "口算(不退位/退位)": 6.222646284043061e-05, "补充知识点4759": 4.1484308560287074e-05, "6的乘法口诀的理解（一）（移后删）": 0.00018667938852129183, "根据积选择算式": 0.00018667938852129183, "补充知识点5264": 0.00018667938852129183, "2-6乘法口诀及应用-": 0.00012445292568086122, "2-6的乘法口诀的理解（一）（移后删）": 6.222646284043061e-05, "根据乘积选算式-": 6.222646284043061e-05, "补充知识点5493": 6.222646284043061e-05, "三位数加两、三位数(连续进位)的应用": 8.296861712057415e-05, "补充知识点5036": 8.296861712057415e-05, "利用被减数、减数、差的关系解决问题": 2.0742154280143537e-05, "差相等，减数越大，被减数越大": 2.0742154280143537e-05, "补充知识点5090": 2.0742154280143537e-05, "数字编码问题": 0.00014519507996100476, "其它编码": 0.00010371077140071768, "小区编码内容提取": 0.00010371077140071768, "补充知识点14379": 0.00010371077140071768, "补充知识点14380": 0.00010371077140071768, "万以内的加法和减法错中求解": 2.0742154280143537e-05, "错中求解(万以内的减法看错被减数)": 2.0742154280143537e-05, "补充知识点23778": 2.0742154280143537e-05, "补充知识点23779": 2.0742154280143537e-05, "补充知识点23780": 2.0742154280143537e-05, "根据两数的分率关系求比": 4.1484308560287074e-05, "根据倍数关系求比": 4.1484308560287074e-05, "补充知识点12089": 4.1484308560287074e-05, "补充知识点12090": 4.1484308560287074e-05, "数表中的规律": 0.00014519507996100476, "数表中规律": 0.00014519507996100476, "根据数表规律填空": 0.00014519507996100476, "补充知识点23433": 0.00014519507996100476, "补充知识点23434": 0.00014519507996100476, "补充知识点23435": 0.00014519507996100476, "补充知识点23436": 0.00014519507996100476, "乘加、乘减(表内)": 2.0742154280143537e-05, "表内乘法的乘加运算顺序": 2.0742154280143537e-05, "补充知识点5397": 2.0742154280143537e-05, "从不同位置看几何体": 4.1484308560287074e-05, "观察三个几何体": 4.1484308560287074e-05, "补充知识点17938": 4.1484308560287074e-05, "补充知识点17939": 4.1484308560287074e-05, "画钟表": 2.0742154280143537e-05, "选择正确的钟表": 2.0742154280143537e-05, "补充知识点12989": 2.0742154280143537e-05, "乘法口诀表": 6.222646284043061e-05, "根据乘法口诀计算": 6.222646284043061e-05, "补充知识点5463": 6.222646284043061e-05, "9的乘法应用题（移后删）": 0.0004563273941631578, "9的乘法的意义应用问题": 0.0004563273941631578, "补充知识点5362": 0.0004563273941631578, "同数连加的应用": 0.0004563273941631578, "加法算式改写为乘法算式": 0.0003941009313227272, "算式改写（加改乘）": 0.0003941009313227272, "补充知识点5131": 0.0003941009313227272, "浓度的认识": 2.0742154280143537e-05, "由溶质和溶剂求溶质占溶液的百分之几": 2.0742154280143537e-05, "补充知识点26182": 2.0742154280143537e-05, "补充知识点26183": 2.0742154280143537e-05, "补充知识点26184": 2.0742154280143537e-05, "正比例与反比例": 2.0742154280143537e-05, "判断成正、反比例": 2.0742154280143537e-05, "补充知识点12608": 2.0742154280143537e-05, "补充知识点12609": 2.0742154280143537e-05, "行程折线图": 0.00018667938852129183, "(单人)涉及时间的折线图": 0.00018667938852129183, "补充知识点22848": 0.00018667938852129183, "补充知识点22849": 0.00018667938852129183, "补充知识点22850": 0.00018667938852129183, "三角板拼角的计算": 0.00010371077140071768, "计算一副三角板拼成的角度": 0.00010371077140071768, "补充知识点15925": 0.00010371077140071768, "补充知识点15926": 0.00010371077140071768, "数的顺序(1-5)": 6.222646284043061e-05, "按顺序连线": 6.222646284043061e-05, "补充知识点210": 6.222646284043061e-05, "双主角排队问题": 0.00010371077140071768, "求两人中间有几人(有遮挡)": 0.00010371077140071768, "补充知识点26021": 0.00010371077140071768, "补充知识点26022": 0.00010371077140071768, "排队问题单主角有\"\"有\"问题（20以内）\"": 4.1484308560287074e-05, "有\"\"有\"问题求一共的方法(20以内)\"": 4.1484308560287074e-05, "补充知识点26007": 4.1484308560287074e-05, "补充知识点26008": 4.1484308560287074e-05, "9的乘加乘减应用题": 0.0004978117027234449, "补充知识点5413": 0.00024890585136172244, "初步认识乘法意义": 0.0007882018626454544, "初步认识乘法的意义（一）（移后删）": 0.0002903901599220095, "看图填空": 0.00031113231420215305, "补充知识点5149": 0.0002903901599220095, "连续两问综合应用题": 2.0742154280143537e-05, "连续两步加法应用题": 2.0742154280143537e-05, "补充知识点4892": 2.0742154280143537e-05, "2-6的乘法应用题": 4.1484308560287074e-05, "看图列乘法算式(2-6)": 4.1484308560287074e-05, "补充知识点5477": 4.1484308560287074e-05, "早起上学前的准备工作": 0.0004563273941631578, "补充知识点14961": 0.0004563273941631578, "用韦恩图表示集合": 0.0001659372342411483, "理解韦恩图的意义": 0.0001659372342411483, "补充知识点14929": 0.0001659372342411483, "补充知识点14930": 0.0001659372342411483, "身份证编码": 2.0742154280143537e-05, "了解身份证号能代表的意义": 2.0742154280143537e-05, "补充知识点14405": 2.0742154280143537e-05, "补充知识点14406": 2.0742154280143537e-05, "用所给数字组最简分数-": 2.0742154280143537e-05, "求给数字组成的最简分数/个数-": 2.0742154280143537e-05, "补充知识点3147": 2.0742154280143537e-05, "补充知识点3148": 2.0742154280143537e-05, "认识复式条形统计图": 2.0742154280143537e-05, "认识复式/横向复式条形统计图": 2.0742154280143537e-05, "补充知识点22392": 2.0742154280143537e-05, "补充知识点22393": 2.0742154280143537e-05, "有关0的乘法": 8.296861712057415e-05, "0的相关乘法计算": 8.296861712057415e-05, "与0有关的乘法计算": 8.296861712057415e-05, "补充知识点6148": 8.296861712057415e-05, "根据乘法口诀求商(6-9)（移后删）": 2.0742154280143537e-05, "算一算": 2.0742154280143537e-05, "补充知识点5717": 2.0742154280143537e-05, "两位数加减混合运算应用题": 6.222646284043061e-05, "求比一个数多或少的数是多少": 6.222646284043061e-05, "补充知识点5123": 6.222646284043061e-05, "两位数与两位数的不进位/进位加的口算运用": 2.0742154280143537e-05, "判断十位上的数字": 2.0742154280143537e-05, "补充知识点4997": 2.0742154280143537e-05, "植树问题（一端栽一端不栽）": 0.0001659372342411483, "植树问题(一端种，一端不种)": 0.0001659372342411483, "求棵数": 0.0001659372342411483, "补充知识点15105": 0.0001659372342411483, "5的乘法口诀应用题": 0.00041484308560287074, "看图列乘法算式并用口诀求结果(5乘几)": 0.00041484308560287074, "补充知识点5181": 0.00041484308560287074, "6的乘加乘减应用题": 0.00014519507996100476, "看图列式(6的乘加、乘减)": 0.00014519507996100476, "补充知识点5451": 0.00014519507996100476, "小数运算解决错中求解问题（除数是整数）": 4.1484308560287074e-05, "补充知识点7650": 4.1484308560287074e-05, "表与时间": 2.0742154280143537e-05, "看表写时间（几时几分几秒）": 2.0742154280143537e-05, "补充知识点13021": 2.0742154280143537e-05, "根据文字列乘法算式(表内乘法)": 0.0001659372342411483, "根据文字列乘法算式": 0.0001659372342411483, "补充知识点5164": 0.0001659372342411483, "杨辉三角": 8.296861712057415e-05, "根据上一行求下一行数(解决简单的计算)": 8.296861712057415e-05, "补充知识点15267": 8.296861712057415e-05, "运用梯形面积公式解决实际问题": 0.00012445292568086122, "运用梯形面积计算平均分配问题": 0.00012445292568086122, "补充知识点16720": 0.00012445292568086122, "象形统计图和统计表": 2.0742154280143537e-05, "按多种标准整理数据": 2.0742154280143537e-05, "补充知识点22170": 2.0742154280143537e-05, "补充知识点22171": 2.0742154280143537e-05, "补充知识点22172": 2.0742154280143537e-05, "单循环赛求总人数或总分数": 6.222646284043061e-05, "补充知识点14765": 6.222646284043061e-05, "补充知识点14766": 6.222646284043061e-05, "根据数据完成统计表和统计图": 4.1484308560287074e-05, "通过数据完成统计表": 4.1484308560287074e-05, "补充知识点22647": 4.1484308560287074e-05, "补充知识点22648": 4.1484308560287074e-05, "补充知识点22649": 4.1484308560287074e-05, "排队问题--求最短时间": 0.0001659372342411483, "一个队伍四个人": 0.0001659372342411483, "补充知识点26247": 0.0001659372342411483, "补充知识点26248": 0.0001659372342411483, "补充知识点26249": 0.0001659372342411483, "报数问题的必胜策略": 6.222646284043061e-05, "无余数": 6.222646284043061e-05, "补充知识点26223": 6.222646284043061e-05, "补充知识点26224": 6.222646284043061e-05, "补充知识点26225": 6.222646284043061e-05, "百分数的读法和写法": 4.1484308560287074e-05, "百分数的写法": 2.0742154280143537e-05, "补充知识点3327": 2.0742154280143537e-05, "补充知识点3328": 2.0742154280143537e-05, "圆周长在生活中的实际应用": 6.222646284043061e-05, "通过辨析条件利用圆周长公式解题": 6.222646284043061e-05, "补充知识点17281": 6.222646284043061e-05, "时钟问题中面积的计算": 2.0742154280143537e-05, "根据经过时间求指针扫过的面积": 2.0742154280143537e-05, "补充知识点17540": 2.0742154280143537e-05, "补充知识点17541": 2.0742154280143537e-05, "用字母表示速度、时间、路程间的关系": 0.00024890585136172244, "含字母的式子表示路程": 0.00024890585136172244, "补充知识点10736": 0.00024890585136172244, "补充知识点10737": 0.00024890585136172244, "用字母表示稍复杂的数量关系": 0.00012445292568086122, "数线图与字母9": 2.0742154280143537e-05, "根据数线图中所标字母的位置比较算式的大小9": 2.0742154280143537e-05, "补充知识点10840": 2.0742154280143537e-05, "补充知识点10841": 2.0742154280143537e-05, "2-5的乘法口诀": 0.00024890585136172244, "根据2-5的乘法口诀计算": 0.00024890585136172244, "补充知识点5206": 0.00024890585136172244, "2-4的乘法口诀理解（一）（移后删）": 0.0003318744684822966, "根据2-4乘法口诀计算": 0.0003318744684822966, "补充知识点5247": 0.0003318744684822966, "5的乘法口诀理解（一）（移后删）": 0.00031113231420215305, "乘法与加法关系(5乘几)": 0.00031113231420215305, "补充知识点5186": 0.00031113231420215305, "排水法求物体体积（多个物体）-": 2.0742154280143537e-05, "求其中一个物体体积（大小不同+知排出水的体积）-": 2.0742154280143537e-05, "补充知识点19632": 2.0742154280143537e-05, "补充知识点19633": 2.0742154280143537e-05, "复杂情况计算比例尺-": 2.0742154280143537e-05, "罗盘仪中数据计算比例尺-": 2.0742154280143537e-05, "补充知识点12788": 2.0742154280143537e-05, "补充知识点12789": 2.0742154280143537e-05, "运用整体减空白法求面积": 0.00018667938852129183, "方格/格点纸上的整体面积减去空白面积，求剩余面积": 0.00018667938852129183, "补充知识点17863": 0.00018667938852129183, "补充知识点17864": 0.00018667938852129183, "补充知识点17865": 0.00018667938852129183, "数十万以内的数": 2.0742154280143537e-05, "十万的认识-组成": 2.0742154280143537e-05, "理解某一数位上数表示的意义": 2.0742154280143537e-05, "补充知识点708": 2.0742154280143537e-05, "求利润差（知成本+原售件数+原利润率+定价变化+售出件数变化）": 2.0742154280143537e-05, "补充知识点8827": 2.0742154280143537e-05, "补充知识点8828": 2.0742154280143537e-05, "已知成本和售价，求利润率": 2.0742154280143537e-05, "已知成本和利润，直接求利润率": 2.0742154280143537e-05, "补充知识点8803": 2.0742154280143537e-05, "补充知识点8804": 2.0742154280143537e-05, "求溶液问题中的比-": 2.0742154280143537e-05, "求溶质与溶液的比（需先计算出量）-": 2.0742154280143537e-05, "补充知识点12111": 2.0742154280143537e-05, "补充知识点12112": 2.0742154280143537e-05, "组数与排列(2-3个对象)": 0.00041484308560287074, "组两位数(3个对象其中一个数为0)": 0.00041484308560287074, "补充知识点14559": 0.00041484308560287074, "补充知识点14560": 0.00041484308560287074, "取币值": 0.00010371077140071768, "取固定张数": 0.00010371077140071768, "补充知识点14683": 0.00010371077140071768, "补充知识点14684": 0.00010371077140071768, "起跑线问题": 2.0742154280143537e-05, "求不同跑道的周长": 2.0742154280143537e-05, "补充知识点17258": 2.0742154280143537e-05, "时钟问题中弧长的计算": 2.0742154280143537e-05, "秒针针尖走过的弧长": 2.0742154280143537e-05, "补充知识点17500": 2.0742154280143537e-05, "补充知识点17501": 2.0742154280143537e-05, "扇形圆心角": 2.0742154280143537e-05, "已知弧长和所在圆周长关系，求圆心角": 2.0742154280143537e-05, "补充知识点17556": 2.0742154280143537e-05, "补充知识点17557": 2.0742154280143537e-05, "组合图形中的面积问题综合": 0.0001659372342411483, "利用组合图形面积计算总量问题": 0.0001659372342411483, "补充知识点17626": 0.0001659372342411483, "补充知识点17627": 0.0001659372342411483, "补充知识点17628": 0.0001659372342411483, "三角形中的等积变形": 2.0742154280143537e-05, "补充知识点17124": 2.0742154280143537e-05, "利用差不变求组合图形的面积": 2.0742154280143537e-05, "利用差不变求简单组合图形的面积问题": 2.0742154280143537e-05, "补充知识点17650": 2.0742154280143537e-05, "补充知识点17651": 2.0742154280143537e-05, "补充知识点17652": 2.0742154280143537e-05, "敲钟问题": 8.296861712057415e-05, "求敲钟所需时间": 8.296861712057415e-05, "补充知识点15143": 8.296861712057415e-05, "用字母关系式总结数字、图形变化规律9": 0.00010371077140071768, "根据数列变化找规律": 0.00010371077140071768, "补充知识点10830": 0.00010371077140071768, "补充知识点10831": 0.00010371077140071768, "列方程解决简单的追及问题\"\"": 4.1484308560287074e-05, "求某车速度（知运行时间内相差距离+其中一车速度）-": 4.1484308560287074e-05, "补充知识点11591": 4.1484308560287074e-05, "补充知识点11592": 4.1484308560287074e-05, "补充知识点11593": 4.1484308560287074e-05, "加法应用题(不进位加)(100以内)": 8.296861712057415e-05, "补充知识点4535": 8.296861712057415e-05, "巧填算符(100以内)": 2.0742154280143537e-05, "补充知识点24169": 2.0742154280143537e-05, "补充知识点24170": 2.0742154280143537e-05, "补充知识点24171": 2.0742154280143537e-05, "时间比较(几时几分)": 8.296861712057415e-05, "补充知识点13055": 8.296861712057415e-05, "认识分": 2.0742154280143537e-05, "认识钟面上的分针": 2.0742154280143537e-05, "补充知识点13027": 2.0742154280143537e-05, "梯形计数": 6.222646284043061e-05, "包含直角梯形的分类枚举": 6.222646284043061e-05, "补充知识点25210": 6.222646284043061e-05, "补充知识点25211": 6.222646284043061e-05, "补充知识点25212": 6.222646284043061e-05, "平行四边形的高及画法": 0.00020742154280143537, "平行四边形的底和高": 0.00020742154280143537, "认识平行四边形底的高": 0.00020742154280143537, "补充知识点16507": 0.00020742154280143537, "补充知识点16508": 0.00020742154280143537, "平行四边形的认识": 0.00014519507996100476, "补充知识点16485": 0.00014519507996100476, "补充知识点16486": 0.00014519507996100476, "平面图形的分割": 0.00010371077140071768, "给定条件分割四边形的问题": 0.00010371077140071768, "分成一个平行四边形和一个梯形（删）": 0.00010371077140071768, "补充知识点19924": 0.00010371077140071768, "补充知识点19925": 0.00010371077140071768, "补充知识点19926": 0.00010371077140071768, "梯形的概念及特点": 0.00018667938852129183, "梯形的概念": 0.0001659372342411483, "梯形的认识": 0.0001659372342411483, "补充知识点16601": 0.0001659372342411483, "补充知识点16602": 0.0001659372342411483, "看错内外圈刻度问题": 8.296861712057415e-05, "辨析看错內外圈时的正确读数": 8.296861712057415e-05, "补充知识点15781": 8.296861712057415e-05, "补充知识点15782": 8.296861712057415e-05, "平行四边形的不稳定性及应用": 4.1484308560287074e-05, "平行四边形具有不稳定性": 4.1484308560287074e-05, "平行四边形的不稳定性在生活中的应用": 4.1484308560287074e-05, "补充知识点16515": 4.1484308560287074e-05, "补充知识点16516": 4.1484308560287074e-05, "两个因数都变化的规律的应用(积变化的规律)": 4.1484308560287074e-05, "根据积变化的规律填合适的符号及数": 4.1484308560287074e-05, "补充知识点10224": 4.1484308560287074e-05, "补充知识点10225": 4.1484308560287074e-05, "补充知识点10226": 4.1484308560287074e-05, "判断商是几位数（除数是两位数）": 0.0003318744684822966, "判断商是几位数(除数是两位数)": 0.0003318744684822966, "商是两位数的实际应用": 0.0003318744684822966, "补充知识点6775": 0.0003318744684822966, "多位数除以两位数的试商": 0.0005185538570035884, "除数不接近整十数的试商": 0.00012445292568086122, "判断竖式对错": 0.00012445292568086122, "补充知识点6780": 0.00012445292568086122, "判断积的末尾0的个数(三位数乘两位数)": 4.1484308560287074e-05, "积的末尾0的个数的直接判断": 4.1484308560287074e-05, "补充知识点6634": 4.1484308560287074e-05, "多个大数比较大小(亿以内)": 4.1484308560287074e-05, "多个亿以内的数比大小": 4.1484308560287074e-05, "补充知识点876": 4.1484308560287074e-05, "三位数减两、三位数(连续退位)的应用": 4.1484308560287074e-05, "基础解决问题": 4.1484308560287074e-05, "补充知识点5066": 4.1484308560287074e-05, "描述其他物品的长度和高度": 6.222646284043061e-05, "选择合适的测量方式": 6.222646284043061e-05, "补充知识点21638": 6.222646284043061e-05, "补充知识点21639": 6.222646284043061e-05, "用乘法的意义改写算式(7-9)": 4.1484308560287074e-05, "算式改写(有减法)": 4.1484308560287074e-05, "补充知识点5144": 4.1484308560287074e-05, "表内乘法计算（1-9)-": 4.1484308560287074e-05, "补全等式中所缺数-": 4.1484308560287074e-05, "补充知识点5377": 4.1484308560287074e-05, "表内乘加、乘减计算": 2.0742154280143537e-05, "补充知识点5453": 2.0742154280143537e-05, "表内乘法应用题（1-9）-": 8.296861712057415e-05, "看图列乘法算式并用口诀求结果(表内乘法)": 8.296861712057415e-05, "补充知识点5372": 8.296861712057415e-05, "表内乘加乘减应用题": 0.00018667938852129183, "补充知识点5408": 0.00018667938852129183, "积的特征(9的乘法)（移后删）": 4.1484308560287074e-05, "补充知识点5359": 4.1484308560287074e-05, "8的乘加、乘减计算": 2.0742154280143537e-05, "补充知识点5438": 2.0742154280143537e-05, "图文算式(表内)": 2.0742154280143537e-05, "算式中只有一种图形": 2.0742154280143537e-05, "补充知识点14005": 2.0742154280143537e-05, "补充知识点14006": 2.0742154280143537e-05, "相同长方体拼搭": 4.1484308560287074e-05, "拼大长方体": 4.1484308560287074e-05, "补充知识点20063": 4.1484308560287074e-05, "补充知识点20064": 4.1484308560287074e-05, "圆的面积计算": 2.0742154280143537e-05, "利用圆的面积公式直接计算(生活情景)": 2.0742154280143537e-05, "补充知识点17306": 2.0742154280143537e-05, "从不同方向观察简单物体": 8.296861712057415e-05, "根据观察者选图片": 8.296861712057415e-05, "补充知识点17948": 8.296861712057415e-05, "补充知识点17949": 8.296861712057415e-05, "根据看到的立体图形的一个面推测立体图形的形状": 8.296861712057415e-05, "根据观察到的平面图形(长方形)来猜测立体图形": 8.296861712057415e-05, "补充知识点18125": 8.296861712057415e-05, "加法、乘法求总数问题的对比-": 0.00024890585136172244, "数形结合--加法、乘法数学问题的比较": 0.00024890585136172244, "补充知识点5278": 0.00024890585136172244, "图文算式(2-6)": 6.222646284043061e-05, "其中一个算式只有一种图形": 8.296861712057415e-05, "补充知识点13897": 6.222646284043061e-05, "补充知识点13898": 6.222646284043061e-05, "用乘法的意义改写算式(2-6)": 2.0742154280143537e-05, "算式改写(加数都相同)": 2.0742154280143537e-05, "补充知识点5140": 2.0742154280143537e-05, "有趣的乘法(表内2-6)": 2.0742154280143537e-05, "棋子摆正方形问题": 2.0742154280143537e-05, "补充知识点5488": 2.0742154280143537e-05, "填算符(加减乘2-6)": 2.0742154280143537e-05, "填算符(+、-、×)": 2.0742154280143537e-05, "补充知识点24193": 2.0742154280143537e-05, "补充知识点24194": 2.0742154280143537e-05, "补充知识点24195": 2.0742154280143537e-05, "6的乘加、乘减计算": 4.1484308560287074e-05, "补充知识点5449": 4.1484308560287074e-05, "2-5的乘加、乘减计算": 0.00012445292568086122, "乘加乘减算式中的大小": 0.00012445292568086122, "补充知识点5420": 0.00012445292568086122, "图文算式(2-5)": 2.0742154280143537e-05, "补充知识点13887": 2.0742154280143537e-05, "补充知识点13888": 2.0742154280143537e-05, "乘法算式的读法及各部分名称": 8.296861712057415e-05, "乘法算式的读法": 8.296861712057415e-05, "补充知识点5160": 8.296861712057415e-05, "看图写乘法算式(表内乘法)": 0.00024890585136172244, "看图列乘法算式": 0.00024890585136172244, "补充知识点5168": 0.00024890585136172244, "植树问题之爬楼梯问题": 6.222646284043061e-05, "求经过的层数": 6.222646284043061e-05, "补充知识点15155": 6.222646284043061e-05, "植树问题（两端都不栽）": 6.222646284043061e-05, "植树问题(两端都不种)": 6.222646284043061e-05, "两侧种树求棵数": 6.222646284043061e-05, "补充知识点15099": 6.222646284043061e-05, "运用平行四边形面积公式解决实际问题（一）": 6.222646284043061e-05, "运用平行四边形面积计算平均分配问题": 6.222646284043061e-05, "补充知识点16588": 6.222646284043061e-05, "算式比大小(10以内)": 4.1484308560287074e-05, "简单计算比大小(5以内)": 4.1484308560287074e-05, "补充知识点24274": 4.1484308560287074e-05, "补充知识点24275": 4.1484308560287074e-05, "补充知识点24276": 4.1484308560287074e-05, "封闭型数阵图(1-10)": 8.296861712057415e-05, "已知线和求未知数(一条线)": 8.296861712057415e-05, "补充知识点24865": 8.296861712057415e-05, "补充知识点24866": 8.296861712057415e-05, "补充知识点24867": 8.296861712057415e-05, "两位数减两位数的口算(不退位)": 4.1484308560287074e-05, "探究口算方法，理解算理(不退位减)": 4.1484308560287074e-05, "补充知识点4982": 4.1484308560287074e-05, "两位数加两位数的口算(不进位)": 0.00010371077140071768, "口算(不进位加)": 0.00010371077140071768, "补充知识点4969": 0.00010371077140071768, "两位数加减的口算运用": 4.1484308560287074e-05, "口算(加减)": 4.1484308560287074e-05, "补充知识点5002": 4.1484308560287074e-05, "两位数加减的口算应用题": 4.1484308560287074e-05, "填表格应用题": 4.1484308560287074e-05, "补充知识点5007": 4.1484308560287074e-05, "两位数与两位数不退位/退位减的口算运用": 2.0742154280143537e-05, "补充知识点4999": 2.0742154280143537e-05, "不等化等(1-5)": 8.296861712057415e-05, "添加或减少变相等": 8.296861712057415e-05, "补充知识点23968": 8.296861712057415e-05, "补充知识点23969": 8.296861712057415e-05, "补充知识点23970": 8.296861712057415e-05, "减法计算(1-5)": 8.296861712057415e-05, "补充知识点3711": 8.296861712057415e-05, "5以内数的分与合": 0.0001659372342411483, "1-5的分与合": 4.1484308560287074e-05, "数的简单分与合": 4.1484308560287074e-05, "补充知识点3634": 4.1484308560287074e-05, "减法计算的实际应用(1-5)": 8.296861712057415e-05, "看图写算式": 8.296861712057415e-05, "补充知识点3724": 8.296861712057415e-05, "1~5加法的含义": 2.0742154280143537e-05, "补充知识点3660": 2.0742154280143537e-05, "1-5的分与合解决实际问题": 0.00012445292568086122, "一共有几种分法": 0.00012445292568086122, "补充知识点3623": 0.00012445292568086122, "单价、数量、总价的数量关系": 0.00014519507996100476, "根据题意先写数量关系再列式": 0.00014519507996100476, "补充知识点14177": 0.00014519507996100476, "补充知识点14178": 0.00014519507996100476, "统计与概率": 2.0742154280143537e-05, "平均数与数据分析": 2.0742154280143537e-05, "平均数简单应用": 2.0742154280143537e-05, "补充知识点25435": 2.0742154280143537e-05, "补充知识点25436": 2.0742154280143537e-05, "补充知识点25437": 2.0742154280143537e-05, "加法横式谜(1-5)": 2.0742154280143537e-05, "补充知识点3645": 2.0742154280143537e-05, "算式中有0的加减法": 0.00012445292568086122, "看图列式（移题删）": 0.00012445292568086122, "补充知识点3744": 0.00012445292568086122, "0的读、写法": 6.222646284043061e-05, "0的认识": 6.222646284043061e-05, "0的读写": 6.222646284043061e-05, "补充知识点227": 6.222646284043061e-05, "平行四边形的特征": 0.00012445292568086122, "平行四边形角的特点": 0.00012445292568086122, "补充知识点16489": 0.00012445292568086122, "补充知识点16490": 0.00012445292568086122, "除数是整十数的笔算除法": 0.000269648005641866, "用三位数除以整十数解决简单的实际问题": 0.00014519507996100476, "利用除数是整十数的除法解决问题": 0.00014519507996100476, "补充知识点6732": 0.00014519507996100476, "口算几百几十数除以整十数": 8.296861712057415e-05, "直接计算出结果": 8.296861712057415e-05, "补充知识点6690": 8.296861712057415e-05, "除数接近整十数的笔算方法(用四舍\"法试商)\"": 0.00012445292568086122, "把除数看作几试商合适": 0.00012445292568086122, "补充知识点6791": 0.00012445292568086122, "试商问题综合(商是一位数)": 0.00010371077140071768, "除数个位是4或6试商的简便方法": 0.00010371077140071768, "补充知识点6803": 0.00010371077140071768, "积不变的规律的应用": 0.00018667938852129183, "根据积不变的规律计算": 0.00018667938852129183, "补充知识点10206": 0.00018667938852129183, "补充知识点10207": 0.00018667938852129183, "补充知识点10208": 0.00018667938852129183, "平角、周角的认识及特征": 8.296861712057415e-05, "角的认识的应用(直角、锐角、钝角、平角和周角)": 4.1484308560287074e-05, "判断两个或两个以上锐角可能拼成的角": 4.1484308560287074e-05, "补充知识点15853": 4.1484308560287074e-05, "补充知识点15854": 4.1484308560287074e-05, "求比一个数多/少几分之几的部分": 2.0742154280143537e-05, "画线段图求比一个数多/少几分之几的部分": 2.0742154280143537e-05, "补充知识点8225": 2.0742154280143537e-05, "购物中的满减问题": 4.1484308560287074e-05, "解决单个满减问题": 4.1484308560287074e-05, "补充知识点14217": 4.1484308560287074e-05, "补充知识点14218": 4.1484308560287074e-05, "积不变的规律": 4.1484308560287074e-05, "积不变时求另一个因数如何变化": 4.1484308560287074e-05, "补充知识点10194": 4.1484308560287074e-05, "补充知识点10195": 4.1484308560287074e-05, "补充知识点10196": 4.1484308560287074e-05, "认识直角梯形": 2.0742154280143537e-05, "直角梯形的认识": 2.0742154280143537e-05, "补充知识点16627": 2.0742154280143537e-05, "补充知识点16628": 2.0742154280143537e-05, "设计编码": 2.0742154280143537e-05, "编码设计": 2.0742154280143537e-05, "补充知识点14403": 2.0742154280143537e-05, "补充知识点14404": 2.0742154280143537e-05, "两位数除两位数的估算方法": 8.296861712057415e-05, "将除数四舍五入后进行估算": 8.296861712057415e-05, "补充知识点7150": 8.296861712057415e-05, "算盘的应用(大数)": 6.222646284043061e-05, "读出算盘上的数(删)": 6.222646284043061e-05, "补充知识点10449": 6.222646284043061e-05, "补充知识点10450": 6.222646284043061e-05, "补充知识点10451": 6.222646284043061e-05, "改写与近似数": 2.0742154280143537e-05, "整数的改写和近似数": 2.0742154280143537e-05, "补充知识点2443": 2.0742154280143537e-05, "用逆推法解决小数乘整数应用题": 4.1484308560287074e-05, "用两次小数乘整数解决倒推问题": 4.1484308560287074e-05, "补充知识点7362": 4.1484308560287074e-05, "三位数除以整十数商是一位数的笔算除法": 4.1484308560287074e-05, "整十数除三位数，商是一位数的笔算(能整除)": 4.1484308560287074e-05, "补充知识点6727": 4.1484308560287074e-05, "两位数除以整十数的笔算除法": 8.296861712057415e-05, "整十数除两位数的笔算": 8.296861712057415e-05, "补充知识点6714": 8.296861712057415e-05, "运用分割法求组合图形的面积": 2.0742154280143537e-05, "分割法计算组合图形的面积初步": 2.0742154280143537e-05, "补充知识点17656": 2.0742154280143537e-05, "补充知识点17657": 2.0742154280143537e-05, "补充知识点17658": 2.0742154280143537e-05, "认识平角": 4.1484308560287074e-05, "平角的辨析": 4.1484308560287074e-05, "补充知识点15839": 4.1484308560287074e-05, "补充知识点15840": 4.1484308560287074e-05, "小数化百分数": 4.1484308560287074e-05, "补充知识点3379": 2.0742154280143537e-05, "补充知识点3380": 2.0742154280143537e-05, "根据溶质和浓度求溶液": 2.0742154280143537e-05, "根据溶剂和浓度求溶液": 2.0742154280143537e-05, "补充知识点26161": 2.0742154280143537e-05, "补充知识点26162": 2.0742154280143537e-05, "补充知识点26163": 2.0742154280143537e-05, "卡片求和问题": 4.1484308560287074e-05, "卡片组数个数与大小、和差": 4.1484308560287074e-05, "补充知识点23085": 4.1484308560287074e-05, "补充知识点23086": 4.1484308560287074e-05, "补充知识点23087": 4.1484308560287074e-05, "用字母表示运算律": 8.296861712057415e-05, "补充知识点10812": 4.1484308560287074e-05, "补充知识点10813": 4.1484308560287074e-05, "2-6的乘加乘减应用题": 2.0742154280143537e-05, "看图根据问题，选择正确列式": 2.0742154280143537e-05, "补充知识点5427": 2.0742154280143537e-05, "比多少的计算运用(100以内)": 2.0742154280143537e-05, "求比一个数多/少几的数是多少": 2.0742154280143537e-05, "补充知识点4763": 2.0742154280143537e-05, "横式谜(11-20)": 2.0742154280143537e-05, "横式谜(基础)": 2.0742154280143537e-05, "补充知识点24667": 2.0742154280143537e-05, "补充知识点24668": 2.0742154280143537e-05, "补充知识点24669": 2.0742154280143537e-05, "图文算式(20以内)": 2.0742154280143537e-05, "一步计算图文算式": 2.0742154280143537e-05, "补充知识点13995": 2.0742154280143537e-05, "补充知识点13996": 2.0742154280143537e-05, "5~2加几的进位加法": 8.296861712057415e-05, "5、4、3、2加几": 8.296861712057415e-05, "补充知识点4309": 8.296861712057415e-05, "提出问题并列算式(20以内进位)": 2.0742154280143537e-05, "提出问题初步": 2.0742154280143537e-05, "补充知识点13849": 2.0742154280143537e-05, "补充知识点13850": 2.0742154280143537e-05, "认识加减法各部分名称": 2.0742154280143537e-05, "根据算式对应某部分名称": 2.0742154280143537e-05, "补充知识点9057": 2.0742154280143537e-05, "补充知识点9058": 2.0742154280143537e-05, "加减法一元推算(20以内)": 2.0742154280143537e-05, "补充知识点24721": 2.0742154280143537e-05, "补充知识点24722": 2.0742154280143537e-05, "补充知识点24723": 2.0742154280143537e-05, "11-20各数的认识": 4.1484308560287074e-05, "几个几个数": 4.1484308560287074e-05, "补充知识点346": 4.1484308560287074e-05, "集合概念": 6.222646284043061e-05, "有表集合回答问题": 6.222646284043061e-05, "补充知识点14915": 6.222646284043061e-05, "补充知识点14916": 6.222646284043061e-05, "排队问题单主第\"\"第\"问题(10以内)\"": 2.0742154280143537e-05, "第\"、\"有\"复习\"": 2.0742154280143537e-05, "补充知识点3952": 2.0742154280143537e-05, "利用分数墙计算同分母分数加减法": 2.0742154280143537e-05, "利用分数墙计算同分母加法": 2.0742154280143537e-05, "补充知识点8048": 2.0742154280143537e-05, "同分母分数连减的计算方法": 2.0742154280143537e-05, "同分母分数的连减计算": 2.0742154280143537e-05, "补充知识点8011": 2.0742154280143537e-05, "钟表中的几分之几": 4.1484308560287074e-05, "判断图形数量占整体几分之几0": 4.1484308560287074e-05, "补充知识点2537": 4.1484308560287074e-05, "除数接近整十数的笔算方法(用五入\"法试商)\"": 8.296861712057415e-05, "判断商偏大或偏小": 8.296861712057415e-05, "补充知识点6797": 8.296861712057415e-05, "数四边形": 2.0742154280143537e-05, "数四边形个数": 2.0742154280143537e-05, "补充知识点25162": 2.0742154280143537e-05, "补充知识点25163": 2.0742154280143537e-05, "补充知识点25164": 2.0742154280143537e-05, "图示法解决问题": 2.0742154280143537e-05, "求总数": 2.0742154280143537e-05, "补充知识点25752": 2.0742154280143537e-05, "补充知识点25753": 2.0742154280143537e-05, "补充知识点25754": 2.0742154280143537e-05, "植树问题之量体温问题-": 2.0742154280143537e-05, "求第N次量体温的时间-": 2.0742154280143537e-05, "补充知识点15116": 2.0742154280143537e-05, "三位数中间有0(不进位)的乘法笔算": 4.1484308560287074e-05, "三位数中间有0(不进位)的算理": 4.1484308560287074e-05, "补充知识点6172": 4.1484308560287074e-05, "三位数乘一位数(不进位)的笔算的应用": 2.0742154280143537e-05, "找结果（不）相等的算式": 2.0742154280143537e-05, "补充知识点6066": 2.0742154280143537e-05, "从条件出发解决倍数问题": 2.0742154280143537e-05, "有关倍的看图列式计算应用题": 2.0742154280143537e-05, "补充知识点1056": 2.0742154280143537e-05, "加减法的速算与巧算": 0.00012445292568086122, "三位数减三位数的巧算": 8.296861712057415e-05, "减去同尾数": 8.296861712057415e-05, "补充知识点23503": 8.296861712057415e-05, "补充知识点23504": 8.296861712057415e-05, "补充知识点23505": 8.296861712057415e-05, "三位数减两、三位数(不退位)的应用": 2.0742154280143537e-05, "看图表解决问题": 2.0742154280143537e-05, "补充知识点5046": 2.0742154280143537e-05, "三位数减两、三位数(连续退位，中间有0)的计算": 2.0742154280143537e-05, "补充知识点5069": 2.0742154280143537e-05, "三位数加三位数的巧算": 4.1484308560287074e-05, "分组凑整": 4.1484308560287074e-05, "补充知识点23491": 4.1484308560287074e-05, "补充知识点23492": 4.1484308560287074e-05, "补充知识点23493": 4.1484308560287074e-05, "除数接近整十数的笔算除法解决问题(用四舍\"法试商)\"": 6.222646284043061e-05, "四舍\"法试商解决问题\"": 6.222646284043061e-05, "补充知识点6784": 6.222646284043061e-05, "三位数乘两位数积的大小比较": 2.0742154280143537e-05, "三位数乘两位数积的比大小": 2.0742154280143537e-05, "补充知识点24271": 2.0742154280143537e-05, "补充知识点24272": 2.0742154280143537e-05, "补充知识点24273": 2.0742154280143537e-05, "以一当二的条形统计图": 0.00010371077140071768, "确定每格表示的数量": 0.00010371077140071768, "补充知识点22358": 0.00010371077140071768, "补充知识点22359": 0.00010371077140071768, "除数接近整十数的笔算除法解决问题(用五入\"法试商)\"": 2.0742154280143537e-05, "除数个位大于5且商有余数": 2.0742154280143537e-05, "补充知识点6787": 2.0742154280143537e-05, "口算整十数除以整十数": 8.296861712057415e-05, "用两种方法口算整十数除以整十数": 8.296861712057415e-05, "补充知识点6707": 8.296861712057415e-05, "口算整百数除以整十数": 4.1484308560287074e-05, "利用数的组成计算": 4.1484308560287074e-05, "补充知识点6703": 4.1484308560287074e-05, "除数是两位数除法的错中求解问题": 8.296861712057415e-05, "除数没有看错": 8.296861712057415e-05, "补充知识点23694": 8.296861712057415e-05, "补充知识点23695": 8.296861712057415e-05, "补充知识点23696": 8.296861712057415e-05, "运用商不变的规律解决实际问题": 2.0742154280143537e-05, "根据商不变的规律判断是否相同": 2.0742154280143537e-05, "补充知识点10368": 2.0742154280143537e-05, "补充知识点10369": 2.0742154280143537e-05, "补充知识点10370": 2.0742154280143537e-05, "最值问题": 2.0742154280143537e-05, "组数求积的最大值或最小值(含0)(三位数乘两位数)": 2.0742154280143537e-05, "组数求积的最值问题综合(只写算式)": 2.0742154280143537e-05, "补充知识点26301": 2.0742154280143537e-05, "补充知识点26302": 2.0742154280143537e-05, "补充知识点26303": 2.0742154280143537e-05, "梯形的上底、下底和高": 2.0742154280143537e-05, "对梯形的上底和下底的理解": 2.0742154280143537e-05, "补充知识点16605": 2.0742154280143537e-05, "补充知识点16606": 2.0742154280143537e-05, "运用画垂线和平行线的方法画长方形": 4.1484308560287074e-05, "描述步骤": 4.1484308560287074e-05, "补充知识点15997": 4.1484308560287074e-05, "补充知识点15998": 4.1484308560287074e-05, "认识平行": 4.1484308560287074e-05, "识别互相平行的两条线": 4.1484308560287074e-05, "补充知识点15655": 4.1484308560287074e-05, "补充知识点15656": 4.1484308560287074e-05, "用除数是整十数的除法解决简单的实际问题": 4.1484308560287074e-05, "直接得到结果的应用题(涉及单位换算)": 4.1484308560287074e-05, "补充知识点6700": 4.1484308560287074e-05, "单价、数量、总价的定义及含义": 2.0742154280143537e-05, "单价、数量、总价的含义": 2.0742154280143537e-05, "补充知识点14169": 2.0742154280143537e-05, "补充知识点14170": 2.0742154280143537e-05, "根据公式求单价": 4.1484308560287074e-05, "补充知识点14187": 2.0742154280143537e-05, "补充知识点14188": 2.0742154280143537e-05, "认识量角器": 4.1484308560287074e-05, "量角器两圈刻度的辨析": 4.1484308560287074e-05, "补充知识点15807": 4.1484308560287074e-05, "补充知识点15808": 4.1484308560287074e-05, "根据三角尺计算角度": 4.1484308560287074e-05, "补充知识点15909": 2.0742154280143537e-05, "补充知识点15910": 2.0742154280143537e-05, "数线段-解决实际问题": 2.0742154280143537e-05, "往返": 2.0742154280143537e-05, "补充知识点25174": 2.0742154280143537e-05, "补充知识点25175": 2.0742154280143537e-05, "补充知识点25176": 2.0742154280143537e-05, "求洒水、收割的面积与单位换算": 6.222646284043061e-05, "补充知识点16277": 6.222646284043061e-05, "补充知识点16278": 6.222646284043061e-05, "排列组合": 2.0742154280143537e-05, "用规定数字组数填数": 2.0742154280143537e-05, "用规定数字组数使等式成立": 2.0742154280143537e-05, "补充知识点25400": 2.0742154280143537e-05, "补充知识点25401": 2.0742154280143537e-05, "补充知识点25402": 2.0742154280143537e-05, "两、三位数的连减运算": 2.0742154280143537e-05, "三位数连减应用": 2.0742154280143537e-05, "连减计算解决问题": 2.0742154280143537e-05, "补充知识点5110": 2.0742154280143537e-05, "认识计数单位十万、百万、千万、亿": 4.1484308560287074e-05, "相邻计数单位的进率": 4.1484308560287074e-05, "补充知识点799": 4.1484308560287074e-05, "用三角板画角": 2.0742154280143537e-05, "用一副三角板可画出的角度": 2.0742154280143537e-05, "补充知识点15931": 2.0742154280143537e-05, "补充知识点15932": 2.0742154280143537e-05, "单人工程问题": 6.222646284043061e-05, "求工作效率": 6.222646284043061e-05, "补充知识点15375": 6.222646284043061e-05, "补充知识点15376": 6.222646284043061e-05, "面积单位比较大小的实际应用": 2.0742154280143537e-05, "面积单位比大小的应用": 2.0742154280143537e-05, "补充知识点13827": 2.0742154280143537e-05, "补充知识点13828": 2.0742154280143537e-05, "补充知识点13829": 2.0742154280143537e-05, "自选长度单位的测量": 2.0742154280143537e-05, "补充知识点13695": 2.0742154280143537e-05, "补充知识点13696": 2.0742154280143537e-05, "补充知识点13697": 2.0742154280143537e-05, "有关算式的找规律问题": 2.0742154280143537e-05, "两位数乘一位数算式找规律": 2.0742154280143537e-05, "补充知识点23168": 2.0742154280143537e-05, "补充知识点23169": 2.0742154280143537e-05, "补充知识点23170": 2.0742154280143537e-05}