# Math1 数据集使用说明

## 数据集概述

Math1 数据集包含小学数学应用题，用于训练和测试极端多标签分类任务。数据集分为三个部分：
- 训练集: 48,211 条记录
- 验证集: 5,073 条记录
- 测试集: 6,602 条记录

每条记录包含一个数学应用题（doc_token）和相关的数学概念标签（doc_label）。

## 数据格式

数据以JSON格式存储，每个文件包含一个数组，数组中的每个元素是一个对象，具有以下结构：

```json
{
  "doc_token": "数学问题的文本内容...",
  "doc_label": [
    "标签1",
    "标签2",
    "标签3"
    // ... 更多标签
  ]
}
```

## 准备工作

在使用Math1数据集之前，需要生成两个额外的文件：
1. `math1_priors.json` - 包含每个标签的先验概率
2. `math1_labels.txt` - 包含所有唯一标签，每行一个

这些文件可以通过运行以下脚本自动生成：

```bash
python scripts/prepare_math1_data.py
```

## 使用方法

### 1. 准备数据文件

```bash
python scripts/prepare_math1_data.py
```

### 2. 训练模型

使用自定义的Qwen模型训练Math1数据集：

```bash
bash scripts/compile_math1.sh
```

### 3. 评估模型

训练完成后，可以使用以下命令评估模型性能：

```bash
python run_irera.py \
    --dataset_name math1 \
    --state_path ./results/math1_infer-retrieve-rank_00/program_state.json \
    --do_validation \
    --do_test \
    --lm_config_path ./lm_config.json
```

## 数据集特点

- 每个问题可能有多个相关标签（平均约5-10个标签）
- 标签体系层次化，从一般到具体（例如："小学数学新知识树" -> "数与代数" -> "应用题"）
- 涵盖小学数学的各个方面，包括但不限于：
  - 数与代数
  - 几何与图形
  - 统计与概率
  - 应用题类型（如找次品问题）

## 注意事项

1. 由于数据集标签具有层次结构，在评估时可能需要考虑层级匹配
2. 数据集中的文本可能包含LaTeX数学表达式
3. 标签数量较多，属于典型的极端多标签分类问题